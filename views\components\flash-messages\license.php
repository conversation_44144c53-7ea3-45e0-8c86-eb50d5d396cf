<?php
/**
 * 🔐 ULTRA-SIMPLIFIED FLASH MESSAGES
 * License Activation System
 * 
 * ⭐ FEATURES:
 * - Simple license key activation
 * - Premium feature unlocking
 * - Trial management (30 days)
 * - Offline validation after initial activation
 * - Easy integration with one function call
 * 
 * @version 3.0.0 Ultra-Simplified Edition
 * <AUTHOR> Development Team
 */

// Include the main flash messages system
require_once __DIR__ . '/flash-messages.php';

// Handle license activation
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'activate_license':
            $licenseKey = trim($_POST['license_key'] ?? '');
            $result = activateLicense($licenseKey);
            
            if ($result['success']) {
                flash_success($result['message'], 'License Activated!', ['enableConfetti' => true]);
            } else {
                flash_error($result['message'], 'Activation Failed');
            }
            break;
            
        case 'deactivate_license':
            deactivateLicense();
            flash_info('License has been deactivated.', 'License Deactivated');
            break;
            
        case 'check_status':
            $status = getLicenseStatus();
            flash_info("License Status: " . ucfirst($status['status']), 'License Status');
            break;
            
        case 'extend_trial':
            $result = extendTrial();
            if ($result['success']) {
                flash_success($result['message'], 'Trial Extended!');
            } else {
                flash_warning($result['message'], 'Trial Extension');
            }
            break;
    }
    
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}

/**
 * 🔐 Activate License Key
 */
function activateLicense($licenseKey) {
    // Valid license keys (in production, this would check against a server)
    $validLicenses = [
        'JOBSPACE-PREMIUM-2024' => [
            'type' => 'premium',
            'expires' => '2025-12-31',
            'features' => ['all_themes', 'advanced_effects', 'priority_support']
        ],
        'ULTIMATE-FLASH-PRO' => [
            'type' => 'premium',
            'expires' => '2025-12-31',
            'features' => ['all_themes', 'advanced_effects', 'priority_support']
        ],
        'DEMO-LICENSE-KEY' => [
            'type' => 'demo',
            'expires' => '2024-12-31',
            'features' => ['basic_themes']
        ]
    ];
    
    if (empty($licenseKey)) {
        return ['success' => false, 'message' => 'Please enter a license key.'];
    }
    
    if (isset($validLicenses[$licenseKey])) {
        $license = $validLicenses[$licenseKey];
        
        // Check if license is expired
        if (strtotime($license['expires']) < time()) {
            return ['success' => false, 'message' => 'License key has expired.'];
        }
        
        // Save license to session and file
        $_SESSION['flash_license'] = [
            'key' => $licenseKey,
            'type' => $license['type'],
            'expires' => $license['expires'],
            'features' => $license['features'],
            'activated_at' => time()
        ];
        
        // Update config
        FlashConfig::$licenseKey = $licenseKey;
        FlashConfig::$isPremium = ($license['type'] === 'premium');
        
        // Save to file for persistence
        saveLicenseToFile($_SESSION['flash_license']);
        
        return [
            'success' => true, 
            'message' => 'License activated successfully! Premium features unlocked.'
        ];
    }
    
    return ['success' => false, 'message' => 'Invalid license key. Please check and try again.'];
}

/**
 * 🗑️ Deactivate License
 */
function deactivateLicense() {
    unset($_SESSION['flash_license']);
    FlashConfig::$licenseKey = '';
    FlashConfig::$isPremium = false;
    
    // Remove license file
    $licenseFile = __DIR__ . '/.license';
    if (file_exists($licenseFile)) {
        @unlink($licenseFile);
    }
}

/**
 * 📊 Get License Status
 */
function getLicenseStatus() {
    // Load license from session or file
    $license = $_SESSION['flash_license'] ?? loadLicenseFromFile();
    
    if (!$license) {
        return [
            'status' => 'free',
            'trial_days_left' => FlashConfig::$trialDaysLeft,
            'features' => ['basic_themes', 'core_functions']
        ];
    }
    
    // Check if expired
    if (strtotime($license['expires']) < time()) {
        return [
            'status' => 'expired',
            'expired_date' => $license['expires'],
            'features' => ['basic_themes', 'core_functions']
        ];
    }
    
    return [
        'status' => $license['type'],
        'expires' => $license['expires'],
        'features' => $license['features'],
        'activated_at' => $license['activated_at']
    ];
}

/**
 * ⏰ Extend Trial Period
 */
function extendTrial() {
    // Check if trial can be extended (only once)
    if (isset($_SESSION['trial_extended'])) {
        return ['success' => false, 'message' => 'Trial can only be extended once.'];
    }
    
    // Extend trial by 7 days
    $_SESSION['flash_trial_start'] = time() - (23 * 24 * 60 * 60); // Reset to 7 days ago
    $_SESSION['trial_extended'] = true;
    FlashConfig::$trialDaysLeft = 7;
    
    return ['success' => true, 'message' => 'Trial extended by 7 days!'];
}

/**
 * 💾 Save License to File
 */
function saveLicenseToFile($license) {
    $licenseFile = __DIR__ . '/.license';
    $data = base64_encode(json_encode($license));
    @file_put_contents($licenseFile, $data);
}

/**
 * 📖 Load License from File
 */
function loadLicenseFromFile() {
    $licenseFile = __DIR__ . '/.license';
    if (file_exists($licenseFile)) {
        $data = @file_get_contents($licenseFile);
        if ($data) {
            $license = json_decode(base64_decode($data), true);
            if ($license && is_array($license)) {
                return $license;
            }
        }
    }
    return null;
}

// Load existing license on page load
$currentLicense = getLicenseStatus();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 Flash Messages - License Activation</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: system-ui, sans-serif;
        }
        
        .license-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .license-card:hover {
            transform: translateY(-5px);
        }
        
        .license-button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 12px 24px;
            border-radius: 10px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            margin: 5px;
            display: inline-block;
        }
        
        .license-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .premium-badge {
            background: linear-gradient(45deg, #f59e0b, #d97706);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .free-badge {
            background: linear-gradient(45deg, #6b7280, #4b5563);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .trial-badge {
            background: linear-gradient(45f, #3b82f6, #1d4ed8);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0;
            text-align: center;
            margin-bottom: 3rem;
        }
    </style>
</head>
<body>

    <!-- Hero Section -->
    <div class="hero-section">
        <div class="container mx-auto px-4">
            <h1 class="text-5xl font-bold mb-4">🔐 License Activation</h1>
            <p class="text-xl mb-6">Unlock Premium Features & Advanced Themes</p>
            <div class="text-lg">
                Current Status: 
                <?php if ($currentLicense['status'] === 'premium'): ?>
                    <span class="premium-badge">Premium Active</span>
                <?php elseif ($currentLicense['status'] === 'free'): ?>
                    <span class="free-badge">Free Version</span>
                <?php else: ?>
                    <span class="trial-badge">Trial Mode</span>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-4 pb-8">
        
        <!-- License Activation -->
        <div class="license-card">
            <h2 class="text-3xl font-bold mb-4">🔑 Activate License</h2>
            
            <?php if ($currentLicense['status'] === 'premium'): ?>
                <div class="p-4 bg-green-50 rounded-lg mb-6">
                    <h3 class="font-bold text-green-800 mb-2">✅ Premium License Active</h3>
                    <p class="text-green-600">License Key: <?= FlashConfig::$licenseKey ?></p>
                    <p class="text-green-600">Expires: <?= $currentLicense['expires'] ?></p>
                    <p class="text-green-600">All premium features unlocked!</p>
                </div>
                
                <form method="POST" class="inline">
                    <input type="hidden" name="action" value="deactivate_license">
                    <button type="submit" class="license-button bg-red-500">Deactivate License</button>
                </form>
                
            <?php else: ?>
                <form method="POST" class="mb-6">
                    <input type="hidden" name="action" value="activate_license">
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">
                            License Key
                        </label>
                        <input type="text" name="license_key" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                               placeholder="Enter your license key"
                               value="">
                    </div>
                    <button type="submit" class="license-button">Activate License</button>
                </form>
                
                <div class="p-4 bg-blue-50 rounded-lg mb-6">
                    <h4 class="font-bold text-blue-800 mb-2">🎯 Demo License Keys</h4>
                    <p class="text-blue-600 text-sm mb-2">Try these demo keys:</p>
                    <ul class="text-blue-600 text-sm">
                        <li><code class="bg-blue-100 px-2 py-1 rounded">JOBSPACE-PREMIUM-2024</code> - Full Premium</li>
                        <li><code class="bg-blue-100 px-2 py-1 rounded">ULTIMATE-FLASH-PRO</code> - Full Premium</li>
                        <li><code class="bg-blue-100 px-2 py-1 rounded">DEMO-LICENSE-KEY</code> - Demo Version</li>
                    </ul>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Feature Comparison -->
        <div class="license-card">
            <h2 class="text-3xl font-bold mb-6">⭐ Feature Comparison</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                
                <!-- Free Version -->
                <div class="border border-gray-200 rounded-lg p-6">
                    <h3 class="text-xl font-bold mb-4">🆓 Free Version</h3>
                    <div class="free-badge mb-4">Always Free</div>
                    
                    <ul class="feature-list">
                        <li>✅ 500+ Core Functions</li>
                        <li>✅ Basic Themes (Modern, Minimal)</li>
                        <li>✅ Smart Auto-Detection</li>
                        <li>✅ Mobile Responsive</li>
                        <li>✅ Touch Gestures</li>
                        <li>✅ Basic Animations</li>
                        <li>❌ Premium Themes</li>
                        <li>❌ Advanced Effects</li>
                        <li>❌ Priority Support</li>
                    </ul>
                </div>
                
                <!-- Trial Version -->
                <div class="border border-blue-200 rounded-lg p-6 bg-blue-50">
                    <h3 class="text-xl font-bold mb-4">🎯 Trial Version</h3>
                    <div class="trial-badge mb-4">30 Days Free</div>
                    
                    <ul class="feature-list">
                        <li>✅ Everything in Free</li>
                        <li>✅ All Premium Themes</li>
                        <li>✅ Advanced Effects</li>
                        <li>✅ Confetti & Particles</li>
                        <li>✅ Glassmorphism Theme</li>
                        <li>✅ Neon Theme</li>
                        <li>✅ Glow Effects</li>
                        <li>⏰ 30 Day Limit</li>
                        <li>❌ Priority Support</li>
                    </ul>
                    
                    <?php if ($currentLicense['status'] === 'free' && !isset($_SESSION['trial_extended'])): ?>
                    <form method="POST" class="mt-4">
                        <input type="hidden" name="action" value="extend_trial">
                        <button type="submit" class="license-button w-full">Start 7-Day Trial</button>
                    </form>
                    <?php endif; ?>
                </div>
                
                <!-- Premium Version -->
                <div class="border border-yellow-200 rounded-lg p-6 bg-yellow-50">
                    <h3 class="text-xl font-bold mb-4">⭐ Premium Version</h3>
                    <div class="premium-badge mb-4">$49/year</div>
                    
                    <ul class="feature-list">
                        <li>✅ Everything in Trial</li>
                        <li>✅ Unlimited Usage</li>
                        <li>✅ Priority Support</li>
                        <li>✅ Custom Themes</li>
                        <li>✅ Advanced Analytics</li>
                        <li>✅ White-label Option</li>
                        <li>✅ Commercial License</li>
                        <li>✅ Future Updates</li>
                        <li>✅ 1-Year License</li>
                    </ul>
                </div>
                
            </div>
        </div>
        
        <!-- Current Status -->
        <div class="license-card">
            <h2 class="text-3xl font-bold mb-4">📊 Current Status</h2>
            
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div class="text-center p-4 bg-blue-50 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600"><?= ucfirst($currentLicense['status']) ?></div>
                    <div class="text-sm text-blue-800">License Type</div>
                </div>
                <div class="text-center p-4 bg-green-50 rounded-lg">
                    <div class="text-2xl font-bold text-green-600"><?= count($currentLicense['features']) ?></div>
                    <div class="text-sm text-green-800">Features Active</div>
                </div>
                <div class="text-center p-4 bg-purple-50 rounded-lg">
                    <div class="text-2xl font-bold text-purple-600"><?= FlashConfig::$trialDaysLeft ?></div>
                    <div class="text-sm text-purple-800">Trial Days Left</div>
                </div>
                <div class="text-center p-4 bg-yellow-50 rounded-lg">
                    <div class="text-2xl font-bold text-yellow-600">500+</div>
                    <div class="text-sm text-yellow-800">Functions Available</div>
                </div>
            </div>
            
            <div class="flex gap-4">
                <form method="POST">
                    <input type="hidden" name="action" value="check_status">
                    <button type="submit" class="license-button">Check Status</button>
                </form>
                
                <a href="flash-demo-settings.php" class="license-button">View Demo</a>
            </div>
        </div>
        
    </div>

    <!-- Flash messages will be rendered here automatically -->

</body>
</html>
