<?php
/**
 * Flash Messages Controller Examples
 * Practical examples showing how to use flash messages in JobSpace controllers
 * 
 * These examples demonstrate real-world usage patterns for different scenarios
 */

// Include flash helper
require_once __DIR__ . '/../app/helpers/FlashHelper.php';

/**
 * Example 1: Authentication Controller
 */
class AuthController {
    
    public function login() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $email = $_POST['email'] ?? '';
            $password = $_POST['password'] ?? '';
            
            // Validate input
            if (empty($email) || empty($password)) {
                flash_error('Please fill in all required fields.', 'Login Failed');
                redirect('/login');
                return;
            }
            
            // Check credentials
            $user = $this->validateCredentials($email, $password);
            
            if ($user) {
                // Login successful
                $_SESSION['user'] = $user;
                flash_login_success($user['first_name']);
                
                // Redirect to intended page or dashboard
                $redirectTo = $_SESSION['intended_url'] ?? '/dashboard';
                unset($_SESSION['intended_url']);
                redirect($redirectTo);
            } else {
                // Login failed
                flash_login_failed();
                redirect('/login');
            }
        }
    }
    
    public function register() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $data = [
                'first_name' => $_POST['first_name'] ?? '',
                'last_name' => $_POST['last_name'] ?? '',
                'email' => $_POST['email'] ?? '',
                'password' => $_POST['password'] ?? '',
                'confirm_password' => $_POST['confirm_password'] ?? ''
            ];
            
            // Validate registration data
            $errors = $this->validateRegistration($data);
            
            if (!empty($errors)) {
                flash_validation_errors($errors);
                redirect('/register');
                return;
            }
            
            // Create user account
            try {
                $userId = $this->createUser($data);
                
                if ($userId) {
                    flash_registration_success();
                    flash_info('Please check your email to verify your account.', 'Email Verification');
                    redirect('/login');
                } else {
                    flash_error('Registration failed. Please try again.', 'Registration Error');
                    redirect('/register');
                }
            } catch (Exception $e) {
                flash_error('An error occurred during registration: ' . $e->getMessage(), 'System Error');
                redirect('/register');
            }
        }
    }
    
    public function logout() {
        session_destroy();
        flash_logout_success();
        redirect('/');
    }
    
    public function forgotPassword() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $email = $_POST['email'] ?? '';
            
            if (empty($email)) {
                flash_error('Please enter your email address.', 'Email Required');
                redirect('/forgot-password');
                return;
            }
            
            if ($this->sendPasswordResetEmail($email)) {
                flash_password_reset_sent();
                redirect('/login');
            } else {
                flash_error('Email address not found.', 'Reset Failed');
                redirect('/forgot-password');
            }
        }
    }
}

/**
 * Example 2: Quiz Controller
 */
class QuizController {
    
    public function startQuiz($quizId) {
        $quiz = $this->getQuiz($quizId);
        
        if (!$quiz) {
            flash_error('Quiz not found.', 'Error');
            redirect('/quiz');
            return;
        }
        
        if (!$quiz['is_active']) {
            flash_warning('This quiz is currently not available.', 'Quiz Unavailable');
            redirect('/quiz');
            return;
        }
        
        // Check if user has already taken this quiz
        if ($this->hasUserTakenQuiz($_SESSION['user']['id'], $quizId)) {
            flash_info('You have already completed this quiz.', 'Quiz Completed');
            redirect('/quiz/results/' . $quizId);
            return;
        }
        
        // Start quiz session
        $_SESSION['quiz_session'] = [
            'quiz_id' => $quizId,
            'start_time' => time(),
            'questions' => $this->getQuizQuestions($quizId)
        ];
        
        flash_info('Quiz started! You have ' . $quiz['time_limit'] . ' minutes to complete.', 'Good Luck!');
        redirect('/quiz/take/' . $quizId);
    }
    
    public function submitQuiz() {
        if (!isset($_SESSION['quiz_session'])) {
            flash_error('No active quiz session found.', 'Session Error');
            redirect('/quiz');
            return;
        }
        
        $quizSession = $_SESSION['quiz_session'];
        $answers = $_POST['answers'] ?? [];
        
        // Calculate score
        $result = $this->calculateQuizScore($quizSession['quiz_id'], $answers);
        
        // Save result to database
        $this->saveQuizResult($_SESSION['user']['id'], $quizSession['quiz_id'], $result);
        
        // Clear quiz session
        unset($_SESSION['quiz_session']);
        
        // Show appropriate message based on score
        if ($result['score'] >= 70) {
            flash_quiz_completed($result['score']);
            flash_success('You earned ' . $result['points'] . ' points!', 'Points Earned');
        } else {
            flash_quiz_failed($result['score']);
            flash_info('Don\'t worry, you can retake this quiz after 24 hours.', 'Try Again');
        }
        
        redirect('/quiz/results/' . $quizSession['quiz_id']);
    }
    
    public function createQuiz() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $data = [
                'title' => $_POST['title'] ?? '',
                'description' => $_POST['description'] ?? '',
                'time_limit' => $_POST['time_limit'] ?? 0,
                'difficulty' => $_POST['difficulty'] ?? 'medium'
            ];
            
            $errors = $this->validateQuizData($data);
            
            if (!empty($errors)) {
                flash_validation_errors($errors);
                redirect('/quiz/create');
                return;
            }
            
            try {
                $quizId = $this->saveQuiz($data);
                flash_created('Quiz');
                flash_info('You can now add questions to your quiz.', 'Next Step');
                redirect('/quiz/edit/' . $quizId);
            } catch (Exception $e) {
                flash_error('Failed to create quiz: ' . $e->getMessage(), 'Creation Failed');
                redirect('/quiz/create');
            }
        }
    }
}

/**
 * Example 3: User Profile Controller
 */
class ProfileController {
    
    public function updateProfile() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $data = [
                'first_name' => $_POST['first_name'] ?? '',
                'last_name' => $_POST['last_name'] ?? '',
                'email' => $_POST['email'] ?? '',
                'phone' => $_POST['phone'] ?? '',
                'bio' => $_POST['bio'] ?? ''
            ];
            
            $errors = $this->validateProfileData($data);
            
            if (!empty($errors)) {
                flash_validation_errors($errors);
                redirect('/profile/edit');
                return;
            }
            
            try {
                $this->updateUserProfile($_SESSION['user']['id'], $data);
                
                // Update session data
                $_SESSION['user'] = array_merge($_SESSION['user'], $data);
                
                flash_updated('Profile');
                redirect('/profile');
            } catch (Exception $e) {
                flash_error('Failed to update profile: ' . $e->getMessage(), 'Update Failed');
                redirect('/profile/edit');
            }
        }
    }
    
    public function uploadAvatar() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['avatar'])) {
            $file = $_FILES['avatar'];
            
            // Validate file
            $validation = $this->validateAvatarFile($file);
            
            if (!$validation['valid']) {
                flash_upload_failed($validation['error']);
                redirect('/profile/edit');
                return;
            }
            
            try {
                $filename = $this->saveAvatarFile($file, $_SESSION['user']['id']);
                
                // Update user avatar in database
                $this->updateUserAvatar($_SESSION['user']['id'], $filename);
                
                // Update session
                $_SESSION['user']['avatar'] = $filename;
                
                flash_upload_success('Avatar');
                redirect('/profile');
            } catch (Exception $e) {
                flash_upload_failed($e->getMessage());
                redirect('/profile/edit');
            }
        }
    }
    
    public function changePassword() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $currentPassword = $_POST['current_password'] ?? '';
            $newPassword = $_POST['new_password'] ?? '';
            $confirmPassword = $_POST['confirm_password'] ?? '';
            
            // Validate current password
            if (!$this->verifyCurrentPassword($_SESSION['user']['id'], $currentPassword)) {
                flash_error('Current password is incorrect.', 'Password Change Failed');
                redirect('/profile/security');
                return;
            }
            
            // Validate new password
            if ($newPassword !== $confirmPassword) {
                flash_error('New passwords do not match.', 'Password Mismatch');
                redirect('/profile/security');
                return;
            }
            
            if (strlen($newPassword) < 6) {
                flash_error('Password must be at least 6 characters long.', 'Password Too Short');
                redirect('/profile/security');
                return;
            }
            
            try {
                $this->updateUserPassword($_SESSION['user']['id'], $newPassword);
                flash_success('Password changed successfully!', 'Security Updated');
                redirect('/profile/security');
            } catch (Exception $e) {
                flash_error('Failed to change password: ' . $e->getMessage(), 'Update Failed');
                redirect('/profile/security');
            }
        }
    }
}

/**
 * Example 4: E-commerce Controller
 */
class EcommerceController {
    
    public function addToCart() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $productId = $_POST['product_id'] ?? 0;
            $quantity = $_POST['quantity'] ?? 1;
            
            $product = $this->getProduct($productId);
            
            if (!$product) {
                flash_error('Product not found.', 'Error');
                redirect('/shop');
                return;
            }
            
            if ($product['stock'] < $quantity) {
                flash_warning('Only ' . $product['stock'] . ' items available in stock.', 'Insufficient Stock');
                redirect('/product/' . $productId);
                return;
            }
            
            try {
                $this->addProductToCart($_SESSION['user']['id'], $productId, $quantity);
                flash_added_to_cart($product['name']);
                redirect('/cart');
            } catch (Exception $e) {
                flash_error('Failed to add product to cart: ' . $e->getMessage(), 'Cart Error');
                redirect('/product/' . $productId);
            }
        }
    }
    
    public function placeOrder() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $cartItems = $this->getCartItems($_SESSION['user']['id']);
            
            if (empty($cartItems)) {
                flash_warning('Your cart is empty.', 'Empty Cart');
                redirect('/cart');
                return;
            }
            
            $shippingAddress = $_POST['shipping_address'] ?? '';
            $paymentMethod = $_POST['payment_method'] ?? '';
            
            if (empty($shippingAddress) || empty($paymentMethod)) {
                flash_required_fields();
                redirect('/checkout');
                return;
            }
            
            try {
                $orderId = $this->createOrder($_SESSION['user']['id'], $cartItems, $shippingAddress, $paymentMethod);
                
                // Clear cart
                $this->clearCart($_SESSION['user']['id']);
                
                flash_order_placed($orderId);
                flash_info('You will receive an email confirmation shortly.', 'Order Confirmation');
                redirect('/orders/' . $orderId);
            } catch (Exception $e) {
                flash_error('Failed to place order: ' . $e->getMessage(), 'Order Failed');
                redirect('/checkout');
            }
        }
    }
}

/**
 * Example 5: Admin Controller
 */
class AdminController {
    
    public function deleteUser($userId) {
        // Check admin permissions
        if ($_SESSION['user']['role'] !== 'admin') {
            flash_access_denied();
            redirect('/dashboard');
            return;
        }
        
        $user = $this->getUser($userId);
        
        if (!$user) {
            flash_error('User not found.', 'Error');
            redirect('/admin/users');
            return;
        }
        
        // Prevent deleting other admins
        if ($user['role'] === 'admin' && $user['id'] !== $_SESSION['user']['id']) {
            flash_error('Cannot delete other administrator accounts.', 'Permission Denied');
            redirect('/admin/users');
            return;
        }
        
        try {
            $this->deleteUserAccount($userId);
            flash_deleted('User account');
            redirect('/admin/users');
        } catch (Exception $e) {
            flash_error('Failed to delete user: ' . $e->getMessage(), 'Deletion Failed');
            redirect('/admin/users');
        }
    }
    
    public function updateSystemSettings() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $settings = $_POST['settings'] ?? [];
            
            try {
                $this->saveSystemSettings($settings);
                flash_saved('System settings');
                redirect('/admin/settings');
            } catch (Exception $e) {
                flash_error('Failed to save settings: ' . $e->getMessage(), 'Save Failed');
                redirect('/admin/settings');
            }
        }
    }
}

/**
 * Helper function to redirect with flash messages
 */
function redirect($url) {
    header('Location: ' . $url);
    exit;
}

/**
 * Usage in your actual controllers:
 * 
 * 1. Include the FlashHelper.php in your bootstrap or autoloader
 * 2. Use flash_* functions before redirects
 * 3. Include flash-messages component in your layouts
 * 4. Flash messages will automatically display and clear
 * 
 * Example in a real controller file:
 * 
 * <?php
 * require_once 'app/helpers/FlashHelper.php';
 * 
 * class LoginController {
 *     public function authenticate() {
 *         if ($this->isValidUser()) {
 *             flash_login_success($user['name']);
 *             redirect('/dashboard');
 *         } else {
 *             flash_login_failed();
 *             redirect('/login');
 *         }
 *     }
 * }
 */
?>
