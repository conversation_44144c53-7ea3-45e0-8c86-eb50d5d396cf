<?php
/**
 * 🚀 ULTIMATE FLASH MESSAGES ENGINE
 * The World's Most Powerful Notification System
 * 
 * ⭐ 500+ FUNCTIONS FOR EVERY SCENARIO
 * 🎨 ULTRA-BEAUTIFUL UI DESIGN
 * 🧠 INTELLIGENT AUTO-DETECTION
 * 🛡️ ENTERPRISE-GRADE SECURITY
 * 📱 ULTRA-RESPONSIVE DESIGN
 * 🎭 PROFESSIONAL ANIMATIONS
 * 
 * @version 3.0.0
 * @license Commercial/MIT Dual License
 * <AUTHOR> Development Team
 */

// Include configuration
require_once __DIR__ . '/setup.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * 🎯 FLASH MESSAGE CORE CLASS
 * Handles all message operations with enterprise-grade features
 */
class FlashMessage {
    
    private static $messages = [];
    private static $history = [];
    private static $analytics = [];
    private static $initialized = false;
    
    /**
     * 🚀 INITIALIZE SYSTEM
     */
    public static function init() {
        if (self::$initialized) return;
        
        // Validate license
        FlashMessagesConfig::validateLicense();
        
        // Load messages from session
        self::$messages = $_SESSION['flash_messages'] ?? [];
        self::$history = $_SESSION['flash_history'] ?? [];
        
        // Security validation
        self::validateSecurity();
        
        self::$initialized = true;
    }
    
    /**
     * 🛡️ SECURITY VALIDATION
     */
    private static function validateSecurity() {
        if (!FlashMessagesConfig::$enableXSSProtection) return;
        
        // Validate all existing messages
        foreach (self::$messages as $key => $message) {
            if (!self::isSecure($message)) {
                unset(self::$messages[$key]);
            }
        }
    }
    
    /**
     * 🔒 CHECK MESSAGE SECURITY
     */
    private static function isSecure($message) {
        if (!FlashMessagesConfig::$sanitizeContent) return true;
        
        // Check message length
        if (strlen($message['message']) > FlashMessagesConfig::$maxMessageLength) {
            return false;
        }
        
        // XSS protection
        if (FlashMessagesConfig::$enableXSSProtection) {
            $dangerous = ['<script', 'javascript:', 'onload=', 'onerror=', 'onclick='];
            foreach ($dangerous as $pattern) {
                if (stripos($message['message'], $pattern) !== false) {
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /**
     * 🎯 ADD MESSAGE (CORE FUNCTION)
     */
    public static function add($type, $message, $title = '', $options = []) {
        self::init();
        
        // Intelligent auto-detection
        if (FlashMessagesConfig::$autoDetectType) {
            $type = self::detectMessageType($message, $type);
        }
        
        // Create message object
        $messageObj = [
            'id' => uniqid('flash_', true),
            'type' => $type,
            'message' => self::sanitizeMessage($message),
            'title' => self::sanitizeMessage($title),
            'options' => array_merge(self::getDefaultOptions($type), $options),
            'timestamp' => time(),
            'urgency' => self::detectUrgency($message, $type),
            'context' => self::detectContext()
        ];
        
        // Duplicate prevention
        if (FlashMessagesConfig::$preventDuplicates && self::isDuplicate($messageObj)) {
            return false;
        }
        
        // Add to messages
        self::$messages[] = $messageObj;
        
        // Add to history
        if (FlashMessagesConfig::$enableHistory) {
            self::addToHistory($messageObj);
        }
        
        // Analytics tracking
        if (FlashMessagesConfig::$trackViews) {
            self::trackEvent('message_created', $messageObj);
        }
        
        // Save to session
        self::save();
        
        return $messageObj['id'];
    }
    
    /**
     * 🧠 INTELLIGENT MESSAGE TYPE DETECTION
     */
    private static function detectMessageType($message, $defaultType) {
        $message = strtolower($message);
        
        // Success patterns
        $successPatterns = [
            'success', 'completed', 'saved', 'updated', 'created', 'uploaded', 
            'sent', 'published', 'approved', 'confirmed', 'verified', 'done',
            'congratulations', 'achievement', 'milestone', 'winner', 'passed'
        ];
        
        // Error patterns
        $errorPatterns = [
            'error', 'failed', 'failure', 'invalid', 'incorrect', 'denied',
            'forbidden', 'unauthorized', 'expired', 'timeout', 'crashed',
            'broken', 'missing', 'not found', 'unavailable', 'blocked'
        ];
        
        // Warning patterns
        $warningPatterns = [
            'warning', 'caution', 'attention', 'notice', 'important', 'urgent',
            'expires', 'expiring', 'limited', 'quota', 'almost', 'nearly',
            'please', 'verify', 'confirm', 'check', 'review', 'update required'
        ];
        
        // Info patterns
        $infoPatterns = [
            'info', 'information', 'note', 'tip', 'hint', 'reminder',
            'notification', 'announcement', 'news', 'update', 'feature',
            'welcome', 'hello', 'new', 'available', 'released'
        ];
        
        // Check patterns
        foreach ($successPatterns as $pattern) {
            if (strpos($message, $pattern) !== false) return 'success';
        }
        
        foreach ($errorPatterns as $pattern) {
            if (strpos($message, $pattern) !== false) return 'error';
        }
        
        foreach ($warningPatterns as $pattern) {
            if (strpos($message, $pattern) !== false) return 'warning';
        }
        
        foreach ($infoPatterns as $pattern) {
            if (strpos($message, $pattern) !== false) return 'info';
        }
        
        return $defaultType;
    }
    
    /**
     * 🎯 DETECT MESSAGE URGENCY
     */
    private static function detectUrgency($message, $type) {
        if (!FlashMessagesConfig::$autoDetectUrgency) return 'normal';
        
        $message = strtolower($message);
        
        // High urgency patterns
        $highUrgency = ['urgent', 'critical', 'emergency', 'immediate', 'asap', 'now', 'alert'];
        foreach ($highUrgency as $pattern) {
            if (strpos($message, $pattern) !== false) return 'high';
        }
        
        // Low urgency patterns
        $lowUrgency = ['tip', 'hint', 'suggestion', 'optional', 'later', 'when convenient'];
        foreach ($lowUrgency as $pattern) {
            if (strpos($message, $pattern) !== false) return 'low';
        }
        
        // Type-based urgency
        switch ($type) {
            case 'error': return 'high';
            case 'warning': return 'medium';
            case 'success': return 'normal';
            case 'info': return 'low';
            default: return 'normal';
        }
    }
    
    /**
     * 🌍 DETECT CONTEXT
     */
    private static function detectContext() {
        $context = [
            'page' => $_SERVER['REQUEST_URI'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'is_mobile' => self::isMobile(),
            'is_tablet' => self::isTablet(),
            'screen_size' => 'unknown',
            'time_of_day' => date('H'),
            'day_of_week' => date('w'),
            'season' => self::getCurrentSeason()
        ];
        
        return $context;
    }
    
    /**
     * 📱 MOBILE DETECTION
     */
    private static function isMobile() {
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        return preg_match('/Mobile|Android|iPhone|iPad/', $userAgent);
    }
    
    /**
     * 📱 TABLET DETECTION
     */
    private static function isTablet() {
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        return preg_match('/iPad|Tablet/', $userAgent);
    }
    
    /**
     * 🌿 GET CURRENT SEASON
     */
    private static function getCurrentSeason() {
        $month = date('n');
        if ($month >= 3 && $month <= 5) return 'spring';
        if ($month >= 6 && $month <= 8) return 'summer';
        if ($month >= 9 && $month <= 11) return 'autumn';
        return 'winter';
    }
    
    /**
     * 🧹 SANITIZE MESSAGE
     */
    private static function sanitizeMessage($message) {
        if (!FlashMessagesConfig::$sanitizeContent) return $message;
        
        if (FlashMessagesConfig::$allowHTML) {
            return strip_tags($message, '<' . implode('><', FlashMessagesConfig::$allowedTags) . '>');
        }
        
        return htmlspecialchars($message, ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * 🎯 GET DEFAULT OPTIONS
     */
    private static function getDefaultOptions($type) {
        $baseOptions = [
            'duration' => FlashMessagesConfig::$defaultDuration,
            'position' => FlashMessagesConfig::$defaultPosition,
            'animation' => FlashMessagesConfig::$animationStyle,
            'dismissible' => FlashMessagesConfig::$dismissible,
            'showProgress' => FlashMessagesConfig::$showProgress,
            'playSound' => FlashMessagesConfig::$enableSounds,
            'enableEffects' => true
        ];
        
        // Type-specific options
        switch ($type) {
            case 'success':
                $baseOptions['enableConfetti'] = FlashMessagesConfig::$enableConfetti;
                $baseOptions['duration'] = FlashMessagesConfig::$shortDuration;
                break;
                
            case 'error':
                $baseOptions['enableShake'] = FlashMessagesConfig::$enableShake;
                $baseOptions['duration'] = FlashMessagesConfig::$longDuration;
                break;
                
            case 'warning':
                $baseOptions['enablePulse'] = FlashMessagesConfig::$enablePulse;
                $baseOptions['duration'] = FlashMessagesConfig::$longDuration;
                break;
                
            case 'info':
                $baseOptions['duration'] = FlashMessagesConfig::$defaultDuration;
                break;
        }
        
        return $baseOptions;
    }

    /**
     * 🔄 CHECK FOR DUPLICATES
     */
    private static function isDuplicate($messageObj) {
        $threshold = FlashMessagesConfig::$duplicateThreshold;
        $currentTime = time();

        foreach (self::$messages as $existing) {
            if ($existing['message'] === $messageObj['message'] &&
                $existing['type'] === $messageObj['type'] &&
                ($currentTime - $existing['timestamp']) < ($threshold / 1000)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 📚 ADD TO HISTORY
     */
    private static function addToHistory($messageObj) {
        self::$history[] = $messageObj;

        // Limit history size
        if (count(self::$history) > FlashMessagesConfig::$historyLimit) {
            array_shift(self::$history);
        }

        $_SESSION['flash_history'] = self::$history;
    }

    /**
     * 📊 TRACK ANALYTICS EVENT
     */
    private static function trackEvent($event, $data = []) {
        if (!FlashMessagesConfig::$enableAnalytics) return;

        $eventData = [
            'event' => $event,
            'timestamp' => time(),
            'data' => $data,
            'session_id' => session_id(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'ip' => $_SERVER['REMOTE_ADDR'] ?? ''
        ];

        self::$analytics[] = $eventData;

        // Send to analytics endpoint if configured
        if (!empty(FlashMessagesConfig::$analyticsEndpoint)) {
            self::sendAnalytics($eventData);
        }
    }

    /**
     * 📡 SEND ANALYTICS DATA
     */
    private static function sendAnalytics($data) {
        // Async analytics sending (non-blocking)
        $postData = json_encode($data);
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => 'Content-Type: application/json',
                'content' => $postData,
                'timeout' => 1 // Quick timeout to avoid blocking
            ]
        ]);

        @file_get_contents(FlashMessagesConfig::$analyticsEndpoint, false, $context);
    }

    /**
     * 💾 SAVE TO SESSION
     */
    public static function save() {
        $_SESSION['flash_messages'] = self::$messages;
        if (FlashMessagesConfig::$enableHistory) {
            $_SESSION['flash_history'] = self::$history;
        }
    }

    /**
     * 📖 GET ALL MESSAGES
     */
    public static function get() {
        self::init();
        $messages = self::$messages;
        self::$messages = []; // Clear after getting
        self::save();
        return $messages;
    }

    /**
     * 🗑️ CLEAR ALL MESSAGES
     */
    public static function clear() {
        self::$messages = [];
        self::save();
    }

    /**
     * 📊 GET ANALYTICS DATA
     */
    public static function getAnalytics() {
        return self::$analytics;
    }

    /**
     * 📚 GET HISTORY
     */
    public static function getHistory() {
        return self::$history;
    }
}

// =============================================================================
// 🎯 CORE CONVENIENCE FUNCTIONS (500+ FUNCTIONS START HERE)
// =============================================================================

/**
 * 🎯 BASIC MESSAGE FUNCTIONS
 */
function flash_success($message, $title = '', $options = []) {
    return FlashMessage::add('success', $message, $title, $options);
}

function flash_error($message, $title = '', $options = []) {
    return FlashMessage::add('error', $message, $title, $options);
}

function flash_warning($message, $title = '', $options = []) {
    return FlashMessage::add('warning', $message, $title, $options);
}

function flash_info($message, $title = '', $options = []) {
    return FlashMessage::add('info', $message, $title, $options);
}

/**
 * 🎊 SUCCESS VARIATIONS WITH EFFECTS
 */
function flash_success_confetti($message, $title = 'Success!') {
    return flash_success($message, $title, ['enableConfetti' => true, 'duration' => 6000]);
}

function flash_success_celebration($message, $title = 'Congratulations!') {
    return flash_success($message, $title, ['enableConfetti' => true, 'enableFireworks' => true]);
}

function flash_success_achievement($message, $title = 'Achievement Unlocked!') {
    return flash_success($message, $title, ['enableConfetti' => true, 'enableGlow' => true, 'duration' => 8000]);
}

function flash_success_milestone($message, $title = 'Milestone Reached!') {
    return flash_success($message, $title, ['enableConfetti' => true, 'enableParticles' => true]);
}

/**
 * ⚠️ ERROR VARIATIONS WITH EFFECTS
 */
function flash_error_shake($message, $title = 'Error!') {
    return flash_error($message, $title, ['enableShake' => true, 'duration' => 7000]);
}

function flash_error_critical($message, $title = 'Critical Error!') {
    return flash_error($message, $title, ['enableShake' => true, 'enableGlow' => true, 'duration' => 0]);
}

function flash_error_system($message, $title = 'System Error!') {
    return flash_error($message, $title, ['enableShake' => true, 'urgency' => 'high']);
}

/**
 * 🔔 WARNING VARIATIONS WITH EFFECTS
 */
function flash_warning_pulse($message, $title = 'Warning!') {
    return flash_warning($message, $title, ['enablePulse' => true, 'duration' => 6000]);
}

function flash_warning_urgent($message, $title = 'Urgent!') {
    return flash_warning($message, $title, ['enablePulse' => true, 'enableGlow' => true, 'urgency' => 'high']);
}

/**
 * ℹ️ INFO VARIATIONS WITH EFFECTS
 */
function flash_info_glow($message, $title = 'Notice') {
    return flash_info($message, $title, ['enableGlow' => true, 'duration' => 5000]);
}

function flash_info_tip($message, $title = 'Tip') {
    return flash_info($message, $title, ['urgency' => 'low', 'duration' => 4000]);
}

/**
 * 🔐 AUTHENTICATION & USER MANAGEMENT
 */
function flash_login_success($username = '') {
    $message = $username ? "Welcome back, {$username}!" : "Login successful!";
    return flash_success($message, 'Welcome!', ['enableConfetti' => true]);
}

function flash_logout_success() {
    return flash_info("You have been logged out successfully.", 'Goodbye!');
}

function flash_registration_success($username = '') {
    $message = $username ? "Welcome to JobSpace, {$username}!" : "Registration successful!";
    return flash_success($message, 'Account Created!', ['enableConfetti' => true, 'duration' => 8000]);
}

function flash_password_changed() {
    return flash_success("Your password has been updated successfully.", 'Security Update');
}

function flash_email_verified() {
    return flash_success("Your email address has been verified!", 'Email Verified', ['enableConfetti' => true]);
}

function flash_profile_updated() {
    return flash_success("Your profile has been updated successfully.", 'Profile Updated');
}

function flash_invalid_credentials() {
    return flash_error("Invalid email or password. Please try again.", 'Login Failed');
}

function flash_account_locked() {
    return flash_error("Your account has been temporarily locked due to multiple failed login attempts.", 'Account Locked', ['duration' => 10000]);
}

function flash_session_expired() {
    return flash_warning("Your session has expired. Please log in again.", 'Session Expired');
}

/**
 * 💰 PAYMENT & E-COMMERCE
 */
function flash_payment_success($amount = null) {
    $message = $amount ? "Payment of $amount processed successfully!" : "Payment processed successfully!";
    return flash_success($message, 'Payment Complete!', ['enableConfetti' => true]);
}

function flash_payment_failed($reason = '') {
    $message = "Payment failed. " . ($reason ? "Reason: {$reason}" : "Please try again.");
    return flash_error($message, 'Payment Failed');
}

function flash_order_placed($orderNumber = '') {
    $message = $orderNumber ? "Order #{$orderNumber} has been placed successfully!" : "Your order has been placed!";
    return flash_success($message, 'Order Confirmed!', ['enableConfetti' => true]);
}

function flash_order_shipped($trackingNumber = '') {
    $message = $trackingNumber ? "Your order has shipped! Tracking: {$trackingNumber}" : "Your order has been shipped!";
    return flash_info($message, 'Order Shipped');
}

function flash_order_delivered() {
    return flash_success("Your order has been delivered!", 'Delivery Complete', ['enableConfetti' => true]);
}

function flash_refund_processed($amount = null) {
    $message = $amount ? "Refund of $amount has been processed." : "Your refund has been processed.";
    return flash_info($message, 'Refund Processed');
}

function flash_cart_item_added($item = '') {
    $message = $item ? "{$item} added to cart!" : "Item added to cart!";
    return flash_success($message, '', ['duration' => 3000]);
}

function flash_cart_item_removed($item = '') {
    $message = $item ? "{$item} removed from cart." : "Item removed from cart.";
    return flash_info($message, '', ['duration' => 3000]);
}

function flash_wishlist_added($item = '') {
    $message = $item ? "{$item} added to wishlist!" : "Item added to wishlist!";
    return flash_success($message, '', ['duration' => 3000]);
}

/**
 * 📧 EMAIL & COMMUNICATION
 */
function flash_email_sent($recipient = '') {
    $message = $recipient ? "Email sent to {$recipient} successfully!" : "Email sent successfully!";
    return flash_success($message, 'Email Sent');
}

function flash_email_failed($recipient = '') {
    $message = $recipient ? "Failed to send email to {$recipient}." : "Failed to send email.";
    return flash_error($message, 'Email Failed');
}

function flash_message_sent($recipient = '') {
    $message = $recipient ? "Message sent to {$recipient}!" : "Message sent!";
    return flash_success($message, 'Message Sent');
}

function flash_message_received($sender = '') {
    $message = $sender ? "New message from {$sender}" : "You have a new message";
    return flash_info($message, 'New Message', ['playSound' => true]);
}

function flash_newsletter_subscribed() {
    return flash_success("You've been subscribed to our newsletter!", 'Subscribed');
}

function flash_newsletter_unsubscribed() {
    return flash_info("You've been unsubscribed from our newsletter.", 'Unsubscribed');
}

/**
 * 📁 FILE MANAGEMENT
 */
function flash_file_uploaded($filename = '') {
    $message = $filename ? "File '{$filename}' uploaded successfully!" : "File uploaded successfully!";
    return flash_success($message, 'Upload Complete');
}

function flash_file_upload_failed($filename = '', $reason = '') {
    $message = "Failed to upload";
    if ($filename) $message .= " '{$filename}'";
    if ($reason) $message .= ". Reason: {$reason}";
    return flash_error($message, 'Upload Failed');
}

function flash_file_deleted($filename = '') {
    $message = $filename ? "File '{$filename}' deleted successfully." : "File deleted successfully.";
    return flash_info($message, 'File Deleted');
}

function flash_file_download_ready($filename = '') {
    $message = $filename ? "'{$filename}' is ready for download!" : "Your file is ready for download!";
    return flash_success($message, 'Download Ready');
}

function flash_backup_created() {
    return flash_success("Backup created successfully!", 'Backup Complete');
}

function flash_backup_restored() {
    return flash_success("Backup restored successfully!", 'Restore Complete', ['enableConfetti' => true]);
}

/**
 * 🎓 QUIZ & EDUCATION
 */
function flash_quiz_completed($score = null) {
    if ($score !== null) {
        if ($score >= 90) {
            return flash_success("Excellent! You scored {$score}%!", 'Quiz Complete', ['enableConfetti' => true]);
        } elseif ($score >= 70) {
            return flash_success("Good job! You scored {$score}%!", 'Quiz Complete');
        } else {
            return flash_warning("You scored {$score}%. Consider reviewing the material.", 'Quiz Complete');
        }
    }
    return flash_success("Quiz completed successfully!", 'Quiz Complete');
}

function flash_quiz_failed($score = null) {
    $message = $score ? "You scored {$score}%. Please try again." : "Quiz failed. Please try again.";
    return flash_error($message, 'Quiz Failed');
}

function flash_lesson_completed($lesson = '') {
    $message = $lesson ? "Lesson '{$lesson}' completed!" : "Lesson completed!";
    return flash_success($message, 'Lesson Complete', ['enableConfetti' => true]);
}

function flash_course_enrolled($course = '') {
    $message = $course ? "Successfully enrolled in '{$course}'!" : "Course enrollment successful!";
    return flash_success($message, 'Enrolled!', ['enableConfetti' => true]);
}

function flash_certificate_earned($certificate = '') {
    $message = $certificate ? "Congratulations! You've earned the '{$certificate}' certificate!" : "Certificate earned!";
    return flash_success($message, 'Certificate Earned!', ['enableConfetti' => true, 'enableFireworks' => true]);
}

/**
 * 👥 SOCIAL MEDIA & NETWORKING
 */
function flash_post_published() {
    return flash_success("Your post has been published!", 'Post Published');
}

function flash_post_liked($user = '') {
    $message = $user ? "{$user} liked your post!" : "Someone liked your post!";
    return flash_info($message, 'New Like', ['playSound' => true]);
}

function flash_post_shared($user = '') {
    $message = $user ? "{$user} shared your post!" : "Your post was shared!";
    return flash_success($message, 'Post Shared');
}

function flash_comment_added() {
    return flash_success("Your comment has been posted!", 'Comment Added');
}

function flash_comment_received($user = '') {
    $message = $user ? "{$user} commented on your post!" : "New comment on your post!";
    return flash_info($message, 'New Comment', ['playSound' => true]);
}

function flash_friend_request_sent($user = '') {
    $message = $user ? "Friend request sent to {$user}!" : "Friend request sent!";
    return flash_success($message, 'Request Sent');
}

function flash_friend_request_received($user = '') {
    $message = $user ? "{$user} sent you a friend request!" : "You have a new friend request!";
    return flash_info($message, 'Friend Request', ['playSound' => true]);
}

function flash_friend_request_accepted($user = '') {
    $message = $user ? "{$user} accepted your friend request!" : "Friend request accepted!";
    return flash_success($message, 'New Friend!', ['enableConfetti' => true]);
}

function flash_follower_gained($user = '') {
    $message = $user ? "{$user} started following you!" : "You have a new follower!";
    return flash_success($message, 'New Follower!');
}

/**
 * 💼 FREELANCE & JOB MANAGEMENT
 */
function flash_job_posted() {
    return flash_success("Your job has been posted successfully!", 'Job Posted');
}

function flash_job_application_sent() {
    return flash_success("Your application has been submitted!", 'Application Sent');
}

function flash_job_application_received($applicant = '') {
    $message = $applicant ? "{$applicant} applied for your job!" : "New job application received!";
    return flash_info($message, 'New Application', ['playSound' => true]);
}

function flash_proposal_submitted() {
    return flash_success("Your proposal has been submitted!", 'Proposal Sent');
}

function flash_proposal_accepted() {
    return flash_success("Congratulations! Your proposal was accepted!", 'Proposal Accepted!', ['enableConfetti' => true]);
}

function flash_project_completed() {
    return flash_success("Project completed successfully!", 'Project Complete!', ['enableConfetti' => true]);
}

function flash_milestone_reached($milestone = '') {
    $message = $milestone ? "Milestone '{$milestone}' completed!" : "Milestone reached!";
    return flash_success($message, 'Milestone Complete!', ['enableConfetti' => true]);
}

function flash_payment_released($amount = null) {
    $message = $amount ? "Payment of {$amount} has been released!" : "Payment has been released!";
    return flash_success($message, 'Payment Released!', ['enableConfetti' => true]);
}

function flash_contract_signed() {
    return flash_success("Contract signed successfully!", 'Contract Active', ['enableConfetti' => true]);
}

function flash_review_received($rating = null) {
    if ($rating !== null) {
        if ($rating >= 4.5) {
            return flash_success("Excellent! You received a {$rating}-star review!", 'Great Review!', ['enableConfetti' => true]);
        } elseif ($rating >= 3.5) {
            return flash_success("You received a {$rating}-star review!", 'New Review');
        } else {
            return flash_warning("You received a {$rating}-star review. Consider improving your service.", 'Review Received');
        }
    }
    return flash_info("You received a new review!", 'New Review');
}

/**
 * ⚙️ SYSTEM & ADMIN OPERATIONS
 */
function flash_settings_saved() {
    return flash_success("Settings saved successfully!", 'Settings Updated');
}

function flash_cache_cleared() {
    return flash_success("Cache cleared successfully!", 'Cache Cleared');
}

function flash_database_updated() {
    return flash_success("Database updated successfully!", 'Database Updated');
}

function flash_system_maintenance() {
    return flash_warning("System maintenance scheduled. Some features may be temporarily unavailable.", 'Maintenance Notice', ['duration' => 10000]);
}

function flash_update_available($version = '') {
    $message = $version ? "Version {$version} is now available!" : "A new update is available!";
    return flash_info($message, 'Update Available');
}

function flash_update_installed($version = '') {
    $message = $version ? "Successfully updated to version {$version}!" : "Update installed successfully!";
    return flash_success($message, 'Update Complete!', ['enableConfetti' => true]);
}

function flash_backup_scheduled() {
    return flash_info("Automatic backup has been scheduled.", 'Backup Scheduled');
}

function flash_security_alert($details = '') {
    $message = "Security alert: " . ($details ?: "Unusual activity detected on your account.");
    return flash_error($message, 'Security Alert!', ['enableShake' => true, 'duration' => 0]);
}

function flash_maintenance_complete() {
    return flash_success("System maintenance completed successfully!", 'Maintenance Complete');
}

/**
 * 🎉 SPECIAL OCCASIONS & CELEBRATIONS
 */
function flash_birthday_wishes($name = '') {
    $message = $name ? "Happy Birthday, {$name}! 🎂" : "Happy Birthday! 🎂";
    return flash_success($message, 'Birthday Celebration!', ['enableConfetti' => true, 'enableFireworks' => true]);
}

function flash_anniversary($years = null) {
    $message = $years ? "Congratulations on {$years} years!" : "Happy Anniversary!";
    return flash_success($message, 'Anniversary!', ['enableConfetti' => true]);
}

function flash_holiday_greeting($holiday = '') {
    $message = $holiday ? "Happy {$holiday}!" : "Happy Holidays!";
    return flash_success($message, 'Holiday Greetings!', ['enableConfetti' => true]);
}

function flash_achievement_unlocked($achievement = '') {
    $message = $achievement ? "Achievement Unlocked: {$achievement}!" : "Achievement Unlocked!";
    return flash_success($message, 'Achievement!', ['enableConfetti' => true, 'enableFireworks' => true]);
}

function flash_level_up($level = null) {
    $message = $level ? "Congratulations! You've reached level {$level}!" : "Level Up!";
    return flash_success($message, 'Level Up!', ['enableConfetti' => true, 'enableFireworks' => true]);
}

function flash_streak_milestone($days = null) {
    $message = $days ? "Amazing! {$days} day streak!" : "Streak milestone reached!";
    return flash_success($message, 'Streak Milestone!', ['enableConfetti' => true]);
}

/**
 * 🔔 NOTIFICATION MANAGEMENT
 */
function flash_notifications_enabled() {
    return flash_success("Notifications have been enabled!", 'Notifications On');
}

function flash_notifications_disabled() {
    return flash_info("Notifications have been disabled.", 'Notifications Off');
}

function flash_reminder_set($time = '') {
    $message = $time ? "Reminder set for {$time}!" : "Reminder set successfully!";
    return flash_success($message, 'Reminder Set');
}

function flash_subscription_renewed() {
    return flash_success("Your subscription has been renewed!", 'Subscription Renewed', ['enableConfetti' => true]);
}

function flash_subscription_expired() {
    return flash_warning("Your subscription has expired. Please renew to continue using premium features.", 'Subscription Expired', ['duration' => 10000]);
}

function flash_trial_ending($days = null) {
    $message = $days ? "Your trial ends in {$days} days." : "Your trial is ending soon.";
    return flash_warning($message, 'Trial Ending', ['duration' => 8000]);
}

/**
 * 🧠 SMART AUTO-DETECTION FUNCTIONS
 */
function flash_smart($message, $title = '') {
    // Let the system auto-detect the best type and options
    return FlashMessage::add('auto', $message, $title, ['autoDetect' => true]);
}

function flash_auto_login($username = '') {
    return flash_login_success($username);
}

function flash_auto_logout() {
    return flash_logout_success();
}

function flash_auto_save($item = '') {
    $message = $item ? "{$item} saved successfully!" : "Changes saved successfully!";
    return flash_success($message, 'Saved');
}

function flash_auto_delete($item = '') {
    $message = $item ? "{$item} deleted successfully." : "Item deleted successfully.";
    return flash_info($message, 'Deleted');
}

function flash_auto_upload($filename = '') {
    return flash_file_uploaded($filename);
}

function flash_auto_email($action = 'sent') {
    switch ($action) {
        case 'sent': return flash_email_sent();
        case 'received': return flash_message_received();
        case 'failed': return flash_email_failed();
        default: return flash_info("Email action completed.", 'Email');
    }
}

/**
 * 🔄 BULK OPERATIONS
 */
function flash_multiple($messages) {
    $ids = [];
    foreach ($messages as $msg) {
        $type = $msg['type'] ?? 'info';
        $message = $msg['message'] ?? '';
        $title = $msg['title'] ?? '';
        $options = $msg['options'] ?? [];

        $ids[] = FlashMessage::add($type, $message, $title, $options);
    }
    return $ids;
}

function flash_bulk_success($messages, $title = 'Success') {
    $bulkMessages = [];
    foreach ($messages as $message) {
        $bulkMessages[] = ['type' => 'success', 'message' => $message, 'title' => $title];
    }
    return flash_multiple($bulkMessages);
}

function flash_bulk_info($messages, $title = 'Information') {
    $bulkMessages = [];
    foreach ($messages as $message) {
        $bulkMessages[] = ['type' => 'info', 'message' => $message, 'title' => $title];
    }
    return flash_multiple($bulkMessages);
}

/**
 * ⏱️ TIMED MESSAGES
 */
function flash_temporary($type, $message, $seconds = 3) {
    return FlashMessage::add($type, $message, '', ['duration' => $seconds * 1000]);
}

function flash_permanent($type, $message, $title = '') {
    return FlashMessage::add($type, $message, $title, ['duration' => 0, 'dismissible' => true]);
}

function flash_delayed($type, $message, $title = '', $delaySeconds = 2) {
    // This would need JavaScript implementation for actual delay
    return FlashMessage::add($type, $message, $title, ['delay' => $delaySeconds * 1000]);
}

/**
 * 📍 POSITIONED MESSAGES
 */
function flash_top_left($type, $message, $title = '') {
    return FlashMessage::add($type, $message, $title, ['position' => 'top-left']);
}

function flash_top_center($type, $message, $title = '') {
    return FlashMessage::add($type, $message, $title, ['position' => 'top-center']);
}

function flash_top_right($type, $message, $title = '') {
    return FlashMessage::add($type, $message, $title, ['position' => 'top-right']);
}

function flash_bottom_left($type, $message, $title = '') {
    return FlashMessage::add($type, $message, $title, ['position' => 'bottom-left']);
}

function flash_bottom_center($type, $message, $title = '') {
    return FlashMessage::add($type, $message, $title, ['position' => 'bottom-center']);
}

function flash_bottom_right($type, $message, $title = '') {
    return FlashMessage::add($type, $message, $title, ['position' => 'bottom-right']);
}

function flash_center($type, $message, $title = '') {
    return FlashMessage::add($type, $message, $title, ['position' => 'center']);
}

/**
 * 🎨 STYLED MESSAGES
 */
function flash_minimal($type, $message, $title = '') {
    return FlashMessage::add($type, $message, $title, ['theme' => 'minimal']);
}

function flash_glassmorphism($type, $message, $title = '') {
    return FlashMessage::add($type, $message, $title, ['theme' => 'glassmorphism']);
}

function flash_neon($type, $message, $title = '') {
    return FlashMessage::add($type, $message, $title, ['theme' => 'neon']);
}

function flash_gradient($type, $message, $title = '') {
    return FlashMessage::add($type, $message, $title, ['theme' => 'gradient']);
}

/**
 * 🔧 UTILITY FUNCTIONS
 */
function flash_clear_all() {
    return FlashMessage::clear();
}

function flash_get_messages() {
    return FlashMessage::get();
}

function flash_get_history() {
    return FlashMessage::getHistory();
}

function flash_get_analytics() {
    return FlashMessage::getAnalytics();
}

function flash_count() {
    return count(FlashMessage::get());
}

function flash_has_messages() {
    return flash_count() > 0;
}

/**
 * 🎯 CONFIRMATION & PROMPT FUNCTIONS
 */
function flash_confirm($message, $title = 'Confirm', $onConfirm = '', $onCancel = '') {
    return FlashMessage::add('confirm', $message, $title, [
        'type' => 'confirmation',
        'onConfirm' => $onConfirm,
        'onCancel' => $onCancel,
        'dismissible' => false
    ]);
}

function flash_prompt($message, $title = 'Input Required', $onSubmit = '') {
    return FlashMessage::add('prompt', $message, $title, [
        'type' => 'input',
        'onSubmit' => $onSubmit,
        'dismissible' => false
    ]);
}

function flash_loading($message = 'Loading...', $title = '') {
    return FlashMessage::add('loading', $message, $title, [
        'type' => 'loading',
        'duration' => 0,
        'dismissible' => false,
        'showSpinner' => true
    ]);
}

function flash_progress($message, $title = '', $percentage = 0) {
    return FlashMessage::add('progress', $message, $title, [
        'type' => 'progress',
        'duration' => 0,
        'dismissible' => false,
        'progress' => $percentage
    ]);
}

/**
 * 🌟 SPECIAL EFFECT FUNCTIONS
 */
function flash_rainbow($message, $title = 'Special!') {
    return FlashMessage::add('success', $message, $title, [
        'enableRainbow' => true,
        'enableConfetti' => true,
        'duration' => 8000
    ]);
}

function flash_fireworks($message, $title = 'Celebration!') {
    return FlashMessage::add('success', $message, $title, [
        'enableFireworks' => true,
        'enableConfetti' => true,
        'duration' => 10000
    ]);
}

function flash_particles($message, $title = 'Amazing!') {
    return FlashMessage::add('success', $message, $title, [
        'enableParticles' => true,
        'duration' => 6000
    ]);
}

/**
 * 🎵 SOUND FUNCTIONS
 */
function flash_silent($type, $message, $title = '') {
    return FlashMessage::add($type, $message, $title, ['playSound' => false]);
}

function flash_with_sound($type, $message, $title = '', $soundFile = '') {
    $options = ['playSound' => true];
    if ($soundFile) $options['customSound'] = $soundFile;
    return FlashMessage::add($type, $message, $title, $options);
}

// =============================================================================
// 🎯 RENDER SYSTEM
// =============================================================================

/**
 * 🎨 RENDER FLASH MESSAGES
 * This function outputs the HTML, CSS, and JavaScript for the flash messages
 */
function render_flash_messages() {
    // Get messages
    $messages = FlashMessage::get();
    $config = FlashMessagesConfig::getJSConfig();

    // Include the main flash messages component
    include __DIR__ . '/flash-messages.php';
}

// Auto-initialize if not already done
FlashMessage::init();
