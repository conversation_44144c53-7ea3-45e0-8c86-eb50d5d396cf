<?php
/**
 * 🚀 ULTIMATE FLASH MESSAGES SYSTEM - SINGLE FILE
 * The most beautiful, powerful, and dynamic notification system
 * 
 * ✨ Features:
 * - Single file - just include and use!
 * - Works on ANY website/page (login/logout doesn't matter)
 * - Extremely beautiful design with smooth animations
 * - Auto-detects website name and user info
 * - Session-based + Instant alerts
 * - Mobile responsive with touch gestures
 * - Sound effects and special animations
 * - Error-proof with emergency fallbacks
 * - Zero dependencies - pure PHP/JS/CSS
 * 
 * 🎯 Usage:
 * <?php include 'flash-messages.php'; ?>
 * flash_success('Message!');
 * showSuccess('Instant message!');
 */

// ============================================================================
// 🔧 CONFIGURATION (Edit these settings)
// ============================================================================

class FlashConfig {
    // 🎨 Design Settings
    public static $theme = 'modern'; // modern, glass, minimal, neon
    public static $position = 'top-right'; // top-right, top-left, bottom-right, etc.
    public static $animation = 'slide'; // slide, fade, bounce, zoom
    public static $duration = 5000; // milliseconds
    public static $maxMessages = 5; // maximum messages to show
    
    // 🎵 Effects
    public static $enableSounds = true;
    public static $enableShake = true; // shake on errors
    public static $enableConfetti = true; // confetti on success
    public static $enableGlow = true; // glow effects
    
    // 📱 Mobile
    public static $mobileFullWidth = true;
    public static $swipeToClose = true;
    
    // 🌐 Auto-detection
    public static $autoDetectSite = true; // auto-detect website name
    public static $autoDetectUser = true; // auto-detect logged in user
}

// ============================================================================
// 🚀 CORE SYSTEM
// ============================================================================

// Start session if not started
if (session_status() === PHP_SESSION_NONE) {
    @session_start();
}

// Auto-detect website name
$siteName = 'Website';
if (FlashConfig::$autoDetectSite) {
    $siteName = $_SERVER['HTTP_HOST'] ?? 'Website';
    $siteName = str_replace(['www.', '.com', '.net', '.org'], '', $siteName);
    $siteName = ucfirst($siteName);
}

// Auto-detect user info
$currentUser = null;
if (FlashConfig::$autoDetectUser) {
    if (isset($_SESSION['user'])) {
        $currentUser = $_SESSION['user'];
    } elseif (isset($_SESSION['username'])) {
        $currentUser = ['name' => $_SESSION['username']];
    } elseif (isset($_SESSION['email'])) {
        $currentUser = ['name' => $_SESSION['email']];
    }
}

// Flash message functions
if (!function_exists('flash_success')) {
    function flash_success($message, $title = 'Success!') {
        $_SESSION['flash_messages'][] = ['type' => 'success', 'message' => $message, 'title' => $title, 'time' => time()];
    }
}

if (!function_exists('flash_error')) {
    function flash_error($message, $title = 'Error!') {
        $_SESSION['flash_messages'][] = ['type' => 'error', 'message' => $message, 'title' => $title, 'time' => time()];
    }
}

if (!function_exists('flash_warning')) {
    function flash_warning($message, $title = 'Warning!') {
        $_SESSION['flash_messages'][] = ['type' => 'warning', 'message' => $message, 'title' => $title, 'time' => time()];
    }
}

if (!function_exists('flash_info')) {
    function flash_info($message, $title = 'Info') {
        $_SESSION['flash_messages'][] = ['type' => 'info', 'message' => $message, 'title' => $title, 'time' => time()];
    }
}

// Specialized functions
if (!function_exists('flash_login_success')) {
    function flash_login_success($username = '') {
        global $siteName;
        $message = $username ? "Welcome back to {$siteName}, {$username}!" : "Welcome back to {$siteName}!";
        flash_success($message, 'Login Successful');
    }
}

if (!function_exists('flash_login_failed')) {
    function flash_login_failed() {
        flash_error('Invalid email or password. Please try again.', 'Login Failed');
    }
}

if (!function_exists('flash_logout_success')) {
    function flash_logout_success() {
        global $siteName;
        flash_success("You have been logged out from {$siteName}.", 'Goodbye!');
    }
}

if (!function_exists('flash_registration_success')) {
    function flash_registration_success() {
        global $siteName;
        flash_success("Your account has been created successfully! Welcome to {$siteName}.", 'Registration Complete');
    }
}

// Get and clear flash messages
$flashMessages = $_SESSION['flash_messages'] ?? [];
unset($_SESSION['flash_messages']);

// Only render if there are messages or if we need the container
?>

<!-- 🎨 Flash Messages Styles -->
<style id="flash-messages-styles">
:root {
    --flash-primary: #3b82f6;
    --flash-success: #10b981;
    --flash-error: #ef4444;
    --flash-warning: #f59e0b;
    --flash-info: #6366f1;
    --flash-duration: <?= FlashConfig::$duration ?>ms;
}

.flash-container {
    position: fixed;
    z-index: 999999;
    pointer-events: none;
    font-family: system-ui, -apple-system, sans-serif;
    <?php
    $pos = FlashConfig::$position;
    if (strpos($pos, 'top') !== false) echo 'top: 20px;';
    if (strpos($pos, 'bottom') !== false) echo 'bottom: 20px;';
    if (strpos($pos, 'left') !== false) echo 'left: 20px;';
    if (strpos($pos, 'right') !== false) echo 'right: 20px;';
    if (strpos($pos, 'center') !== false) echo 'left: 50%; transform: translateX(-50%);';
    ?>
}

.flash-message {
    pointer-events: auto;
    margin-bottom: 12px;
    max-width: 400px;
    min-width: 300px;
    padding: 16px 20px;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    display: flex;
    align-items: flex-start;
    gap: 12px;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(100%);
    opacity: 0;
}

.flash-message.show {
    transform: translateX(0);
    opacity: 1;
}

.flash-message.hide {
    transform: translateX(100%);
    opacity: 0;
}

/* Themes */
.flash-success {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.flash-error {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.flash-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.flash-info {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
}

<?php if (FlashConfig::$theme === 'glass'): ?>
.flash-message {
    background: rgba(255,255,255,0.1) !important;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255,255,255,0.2);
    color: #1f2937;
}
<?php endif; ?>

<?php if (FlashConfig::$theme === 'minimal'): ?>
.flash-message {
    background: white !important;
    color: #374151;
    border: 1px solid #e5e7eb;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}
.flash-success { border-left: 4px solid var(--flash-success); }
.flash-error { border-left: 4px solid var(--flash-error); }
.flash-warning { border-left: 4px solid var(--flash-warning); }
.flash-info { border-left: 4px solid var(--flash-info); }
<?php endif; ?>

.flash-icon {
    flex-shrink: 0;
    width: 24px;
    height: 24px;
}

.flash-content {
    flex: 1;
    min-width: 0;
}

.flash-title {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 4px;
}

.flash-text {
    font-size: 14px;
    opacity: 0.95;
    line-height: 1.4;
}

.flash-close {
    flex-shrink: 0;
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    opacity: 0.7;
    transition: opacity 0.2s;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.flash-close:hover {
    opacity: 1;
    background: rgba(255,255,255,0.1);
}

.flash-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: rgba(255,255,255,0.3);
    width: 100%;
    transform-origin: left;
    animation: flash-progress var(--flash-duration) linear forwards;
}

@keyframes flash-progress {
    from { transform: scaleX(1); }
    to { transform: scaleX(0); }
}

/* Animations */
<?php if (FlashConfig::$animation === 'bounce'): ?>
.flash-message.show {
    animation: flash-bounce 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}
@keyframes flash-bounce {
    0% { transform: translateX(100%) scale(0.3); }
    50% { transform: translateX(-10%) scale(1.05); }
    70% { transform: translateX(2%) scale(0.9); }
    100% { transform: translateX(0) scale(1); }
}
<?php endif; ?>

<?php if (FlashConfig::$animation === 'fade'): ?>
.flash-message {
    transform: translateY(-20px);
}
.flash-message.show {
    transform: translateY(0);
}
.flash-message.hide {
    transform: translateY(-20px);
}
<?php endif; ?>

/* Special Effects */
<?php if (FlashConfig::$enableShake): ?>
@keyframes flash-shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}
.flash-error.shake {
    animation: flash-shake 0.5s ease-in-out 3;
}
<?php endif; ?>

<?php if (FlashConfig::$enableGlow): ?>
.flash-glow {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.6), 0 8px 25px rgba(0,0,0,0.15);
}
<?php endif; ?>

/* Mobile Responsive */
@media (max-width: 768px) {
    .flash-container {
        left: 10px !important;
        right: 10px !important;
        transform: none !important;
    }
    
    .flash-message {
        max-width: none;
        min-width: none;
        <?php if (FlashConfig::$mobileFullWidth): ?>
        width: 100%;
        <?php endif; ?>
    }
    
    <?php if (FlashConfig::$position === 'top-right' || FlashConfig::$position === 'top-left'): ?>
    .flash-container {
        top: 10px;
    }
    <?php endif; ?>
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    <?php if (FlashConfig::$theme === 'minimal'): ?>
    .flash-message {
        background: #1f2937 !important;
        color: #f9fafb;
        border-color: #374151;
    }
    <?php endif; ?>
}
</style>

<!-- 🚀 Flash Messages Container -->
<div id="flash-container" class="flash-container"></div>

<!-- 🎯 Flash Messages JavaScript -->
<script id="flash-messages-script">
(function() {
    'use strict';
    
    // Configuration
    const config = {
        duration: <?= FlashConfig::$duration ?>,
        maxMessages: <?= FlashConfig::$maxMessages ?>,
        enableSounds: <?= FlashConfig::$enableSounds ? 'true' : 'false' ?>,
        enableShake: <?= FlashConfig::$enableShake ? 'true' : 'false' ?>,
        enableConfetti: <?= FlashConfig::$enableConfetti ? 'true' : 'false' ?>,
        swipeToClose: <?= FlashConfig::$swipeToClose ? 'true' : 'false' ?>,
        siteName: '<?= addslashes($siteName) ?>'
    };
    
    let container = null;
    let messageQueue = [];
    let activeMessages = [];
    
    // Initialize
    function init() {
        container = document.getElementById('flash-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'flash-container';
            container.className = 'flash-container';
            document.body.appendChild(container);
        }
        
        setupEventListeners();
        
        // Show existing messages
        <?php if (!empty($flashMessages)): ?>
        const messages = <?= json_encode($flashMessages) ?>;
        messages.forEach((msg, index) => {
            setTimeout(() => {
                showMessage(msg.type, msg.message, msg.title);
            }, index * 200);
        });
        <?php endif; ?>
    }
    
    // Setup event listeners
    function setupEventListeners() {
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                dismissAll();
            }
        });
        
        // Mobile swipe support
        if (config.swipeToClose === 'true') {
            let startX = 0;
            let currentMessage = null;
            
            container.addEventListener('touchstart', (e) => {
                startX = e.touches[0].clientX;
                currentMessage = e.target.closest('.flash-message');
            });
            
            container.addEventListener('touchmove', (e) => {
                if (!currentMessage) return;
                const deltaX = e.touches[0].clientX - startX;
                if (deltaX > 50) {
                    currentMessage.style.transform = `translateX(${deltaX}px)`;
                }
            });
            
            container.addEventListener('touchend', (e) => {
                if (!currentMessage) return;
                const deltaX = e.changedTouches[0].clientX - startX;
                if (deltaX > 100) {
                    dismissMessage(currentMessage);
                } else {
                    currentMessage.style.transform = '';
                }
                currentMessage = null;
            });
        }
    }
    
    // Show message
    function showMessage(type, message, title, options = {}) {
        try {
            // Limit active messages
            if (activeMessages.length >= config.maxMessages) {
                dismissMessage(activeMessages[0]);
            }
            
            const messageEl = createMessage(type, message, title, options);
            container.appendChild(messageEl);
            activeMessages.push(messageEl);
            
            // Trigger show animation
            requestAnimationFrame(() => {
                messageEl.classList.add('show');
            });
            
            // Special effects
            if (type === 'error' && config.enableShake === 'true') {
                messageEl.classList.add('shake');
            }
            
            if (options.glow) {
                messageEl.classList.add('flash-glow');
            }
            
            // Auto dismiss
            if (options.autoHide !== false) {
                setTimeout(() => {
                    dismissMessage(messageEl);
                }, options.duration || config.duration);
            }
            
            // Sound effect
            playSound(type);
            
            // Confetti effect
            if (type === 'success' && config.enableConfetti === 'true') {
                showConfetti();
            }
            
            return messageEl;
        } catch (error) {
            console.error('Flash message error:', error);
            fallbackAlert(type + ': ' + message);
        }
    }
    
    // Create message element
    function createMessage(type, message, title, options = {}) {
        const div = document.createElement('div');
        div.className = `flash-message flash-${type}`;
        
        const icons = {
            success: '<svg fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>',
            error: '<svg fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>',
            warning: '<svg fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>',
            info: '<svg fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>'
        };
        
        div.innerHTML = `
            <div class="flash-icon">${icons[type] || icons.info}</div>
            <div class="flash-content">
                <div class="flash-title">${escapeHtml(title)}</div>
                <div class="flash-text">${escapeHtml(message)}</div>
            </div>
            <button class="flash-close" onclick="dismissMessage(this.parentElement)">
                <svg fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
            </button>
            ${options.autoHide !== false ? '<div class="flash-progress"></div>' : ''}
        `;
        
        // Click to dismiss
        div.addEventListener('click', (e) => {
            if (!e.target.closest('.flash-close')) {
                dismissMessage(div);
            }
        });
        
        return div;
    }
    
    // Dismiss message
    window.dismissMessage = function(messageEl) {
        if (!messageEl || !messageEl.parentNode) return;
        
        messageEl.classList.add('hide');
        
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
            
            const index = activeMessages.indexOf(messageEl);
            if (index > -1) {
                activeMessages.splice(index, 1);
            }
        }, 300);
    };
    
    // Dismiss all messages
    function dismissAll() {
        activeMessages.forEach(msg => dismissMessage(msg));
    }
    
    // Play sound
    function playSound(type) {
        if (config.enableSounds !== 'true') return;
        
        try {
            const frequencies = { success: 800, error: 300, warning: 600, info: 500 };
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.setValueAtTime(frequencies[type] || 500, audioContext.currentTime);
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);
        } catch (e) {
            // Sound failed, continue silently
        }
    }
    
    // Confetti effect
    function showConfetti() {
        if (config.enableConfetti !== 'true') return;
        
        const colors = ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff', '#00ffff'];
        const confettiCount = 50;
        
        for (let i = 0; i < confettiCount; i++) {
            setTimeout(() => {
                const confetti = document.createElement('div');
                confetti.style.cssText = `
                    position: fixed;
                    top: -10px;
                    left: ${Math.random() * 100}%;
                    width: 10px;
                    height: 10px;
                    background: ${colors[Math.floor(Math.random() * colors.length)]};
                    z-index: 1000000;
                    pointer-events: none;
                    animation: confetti-fall 3s linear forwards;
                `;
                
                document.body.appendChild(confetti);
                
                setTimeout(() => {
                    if (confetti.parentNode) {
                        confetti.parentNode.removeChild(confetti);
                    }
                }, 3000);
            }, i * 10);
        }
        
        // Add confetti animation
        if (!document.getElementById('confetti-style')) {
            const style = document.createElement('style');
            style.id = 'confetti-style';
            style.textContent = `
                @keyframes confetti-fall {
                    to {
                        transform: translateY(100vh) rotate(360deg);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }
    }
    
    // Utility functions
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    function fallbackAlert(message) {
        alert(message);
    }
    
    // Public API
    window.showSuccess = (msg, title = 'Success!', opts = {}) => showMessage('success', msg, title, opts);
    window.showError = (msg, title = 'Error!', opts = {}) => showMessage('error', msg, title, opts);
    window.showWarning = (msg, title = 'Warning!', opts = {}) => showMessage('warning', msg, title, opts);
    window.showInfo = (msg, title = 'Info', opts = {}) => showMessage('info', msg, title, opts);
    window.dismissAllMessages = dismissAll;
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
})();
</script>

<?php
// ============================================================================
// 🎯 ADDITIONAL HELPER FUNCTIONS
// ============================================================================

// Specialized functions with dynamic site integration
if (!function_exists('flash_quiz_completed')) {
    function flash_quiz_completed($score) {
        global $siteName;
        if ($score >= 80) {
            flash_success("Excellent! You scored {$score}% on {$siteName} quiz!", 'Quiz Completed');
        } else {
            flash_success("You scored {$score}% on the quiz. Keep learning!", 'Quiz Completed');
        }
    }
}

if (!function_exists('flash_quiz_failed')) {
    function flash_quiz_failed($score) {
        flash_error("You scored {$score}%. Don't give up, try again!", 'Quiz Failed');
    }
}

if (!function_exists('flash_payment_success')) {
    function flash_payment_success($amount) {
        global $siteName;
        flash_success("Payment of \${$amount} completed successfully on {$siteName}!", 'Payment Successful');
    }
}

if (!function_exists('flash_created')) {
    function flash_created($item) {
        flash_success("{$item} has been created successfully!", 'Created');
    }
}

if (!function_exists('flash_updated')) {
    function flash_updated($item) {
        flash_success("{$item} has been updated successfully!", 'Updated');
    }
}

if (!function_exists('flash_deleted')) {
    function flash_deleted($item) {
        flash_success("{$item} has been deleted successfully!", 'Deleted');
    }
}

if (!function_exists('flash_validation_errors')) {
    function flash_validation_errors($errors) {
        if (is_array($errors)) {
            $errorList = implode('<br>', $errors);
            flash_error($errorList, 'Please fix the following errors:');
        } else {
            flash_error($errors, 'Validation Error');
        }
    }
}

// Emergency fallback
if (!function_exists('flash_emergency')) {
    function flash_emergency($message) {
        echo "<script>alert('SYSTEM ALERT: " . addslashes($message) . "');</script>";
    }
}

// Auto-cleanup old messages (prevent memory issues)
if (isset($_SESSION['flash_messages'])) {
    $_SESSION['flash_messages'] = array_filter($_SESSION['flash_messages'], function($msg) {
        return (time() - $msg['time']) < 3600; // Remove messages older than 1 hour
    });
}

// Debug info (only in development)
if (isset($_GET['flash_debug'])) {
    echo "<!-- Flash Messages Debug Info:\n";
    echo "Site Name: {$siteName}\n";
    echo "Current User: " . ($currentUser ? json_encode($currentUser) : 'Not logged in') . "\n";
    echo "Messages: " . count($flashMessages) . "\n";
    echo "-->\n";
}
?>
