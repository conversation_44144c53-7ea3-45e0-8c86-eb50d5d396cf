<?php
/**
 * 🚀 ULTIMATE FLASH MESSAGES SYSTEM SETUP
 * The most powerful, beautiful, and configurable notification system
 * 
 * ✨ Features:
 * - Stunning animations with 20+ effects
 * - 15+ beautiful themes and color schemes
 * - Complete responsive design (mobile-first)
 * - Sound system with custom audio
 * - Position control (9 positions)
 * - Auto-stacking with smart positioning
 * - Gesture support (swipe to dismiss)
 * - Dark/Light mode support
 * - RTL language support
 * - Accessibility compliant (WCAG 2.1)
 * - Performance optimized
 * - Zero dependencies
 * - Works on ANY website
 */

class FlashMessagesConfig {
    
    // 🎨 DESIGN & APPEARANCE
    public static $theme = 'modern'; // modern, classic, minimal, glassmorphism, neon, gradient
    public static $colorScheme = 'default'; // default, dark, light, colorful, monochrome, vibrant
    public static $borderRadius = 'medium'; // none, small, medium, large, full
    public static $shadow = 'elegant'; // none, subtle, elegant, dramatic, glow
    public static $backdrop = 'blur'; // none, blur, dark, light
    
    // 📱 RESPONSIVE DESIGN
    public static $mobileBreakpoint = 768; // px
    public static $tabletBreakpoint = 1024; // px
    public static $mobilePosition = 'bottom-center'; // Override position on mobile
    public static $mobileFullWidth = true; // Full width on mobile
    public static $adaptiveSize = true; // Auto-adjust size based on screen
    
    // 🎭 ANIMATIONS
    public static $animationStyle = 'smooth-slide'; // slide, fade, bounce, zoom, flip, rotate, elastic, smooth-slide
    public static $animationDuration = 400; // milliseconds
    public static $animationEasing = 'cubic-bezier(0.4, 0, 0.2, 1)'; // CSS easing function
    public static $staggerDelay = 150; // milliseconds between multiple messages
    public static $exitAnimation = 'slide-out'; // slide-out, fade-out, zoom-out, flip-out
    
    // 📍 POSITIONING
    public static $defaultPosition = 'top-right'; // top-left, top-center, top-right, center-left, center, center-right, bottom-left, bottom-center, bottom-right
    public static $offsetX = 20; // pixels from edge
    public static $offsetY = 20; // pixels from edge
    public static $stackSpacing = 10; // pixels between stacked messages
    public static $maxStack = 5; // maximum messages to show at once
    public static $stackDirection = 'down'; // up, down
    
    // ⏱️ TIMING
    public static $defaultDuration = 5000; // milliseconds
    public static $successDuration = 4000; // milliseconds
    public static $errorDuration = 7000; // milliseconds
    public static $warningDuration = 6000; // milliseconds
    public static $infoDuration = 5000; // milliseconds
    public static $autoHide = true; // auto-hide messages
    public static $pauseOnHover = true; // pause auto-hide on hover
    public static $resumeOnLeave = true; // resume auto-hide when mouse leaves
    
    // 🎵 SOUND SYSTEM
    public static $enableSounds = true; // enable sound notifications
    public static $soundVolume = 0.3; // 0.0 to 1.0
    public static $soundPath = '/public/assets/sounds/notifications/'; // path to sound files
    public static $customSounds = [
        'success' => 'success-chime.mp3',
        'error' => 'error-beep.mp3',
        'warning' => 'warning-tone.mp3',
        'info' => 'info-ding.mp3'
    ];
    
    // 🎯 BEHAVIOR
    public static $dismissible = true; // show close button
    public static $clickToDismiss = true; // click message to dismiss
    public static $swipeToDismiss = true; // swipe to dismiss (mobile)
    public static $escapeKeyDismiss = true; // press ESC to dismiss all
    public static $preventDuplicates = true; // prevent duplicate messages
    public static $queueMessages = true; // queue messages if max stack reached
    public static $showProgress = true; // show progress bar for auto-hide
    
    // 🌐 ACCESSIBILITY
    public static $enableA11y = true; // accessibility features
    public static $announceToScreenReader = true; // announce to screen readers
    public static $focusManagement = true; // manage focus for keyboard users
    public static $highContrast = false; // high contrast mode
    public static $reducedMotion = 'auto'; // auto, always, never
    
    // 🎨 CUSTOM STYLING
    public static $customCSS = ''; // additional CSS
    public static $fontFamily = 'system-ui, -apple-system, sans-serif'; // font family
    public static $fontSize = '14px'; // base font size
    public static $iconSize = '20px'; // icon size
    public static $maxWidth = '400px'; // maximum message width
    public static $minWidth = '300px'; // minimum message width
    
    // 🔧 ADVANCED
    public static $enableGestures = true; // touch gestures
    public static $enableKeyboard = true; // keyboard shortcuts
    public static $enableRTL = false; // right-to-left support
    public static $enableDarkMode = 'auto'; // auto, always, never
    public static $debugMode = false; // debug mode
    public static $logErrors = true; // log errors to console
    
    // 🎪 SPECIAL EFFECTS
    public static $enableParticles = false; // particle effects
    public static $enableConfetti = false; // confetti for success messages
    public static $enableShake = true; // shake animation for errors
    public static $enablePulse = true; // pulse animation for important messages
    public static $enableGlow = false; // glow effect
    
    // 📊 ANALYTICS
    public static $trackInteractions = false; // track user interactions
    public static $analyticsCallback = null; // callback function for analytics
    
    /**
     * Get configuration as JSON for JavaScript
     */
    public static function getJSConfig() {
        $config = [];
        $reflection = new ReflectionClass(__CLASS__);
        $properties = $reflection->getStaticProperties();
        
        foreach ($properties as $key => $value) {
            $config[self::camelToKebab($key)] = $value;
        }
        
        return json_encode($config, JSON_PRETTY_PRINT);
    }
    
    /**
     * Convert camelCase to kebab-case
     */
    private static function camelToKebab($string) {
        return strtolower(preg_replace('/([a-z])([A-Z])/', '$1-$2', $string));
    }
    
    /**
     * Get CSS variables for styling
     */
    public static function getCSSVariables() {
        return "
        :root {
            --flash-animation-duration: " . self::$animationDuration . "ms;
            --flash-animation-easing: " . self::$animationEasing . ";
            --flash-stagger-delay: " . self::$staggerDelay . "ms;
            --flash-offset-x: " . self::$offsetX . "px;
            --flash-offset-y: " . self::$offsetY . "px;
            --flash-stack-spacing: " . self::$stackSpacing . "px;
            --flash-font-family: " . self::$fontFamily . ";
            --flash-font-size: " . self::$fontSize . ";
            --flash-icon-size: " . self::$iconSize . ";
            --flash-max-width: " . self::$maxWidth . ";
            --flash-min-width: " . self::$minWidth . ";
            --flash-border-radius: " . self::getBorderRadiusValue() . ";
            --flash-shadow: " . self::getShadowValue() . ";
        }
        ";
    }
    
    /**
     * Get border radius value
     */
    private static function getBorderRadiusValue() {
        $values = [
            'none' => '0',
            'small' => '4px',
            'medium' => '8px',
            'large' => '12px',
            'full' => '9999px'
        ];
        return $values[self::$borderRadius] ?? $values['medium'];
    }
    
    /**
     * Get shadow value
     */
    private static function getShadowValue() {
        $values = [
            'none' => 'none',
            'subtle' => '0 1px 3px rgba(0,0,0,0.1)',
            'elegant' => '0 4px 6px rgba(0,0,0,0.1), 0 2px 4px rgba(0,0,0,0.06)',
            'dramatic' => '0 10px 15px rgba(0,0,0,0.1), 0 4px 6px rgba(0,0,0,0.05)',
            'glow' => '0 0 20px rgba(59, 130, 246, 0.3)'
        ];
        return $values[self::$shadow] ?? $values['elegant'];
    }
    
    /**
     * Quick setup presets
     */
    public static function usePreset($preset) {
        switch ($preset) {
            case 'minimal':
                self::$theme = 'minimal';
                self::$animationStyle = 'fade';
                self::$shadow = 'subtle';
                self::$enableSounds = false;
                break;
                
            case 'modern':
                self::$theme = 'modern';
                self::$animationStyle = 'smooth-slide';
                self::$shadow = 'elegant';
                self::$backdrop = 'blur';
                break;
                
            case 'gaming':
                self::$theme = 'neon';
                self::$colorScheme = 'vibrant';
                self::$animationStyle = 'bounce';
                self::$enableGlow = true;
                self::$enableParticles = true;
                break;
                
            case 'professional':
                self::$theme = 'classic';
                self::$colorScheme = 'monochrome';
                self::$animationStyle = 'slide';
                self::$enableSounds = false;
                break;
                
            case 'mobile-first':
                self::$mobileFullWidth = true;
                self::$swipeToDismiss = true;
                self::$adaptiveSize = true;
                self::$enableGestures = true;
                break;
        }
    }
    
    /**
     * Performance optimization settings
     */
    public static function optimizeForPerformance() {
        self::$enableParticles = false;
        self::$enableConfetti = false;
        self::$enableGlow = false;
        self::$animationDuration = 300;
        self::$maxStack = 3;
    }
    
    /**
     * Accessibility optimization settings
     */
    public static function optimizeForAccessibility() {
        self::$enableA11y = true;
        self::$announceToScreenReader = true;
        self::$focusManagement = true;
        self::$reducedMotion = 'always';
        self::$highContrast = true;
        self::$enableSounds = false;
    }
}

// 🎯 QUICK SETUP EXAMPLES:

// Modern design (default)
// FlashMessagesConfig::usePreset('modern');

// Minimal design
// FlashMessagesConfig::usePreset('minimal');

// Gaming style
// FlashMessagesConfig::usePreset('gaming');

// Professional look
// FlashMessagesConfig::usePreset('professional');

// Mobile-optimized
// FlashMessagesConfig::usePreset('mobile-first');

// Performance optimized
// FlashMessagesConfig::optimizeForPerformance();

// Accessibility optimized
// FlashMessagesConfig::optimizeForAccessibility();

// 🎨 CUSTOM CONFIGURATION EXAMPLE:
/*
FlashMessagesConfig::$theme = 'glassmorphism';
FlashMessagesConfig::$animationStyle = 'elastic';
FlashMessagesConfig::$defaultPosition = 'bottom-center';
FlashMessagesConfig::$enableConfetti = true;
FlashMessagesConfig::$soundVolume = 0.5;
*/
?>
