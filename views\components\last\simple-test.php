<?php
// 🧪 Simple Test - Just include and use!
include 'flash-messages.php';

// Test different message types
flash_success('Success!', 'This is a success message');
flash_error('Error!', 'This is an error message');
flash_warning('Warning!', 'This is a warning message');
flash_info('Info!', 'This is an info message');

// Test toast
flash_toast_success('Toast Success!');

// Test confirmation
flash_delete('test item', 'alert("Deleted!")', 'alert("Cancelled!")');
?>

<!DOCTYPE html>
<html>
<head>
    <title>Simple Flash Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            background: #f0f0f0; 
            padding: 50px; 
            text-align: center; 
        }
        .test-box {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            max-width: 500px;
            margin: 0 auto;
        }
        h1 { color: #333; }
        p { color: #666; }
        .btn {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #005a87; }
    </style>
</head>
<body>
    <div class="test-box">
        <h1>🚀 Flash Messages Working!</h1>
        <p>Check the notifications that appeared automatically.</p>
        <p>The system is working perfectly with zero setup!</p>
        
        <button class="btn" onclick="flash_success_js()">JS Success</button>
        <button class="btn" onclick="flash_confirm_js()">JS Confirm</button>
        <button class="btn" onclick="location.reload()">Test Again</button>
        
        <script>
            function flash_success_js() {
                FlashJS.fire({
                    icon: 'success',
                    title: 'JavaScript Success!',
                    text: 'This was triggered from JavaScript'
                });
            }
            
            function flash_confirm_js() {
                FlashJS.fire({
                    icon: 'question',
                    title: 'JavaScript Confirm?',
                    text: 'Do you want to continue?',
                    showCancelButton: true,
                    confirmButtonText: 'Yes!',
                    cancelButtonText: 'No',
                    onConfirm: () => {
                        FlashJS.fire({
                            icon: 'success',
                            title: 'Confirmed!',
                            text: 'You clicked Yes!'
                        });
                    }
                });
            }
        </script>
    </div>
</body>
</html>
