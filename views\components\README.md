# JobSpace Dynamic Components System

A comprehensive, reusable, and dynamic component library for the JobSpace project built with PHP, Tailwind CSS, and JavaScript.

## 🎯 Overview

This component system provides a modular, maintainable, and scalable approach to building UI elements across the JobSpace platform. All components are designed to be:

- **Dynamic**: Data-driven with flexible parameters
- **Reusable**: Can be used across different pages and contexts
- **Responsive**: Mobile-first design with Tailwind CSS
- **Accessible**: Following web accessibility best practices
- **Consistent**: Unified design language and behavior

## 📁 Component Structure

```
views/components/
├── alerts/              # Alert & notification components
├── buttons/             # Button components
├── cards/               # Card components
├── forms/               # Form field components
├── modals/              # Modal dialog components
├── navbar/              # Navigation components
├── tables/              # Table & data display components
├── feed/                # Feed item components
├── quiz/                # Quiz-specific components
├── social/              # Social media components
├── ecommerce/           # E-commerce components
├── freelance/           # Freelance marketplace components
├── wallet/              # Payment & wallet components
├── dashboard/           # Dashboard widget components
├── admin/               # Admin panel components
├── layout/              # Layout components
├── media/               # Media handling components
├── utils/               # Utility components
├── widgets/             # Widget components
└── component-helper.php # Helper functions
```

## 🚀 Quick Start

### 1. Include the Helper

```php
// Include the component helper
require_once 'views/components/component-helper.php';
```

### 2. Basic Usage

```php
// Load a component
include_component('alerts/success', [
    'message' => 'User created successfully!',
    'title' => 'Success',
    'autoHide' => true
]);

// Or use the shorthand function
component('buttons/primary', [
    'text' => 'Save Changes',
    'type' => 'submit',
    'size' => 'lg'
]);
```

## 📚 Component Documentation

### Alerts (`alerts/`)

Display notifications and messages to users.

**Available Components:**
- `success.php` - Success messages
- `error.php` - Error messages  
- `warning.php` - Warning messages
- `info.php` - Information messages

**Usage:**
```php
// Success alert
show_alert('success', 'Operation completed successfully!', [
    'title' => 'Success',
    'autoHide' => true,
    'duration' => 5000
]);

// Error alert with custom options
include_component('alerts/error', [
    'message' => 'Please fix the errors below',
    'title' => 'Validation Error',
    'dismissible' => true,
    'size' => 'lg',
    'position' => 'top'
]);
```

### Buttons (`buttons/`)

Interactive button components with various styles and states.

**Available Components:**
- `primary.php` - Primary action buttons
- `secondary.php` - Secondary action buttons

**Usage:**
```php
// Primary button
echo render_button('primary', 'Save Changes', [
    'type' => 'submit',
    'size' => 'lg',
    'loading' => false,
    'icon' => '<svg>...</svg>',
    'onclick' => 'saveForm()'
]);

// Button as link
include_component('buttons/secondary', [
    'text' => 'View Profile',
    'href' => '/user/profile/123',
    'target' => '_blank',
    'size' => 'md'
]);
```

### Cards (`cards/`)

Display structured content in card format.

**Available Components:**
- `user-card.php` - User profile cards

**Usage:**
```php
// User card
include_component('cards/user-card', [
    'user' => [
        'id' => 123,
        'first_name' => 'John',
        'last_name' => 'Doe',
        'username' => 'johndoe',
        'email' => '<EMAIL>',
        'avatar' => 'avatar.jpg',
        'role' => 'user'
    ],
    'size' => 'md',
    'layout' => 'vertical',
    'showActions' => true,
    'showStats' => true
]);
```

### Forms (`forms/`)

Form input components with validation and styling.

**Available Components:**
- `input-field.php` - Text input fields

**Usage:**
```php
// Input field
render_form_field('input-field', [
    'name' => 'email',
    'type' => 'email',
    'label' => 'Email Address',
    'placeholder' => 'Enter your email',
    'required' => true,
    'value' => $user['email'] ?? '',
    'error' => $errors['email'] ?? '',
    'help' => 'We will never share your email'
]);
```

### Feed (`feed/`)

Display feed items for different content types.

**Available Components:**
- `feed-item.php` - Universal feed item component

**Usage:**
```php
// Quiz feed item
render_feed_item($quizData, 'quiz', [
    'showActions' => true,
    'showAuthor' => true,
    'size' => 'md'
]);

// Social post feed item
render_feed_item($postData, 'social', [
    'showTimestamp' => true
]);

// E-commerce product feed item
render_feed_item($productData, 'ecommerce');
```

### Utils (`utils/`)

Utility components for common UI patterns.

**Available Components:**
- `loading-spinner.php` - Loading indicators
- `empty-state.php` - Empty state displays

**Usage:**
```php
// Loading spinner
render_loading('lg', 'blue');

// Loading overlay
include_component('utils/loading-spinner', [
    'size' => 'lg',
    'text' => 'Processing...',
    'overlay' => true,
    'type' => 'circle'
]);

// Empty state
render_empty_state('No quizzes found', '', [
    [
        'text' => 'Create Quiz',
        'type' => 'primary',
        'href' => '/quiz/create',
        'icon' => '<svg>...</svg>'
    ]
]);
```

## 🎨 Customization

### Component Parameters

All components accept a `$data` array with parameters. Common parameters include:

- `size` - Component size (xs, sm, md, lg, xl)
- `class` - Additional CSS classes
- `id` - HTML ID attribute
- `attributes` - Additional HTML attributes
- `disabled` - Disable component
- `loading` - Show loading state

### Styling

Components use Tailwind CSS classes. You can:

1. **Override classes**: Pass custom classes via the `class` parameter
2. **Extend components**: Create new components based on existing ones
3. **Theme customization**: Modify Tailwind configuration

### JavaScript Integration

Components include necessary JavaScript for:

- Interactive behaviors
- Form validation
- AJAX functionality
- Event handling

## 🔧 Helper Functions

### Core Functions

```php
// Include component
include_component($component, $data, $return);

// Render multiple components
render_components($components, $return);

// Check if component exists
component_exists($component);

// Get available components
get_available_components($category);
```

### Shorthand Functions

```php
// Alerts
show_alert($type, $message, $options);

// Buttons
render_button($type, $text, $options);

// Cards
render_card($type, $data, $options);

// Forms
render_form_field($type, $data);

// Loading
render_loading($size, $color);

// Empty states
render_empty_state($message, $icon, $actions);
```

## 📱 Responsive Design

All components are built with mobile-first responsive design:

- **Mobile**: Single column, touch-friendly
- **Tablet**: Adaptive layouts, collapsible elements
- **Desktop**: Multi-column, hover states

## ♿ Accessibility

Components follow WCAG guidelines:

- Semantic HTML structure
- ARIA attributes
- Keyboard navigation
- Screen reader support
- Color contrast compliance

## 🧪 Testing

Test components by:

1. **Visual testing**: Check appearance across devices
2. **Functional testing**: Verify interactive behaviors
3. **Accessibility testing**: Use screen readers and keyboard navigation
4. **Performance testing**: Monitor load times and responsiveness

## 🚀 Performance

Components are optimized for:

- **Minimal CSS**: Only necessary styles
- **Efficient JavaScript**: Event delegation and lazy loading
- **Image optimization**: Responsive images and lazy loading
- **Caching**: Component output caching where appropriate

## 📈 Future Enhancements

Planned improvements:

- Component variants and themes
- Advanced form validation
- Real-time updates
- Animation and transitions
- Component composition patterns
- TypeScript definitions

## 🤝 Contributing

When adding new components:

1. Follow the existing naming convention
2. Include comprehensive documentation
3. Add usage examples
4. Ensure responsive design
5. Test accessibility
6. Update this README

## 📄 License

This component system is part of the JobSpace project and follows the same licensing terms.
