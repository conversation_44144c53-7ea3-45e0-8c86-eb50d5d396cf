<?php
// Immediate test - render right away
require_once __DIR__ . '/flash-messages.php';

// Add test message and render immediately
flash_success('Immediate test message!', 'Immediate Test');
flash_render_now();
?>
<!DOCTYPE html>
<html>
<head>
    <title>Immediate Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 50px; 
            background: #f0f0f0; 
        }
        .info {
            background: white;
            padding: 20px;
            border-radius: 10px;
            max-width: 500px;
            margin: 0 auto;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="info">
        <h1>🚀 Immediate Test</h1>
        <p>The flash message system has been rendered immediately.</p>
        <p>You should see a green success message appear.</p>
        
        <button onclick="manualTest()" style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 10px;">
            ✅ Manual Success Test
        </button>
        
        <button onclick="errorTest()" style="padding: 10px 20px; background: #dc3545; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 10px;">
            ❌ Manual Error Test
        </button>
        
        <button onclick="checkSystem()" style="padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 10px;">
            🔍 Check System
        </button>
    </div>

    <script>
        function manualTest() {
            if (typeof window.flashShow === 'function') {
                window.flashShow({
                    id: 'manual-success-' + Date.now(),
                    type: 'success',
                    message: 'Manual success message created!',
                    title: 'Manual Success',
                    options: { duration: 5000, dismissible: true, showIcon: true }
                });
            } else {
                alert('❌ flashShow function not available');
            }
        }
        
        function errorTest() {
            if (typeof window.flashShow === 'function') {
                window.flashShow({
                    id: 'manual-error-' + Date.now(),
                    type: 'error',
                    message: 'Manual error message created!',
                    title: 'Manual Error',
                    options: { duration: 7000, dismissible: true, showIcon: true }
                });
            } else {
                alert('❌ flashShow function not available');
            }
        }
        
        function checkSystem() {
            const container = document.getElementById('flash-container');
            const hasFlashShow = typeof window.flashShow === 'function';
            const hasFlashTest = typeof window.flashTest === 'function';
            
            let status = '🔍 System Check Results:\n\n';
            status += '📦 Container: ' + (container ? '✅ Found' : '❌ Missing') + '\n';
            status += '🔧 flashShow: ' + (hasFlashShow ? '✅ Available' : '❌ Missing') + '\n';
            status += '🧪 flashTest: ' + (hasFlashTest ? '✅ Available' : '❌ Missing') + '\n';
            
            if (container) {
                status += '📊 Container classes: ' + container.className + '\n';
                status += '📍 Container position: ' + getComputedStyle(container).position + '\n';
            }
            
            alert(status);
            console.log(status);
        }
        
        // Auto-check after 2 seconds
        setTimeout(() => {
            console.log('🔍 Auto-checking system...');
            const container = document.getElementById('flash-container');
            const hasFlashShow = typeof window.flashShow === 'function';
            
            if (container && hasFlashShow) {
                console.log('✅ System appears to be working!');
            } else {
                console.error('❌ System not working properly');
                console.log('Container:', container);
                console.log('flashShow:', hasFlashShow);
            }
        }, 2000);
    </script>
</body>
</html>
