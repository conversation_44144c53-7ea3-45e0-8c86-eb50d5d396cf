<?php
/**
 * Universal Flash Message Helper
 * The ONLY notification system you need for JobSpace
 *
 * Features:
 * - Session-based flash messages (survives redirects)
 * - Instant JavaScript alerts (immediate display)
 * - Auto-fallback for failed messages
 * - Emergency notifications when system fails
 * - Bulk message support
 * - Custom options and positioning
 * - Sound notifications
 * - Mobile-responsive design
 *
 * Usage Examples:
 *
 * // Basic usage (session-based, survives redirects)
 * flash_success('Login successful!');
 * flash_error('Invalid credentials!');
 * flash_warning('Please verify your email!');
 * flash_info('Welcome to JobSpace!');
 *
 * // Instant alerts (JavaScript, immediate display)
 * flash_instant('success', 'Saved!');
 * flash_instant('error', 'Failed to save!');
 *
 * // With custom options
 * flash_success('Quiz completed!', 'Congratulations!', [
 *     'autoHide' => false,
 *     'showTimestamp' => true,
 *     'position' => 'top-center',
 *     'duration' => 10000
 * ]);
 *
 * // Multiple messages
 * flash_multiple([
 *     ['type' => 'success', 'message' => 'Profile updated!'],
 *     ['type' => 'info', 'message' => 'Check your email for confirmation']
 * ]);
 *
 * // Emergency fallback (when all else fails)
 * flash_emergency('Critical system error occurred');
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * Set a flash message (session-based, survives redirects)
 *
 * @param string $type Message type (success, error, warning, info)
 * @param string $message Message text
 * @param string $title Optional title
 * @param array $options Additional options
 */
function flash_message($type, $message, $title = '', $options = []) {
    try {
        if (!isset($_SESSION['flash_messages'])) {
            $_SESSION['flash_messages'] = [];
        }

        $_SESSION['flash_messages'][] = [
            'type' => $type,
            'message' => $message,
            'title' => $title,
            'options' => $options,
            'timestamp' => time()
        ];
    } catch (Exception $e) {
        // Fallback to instant alert if session fails
        flash_instant($type, $message, $title);
    }
}

/**
 * Set instant JavaScript alert (immediate display, no session)
 *
 * @param string $type Message type
 * @param string $message Message text
 * @param string $title Optional title
 * @param array $options Additional options
 */
function flash_instant($type, $message, $title = '', $options = []) {
    echo "<script>
        document.addEventListener('DOMContentLoaded', function() {
            try {
                if (typeof addFlashMessage === 'function') {
                    addFlashMessage('{$type}', '" . addslashes($message) . "', '" . addslashes($title) . "', " . json_encode($options) . ");
                } else if (typeof showInstantAlert === 'function') {
                    showInstantAlert('{$type}', '" . addslashes($message) . "', '" . addslashes($title) . "');
                } else {
                    // Emergency fallback
                    showEmergencyMessage('" . addslashes($title ? $title . ': ' . $message : $message) . "');
                }
            } catch (error) {
                console.error('Flash instant failed:', error);
                alert('" . addslashes($title ? $title . ': ' . $message : $message) . "');
            }
        });
    </script>";
}

/**
 * Emergency flash message (when everything else fails)
 *
 * @param string $message Emergency message
 */
function flash_emergency($message) {
    echo "<div style='position:fixed;top:20px;right:20px;background:#dc2626;color:white;padding:15px;border-radius:8px;z-index:999999;max-width:300px;box-shadow:0 4px 6px rgba(0,0,0,0.3);font-family:system-ui,-apple-system,sans-serif;'>
        <strong>⚠️ System Alert:</strong><br>" . htmlspecialchars($message) . "
        <button onclick='this.parentElement.remove()' style='float:right;background:none;border:none;color:white;font-size:18px;cursor:pointer;margin-top:-5px;line-height:1;'>&times;</button>
    </div>";
}

/**
 * Set success flash message (session-based)
 *
 * @param string $message Success message
 * @param string $title Optional title (default: 'Success!')
 * @param array $options Additional options
 */
function flash_success($message, $title = 'Success!', $options = []) {
    flash_message('success', $message, $title, $options);
}

/**
 * Set error flash message (session-based)
 *
 * @param string $message Error message
 * @param string $title Optional title (default: 'Error!')
 * @param array $options Additional options
 */
function flash_error($message, $title = 'Error!', $options = []) {
    flash_message('error', $message, $title, $options);
}

/**
 * Set warning flash message (session-based)
 *
 * @param string $message Warning message
 * @param string $title Optional title (default: 'Warning!')
 * @param array $options Additional options
 */
function flash_warning($message, $title = 'Warning!', $options = []) {
    flash_message('warning', $message, $title, $options);
}

/**
 * Set info flash message (session-based)
 *
 * @param string $message Info message
 * @param string $title Optional title (default: 'Info')
 * @param array $options Additional options
 */
function flash_info($message, $title = 'Info', $options = []) {
    flash_message('info', $message, $title, $options);
}

// ============================================================================
// INSTANT ALERT FUNCTIONS (JavaScript-based, immediate display)
// ============================================================================

/**
 * Instant success alert
 */
function flash_success_instant($message, $title = 'Success!', $options = []) {
    flash_instant('success', $message, $title, $options);
}

/**
 * Instant error alert
 */
function flash_error_instant($message, $title = 'Error!', $options = []) {
    flash_instant('error', $message, $title, $options);
}

/**
 * Instant warning alert
 */
function flash_warning_instant($message, $title = 'Warning!', $options = []) {
    flash_instant('warning', $message, $title, $options);
}

/**
 * Instant info alert
 */
function flash_info_instant($message, $title = 'Info', $options = []) {
    flash_instant('info', $message, $title, $options);
}

// ============================================================================
// SMART FUNCTIONS (Auto-choose between session and instant)
// ============================================================================

/**
 * Smart success message (auto-chooses best method)
 */
function flash_success_smart($message, $title = 'Success!', $instant = false) {
    if ($instant || headers_sent()) {
        flash_success_instant($message, $title);
    } else {
        flash_success($message, $title);
    }
}

/**
 * Smart error message (auto-chooses best method)
 */
function flash_error_smart($message, $title = 'Error!', $instant = false) {
    if ($instant || headers_sent()) {
        flash_error_instant($message, $title);
    } else {
        flash_error($message, $title);
    }
}

/**
 * Smart warning message (auto-chooses best method)
 */
function flash_warning_smart($message, $title = 'Warning!', $instant = false) {
    if ($instant || headers_sent()) {
        flash_warning_instant($message, $title);
    } else {
        flash_warning($message, $title);
    }
}

/**
 * Smart info message (auto-chooses best method)
 */
function flash_info_smart($message, $title = 'Info', $instant = false) {
    if ($instant || headers_sent()) {
        flash_info_instant($message, $title);
    } else {
        flash_info($message, $title);
    }
}

/**
 * Set multiple flash messages at once
 * 
 * @param array $messages Array of message arrays
 * 
 * Example:
 * flash_multiple([
 *     ['type' => 'success', 'message' => 'Profile updated!'],
 *     ['type' => 'info', 'message' => 'Check your email', 'title' => 'Verification']
 * ]);
 */
function flash_multiple($messages) {
    foreach ($messages as $msg) {
        $type = $msg['type'] ?? 'info';
        $message = $msg['message'] ?? '';
        $title = $msg['title'] ?? '';
        $options = $msg['options'] ?? [];
        
        if ($message) {
            flash_message($type, $message, $title, $options);
        }
    }
}

/**
 * Authentication related flash messages
 */

// Login messages
function flash_login_success($username = '') {
    $message = $username ? "Welcome back, {$username}!" : 'Login successful!';
    flash_success($message, 'Welcome!');
}

function flash_login_failed() {
    flash_error('Invalid email or password. Please try again.', 'Login Failed');
}

function flash_login_required() {
    flash_warning('Please login to access this page.', 'Authentication Required');
}

// Logout messages
function flash_logout_success() {
    flash_success('You have been logged out successfully.', 'Goodbye!');
}

// Registration messages
function flash_registration_success() {
    flash_success('Your account has been created successfully! Please login to continue.', 'Registration Complete');
}

function flash_registration_failed($errors = []) {
    if (empty($errors)) {
        flash_error('Registration failed. Please check your information and try again.', 'Registration Failed');
    } else {
        $errorList = is_array($errors) ? implode(', ', $errors) : $errors;
        flash_error("Registration failed: {$errorList}", 'Registration Failed');
    }
}

// Email verification
function flash_email_verification_sent() {
    flash_info('A verification email has been sent to your email address.', 'Check Your Email');
}

function flash_email_verified() {
    flash_success('Your email has been verified successfully!', 'Email Verified');
}

// Password reset
function flash_password_reset_sent() {
    flash_info('Password reset instructions have been sent to your email.', 'Check Your Email');
}

function flash_password_reset_success() {
    flash_success('Your password has been reset successfully!', 'Password Updated');
}

/**
 * CRUD operation flash messages
 */

// Create messages
function flash_created($item = 'Item') {
    flash_success("{$item} has been created successfully!", 'Created');
}

// Update messages
function flash_updated($item = 'Item') {
    flash_success("{$item} has been updated successfully!", 'Updated');
}

// Delete messages
function flash_deleted($item = 'Item') {
    flash_success("{$item} has been deleted successfully!", 'Deleted');
}

// Save messages
function flash_saved($item = 'Changes') {
    flash_success("{$item} have been saved successfully!", 'Saved');
}

/**
 * Validation flash messages
 */

function flash_validation_errors($errors) {
    if (is_array($errors)) {
        $errorList = implode('<br>', $errors);
        flash_error($errorList, 'Please fix the following errors:', [
            'autoHide' => false
        ]);
    } else {
        flash_error($errors, 'Validation Error');
    }
}

function flash_required_fields() {
    flash_warning('Please fill in all required fields.', 'Missing Information');
}

/**
 * Permission and access flash messages
 */

function flash_access_denied() {
    flash_error('You do not have permission to access this resource.', 'Access Denied');
}

function flash_admin_required() {
    flash_error('Administrator privileges required to access this page.', 'Admin Access Required');
}

function flash_contributor_required() {
    flash_warning('Contributor access required for this action.', 'Contributor Access Required');
}

/**
 * Quiz related flash messages
 */

function flash_quiz_completed($score = null) {
    $message = $score !== null ? "Quiz completed! Your score: {$score}%" : 'Quiz completed successfully!';
    flash_success($message, 'Well Done!');
}

function flash_quiz_failed($score = null) {
    $message = $score !== null ? "Quiz failed. Your score: {$score}%. Try again!" : 'Quiz failed. Please try again.';
    flash_error($message, 'Better Luck Next Time');
}

function flash_quiz_time_up() {
    flash_warning('Time is up! Your quiz has been submitted automatically.', 'Time Up');
}

/**
 * E-commerce related flash messages
 */

function flash_added_to_cart($product = 'Product') {
    flash_success("{$product} has been added to your cart!", 'Added to Cart');
}

function flash_removed_from_cart($product = 'Product') {
    flash_info("{$product} has been removed from your cart.", 'Removed from Cart');
}

function flash_order_placed($orderNumber = '') {
    $message = $orderNumber ? "Order #{$orderNumber} has been placed successfully!" : 'Your order has been placed successfully!';
    flash_success($message, 'Order Confirmed');
}

/**
 * Payment related flash messages
 */

function flash_payment_success($amount = null) {
    $message = $amount ? "Payment of \${$amount} completed successfully!" : 'Payment completed successfully!';
    flash_success($message, 'Payment Successful');
}

function flash_payment_failed($reason = '') {
    $message = $reason ? "Payment failed: {$reason}" : 'Payment failed. Please try again.';
    flash_error($message, 'Payment Failed');
}

function flash_insufficient_balance() {
    flash_error('Insufficient balance in your wallet.', 'Payment Failed');
}

/**
 * File upload flash messages
 */

function flash_upload_success($filename = 'File') {
    flash_success("{$filename} has been uploaded successfully!", 'Upload Complete');
}

function flash_upload_failed($reason = '') {
    $message = $reason ? "Upload failed: {$reason}" : 'File upload failed. Please try again.';
    flash_error($message, 'Upload Failed');
}

function flash_file_too_large($maxSize = '10MB') {
    flash_error("File is too large. Maximum size allowed is {$maxSize}.", 'File Too Large');
}

/**
 * Social media flash messages
 */

function flash_post_published() {
    flash_success('Your post has been published successfully!', 'Post Published');
}

function flash_comment_added() {
    flash_success('Your comment has been added!', 'Comment Added');
}

function flash_followed_user($username = 'user') {
    flash_success("You are now following {$username}!", 'Following');
}

function flash_unfollowed_user($username = 'user') {
    flash_info("You have unfollowed {$username}.", 'Unfollowed');
}

/**
 * System messages
 */

function flash_maintenance_mode() {
    flash_warning('The system is currently under maintenance. Some features may be unavailable.', 'Maintenance Mode', [
        'autoHide' => false
    ]);
}

function flash_system_error() {
    flash_error('A system error occurred. Please try again later or contact support.', 'System Error');
}

function flash_feature_coming_soon() {
    flash_info('This feature is coming soon! Stay tuned for updates.', 'Coming Soon');
}

/**
 * Utility functions
 */

/**
 * Check if there are any flash messages
 * 
 * @return bool
 */
function has_flash_messages() {
    return !empty($_SESSION['flash_messages']);
}

/**
 * Get flash messages count
 * 
 * @return int
 */
function flash_messages_count() {
    return count($_SESSION['flash_messages'] ?? []);
}

/**
 * Clear all flash messages
 */
function clear_flash_messages() {
    unset($_SESSION['flash_messages']);
}

/**
 * Get flash messages without clearing them
 * 
 * @return array
 */
function peek_flash_messages() {
    return $_SESSION['flash_messages'] ?? [];
}

/**
 * Display flash messages (include the component)
 */
function display_flash_messages() {
    if (has_flash_messages()) {
        include_once __DIR__ . '/../../views/components/utils/flash-messages.php';
    }
}
?>
