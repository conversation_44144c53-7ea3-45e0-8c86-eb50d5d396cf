<?php
/**
 * 🧪 QUICK TEST - Flash Messages System
 * Simple test page to verify everything works correctly
 */

// Include the main flash messages system
require_once __DIR__ . '/flash-messages.php';

// Handle test actions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'test_success':
            flash_success('This is a success message!', 'Success!');
            break;
        case 'test_error':
            flash_error('This is an error message!', 'Error!');
            break;
        case 'test_warning':
            flash_warning('This is a warning message!', 'Warning!');
            break;
        case 'test_info':
            flash_info('This is an info message!', 'Information');
            break;
        case 'test_smart':
            flash_smart('Login successful! Welcome back user!');
            break;
        case 'test_payment':
            flash_payment_success('$299.99');
            break;
        case 'test_achievement':
            flash_achievement_unlocked('Test Master');
            break;
        case 'test_multiple':
            flash_success('First message', 'Success');
            flash_info('Second message', 'Info');
            flash_warning('Third message', 'Warning');
            break;
        case 'clear_all':
            flash_clear_all();
            break;
    }
    
    // Redirect to prevent form resubmission
    header('Location: ' . $_SERVER['PHP_SELF'] . '#test-results');
    exit;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Quick Test - Flash Messages</title>
    <style>
        body {
            font-family: system-ui, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 8px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-block;
            min-width: 200px;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
        }
        
        .test-button:active {
            transform: scale(0.95);
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        
        .status-box {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .success { background: #4CAF50; }
        .error { background: #f44336; }
        .warning { background: #ff9800; }
        .info { background: #2196F3; }
        .special { background: #9c27b0; }
        .clear { background: #607d8b; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Quick Test - Flash Messages System</h1>
        <p>Click any button below to test the flash messages system:</p>
        
        <div class="status-box">
            <h3>📊 System Status</h3>
            <p><strong>Theme:</strong> <?= FlashConfig::$theme ?></p>
            <p><strong>Position:</strong> <?= FlashConfig::$position ?></p>
            <p><strong>License:</strong> <?= FlashConfig::$isPremium ? 'Premium' : 'Trial/Free' ?></p>
            <p><strong>Trial Days Left:</strong> <?= FlashConfig::$trialDaysLeft ?></p>
        </div>
        
        <h3>🎯 Basic Message Tests</h3>
        <div class="test-grid">
            <form method="POST" style="display: inline;">
                <input type="hidden" name="action" value="test_success">
                <button type="submit" class="test-button success">✅ Success Message</button>
            </form>
            
            <form method="POST" style="display: inline;">
                <input type="hidden" name="action" value="test_error">
                <button type="submit" class="test-button error">❌ Error Message</button>
            </form>
            
            <form method="POST" style="display: inline;">
                <input type="hidden" name="action" value="test_warning">
                <button type="submit" class="test-button warning">⚠️ Warning Message</button>
            </form>
            
            <form method="POST" style="display: inline;">
                <input type="hidden" name="action" value="test_info">
                <button type="submit" class="test-button info">ℹ️ Info Message</button>
            </form>
        </div>
        
        <h3>🧠 Smart & Special Tests</h3>
        <div class="test-grid">
            <form method="POST" style="display: inline;">
                <input type="hidden" name="action" value="test_smart">
                <button type="submit" class="test-button special">🤖 Smart Detection</button>
            </form>
            
            <form method="POST" style="display: inline;">
                <input type="hidden" name="action" value="test_payment">
                <button type="submit" class="test-button special">💰 Payment Success</button>
            </form>
            
            <form method="POST" style="display: inline;">
                <input type="hidden" name="action" value="test_achievement">
                <button type="submit" class="test-button special">🏆 Achievement</button>
            </form>
            
            <form method="POST" style="display: inline;">
                <input type="hidden" name="action" value="test_multiple">
                <button type="submit" class="test-button special">📦 Multiple Messages</button>
            </form>
        </div>
        
        <h3>🔧 Control</h3>
        <form method="POST" style="display: inline;">
            <input type="hidden" name="action" value="clear_all">
            <button type="submit" class="test-button clear">🗑️ Clear All Messages</button>
        </form>
        
        <div id="test-results" style="margin-top: 40px;">
            <h3>📋 Quick Links</h3>
            <p>
                <a href="flash-demo-settings.php" style="color: #667eea; text-decoration: none; font-weight: bold;">🚀 Full Demo & Settings</a> |
                <a href="license.php" style="color: #667eea; text-decoration: none; font-weight: bold;">🔐 License Management</a> |
                <a href="README.md" style="color: #667eea; text-decoration: none; font-weight: bold;">📖 Documentation</a>
            </p>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
            <h4>💡 Usage Example</h4>
            <pre style="background: #1a1a1a; color: #00ff00; padding: 15px; border-radius: 5px; overflow-x: auto;"><code>&lt;?php
// Include the system (one line!)
require_once 'views/components/flash-messages/flash-messages.php';

// Use any function
flash_success('Welcome!', 'Success!');
flash_payment_success('$299.99');
flash_achievement_unlocked('Master');
?&gt;</code></pre>
        </div>
    </div>

    <!-- Flash messages will be rendered here automatically -->

    <script>
        // Add click feedback
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('.test-button');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    // Visual feedback
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                    
                    // Loading state
                    const originalText = this.textContent;
                    this.textContent = '⏳ Processing...';
                    this.disabled = true;
                    
                    // Reset after delay
                    setTimeout(() => {
                        this.textContent = originalText;
                        this.disabled = false;
                    }, 1000);
                });
            });
            
            // Smooth scroll to results if hash present
            if (window.location.hash === '#test-results') {
                setTimeout(() => {
                    document.getElementById('test-results').scrollIntoView({ 
                        behavior: 'smooth', 
                        block: 'center' 
                    });
                }, 100);
            }
        });
    </script>

</body>
</html>
