<?php
/**
 * 🧪 ULTIMATE FLASH MESSAGES - QUICK TEST
 * Simple test file to verify the system works correctly
 */

// Include the system (one line!)
require_once __DIR__ . '/include.php';

// Handle test actions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'test_basic':
            flash_success('System is working perfectly!', 'Test Successful');
            flash_info('All 500+ functions are ready to use!', 'System Ready');
            break;
            
        case 'test_smart':
            flash_smart('Login successful! Welcome back user!');
            flash_smart('Error occurred while processing request');
            flash_smart('File uploaded successfully');
            break;
            
        case 'test_effects':
            flash_success_confetti('Amazing! The system works!', 'Celebration!');
            break;
            
        case 'test_context':
            flash_login_success('Test User');
            flash_payment_success('$99.99');
            flash_achievement_unlocked('System Tester');
            break;
    }
    
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Flash Messages Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: system-ui, sans-serif;
        }
        .test-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 2rem auto;
            max-width: 600px;
        }
        .test-button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 12px 24px;
            border-radius: 10px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            margin: 10px;
            display: inline-block;
            text-decoration: none;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>

    <div class="test-card">
        <h1 class="text-4xl font-bold text-center mb-6">🧪 Flash Messages Test</h1>
        <p class="text-center text-gray-600 mb-8">Test the Ultimate Flash Messages System</p>
        
        <div class="text-center">
            <form method="POST" class="inline">
                <input type="hidden" name="action" value="test_basic">
                <button type="submit" class="test-button">🎯 Test Basic Messages</button>
            </form>
            
            <form method="POST" class="inline">
                <input type="hidden" name="action" value="test_smart">
                <button type="submit" class="test-button">🧠 Test Smart Detection</button>
            </form>
            
            <form method="POST" class="inline">
                <input type="hidden" name="action" value="test_effects">
                <button type="submit" class="test-button">🎊 Test Special Effects</button>
            </form>
            
            <form method="POST" class="inline">
                <input type="hidden" name="action" value="test_context">
                <button type="submit" class="test-button">🎯 Test Context-Aware</button>
            </form>
        </div>
        
        <div class="mt-8 p-4 bg-gray-50 rounded-lg">
            <h3 class="font-bold text-lg mb-2">✅ System Status</h3>
            <?php $status = flash_system_status(); ?>
            <ul class="text-sm text-gray-600">
                <li>Version: <?= $status['version'] ?></li>
                <li>Status: <?= ucfirst($status['status']) ?></li>
                <li>License: <?= ucfirst($status['license']) ?></li>
                <li>Functions Available: <?= $status['functions_available'] ?>+</li>
                <li>Themes Available: <?= $status['themes_available'] ?>+</li>
            </ul>
        </div>
        
        <div class="mt-6 text-center">
            <a href="demo.php" class="test-button">🚀 View Full Demo</a>
            <a href="README.md" class="test-button">📖 Documentation</a>
        </div>
    </div>

    <!-- The system automatically renders messages here -->

</body>
</html>
