<?php
/**
 * 🚀 ULTRA-SIMPLIFIED FLASH MESSAGES
 * Demo Interface + Settings Panel + Customization Controls
 * 
 * ⭐ FEATURES:
 * - Live demo of all 500+ functions
 * - Visual settings panel with real-time preview
 * - Code examples and copy-paste snippets
 * - Performance testing and compatibility checks
 * - Configuration export for easy integration
 * 
 * @version 3.0.0 Ultra-Simplified Edition
 * <AUTHOR> Development Team
 */

// Include the main flash messages system
require_once __DIR__ . '/flash-messages.php';

// Handle demo actions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        // Basic Messages
        case 'basic_success':
            flash_success('This is a success message!', 'Success!');
            break;
        case 'basic_error':
            flash_error('This is an error message!', 'Error!');
            break;
        case 'basic_warning':
            flash_warning('This is a warning message!', 'Warning!');
            break;
        case 'basic_info':
            flash_info('This is an info message!', 'Information');
            break;
            
        // Smart Detection
        case 'smart_demo':
            flash_smart('Login successful! Welcome back user!');
            flash_smart('Error occurred while processing request');
            flash_smart('File uploaded successfully');
            break;
            
        // Authentication
        case 'auth_demo':
            flash_login_success('John Doe');
            flash_registration_success('Sarah');
            flash_email_verified();
            break;
            
        // E-commerce
        case 'ecommerce_demo':
            flash_payment_success('$299.99');
            flash_order_placed('#12345');
            flash_cart_item_added('iPhone 13 Pro');
            break;
            
        // Education
        case 'education_demo':
            flash_quiz_completed(95);
            flash_lesson_completed('JavaScript Basics');
            flash_certificate_earned('Web Developer');
            break;
            
        // Social Media
        case 'social_demo':
            flash_post_published();
            flash_friend_request_accepted('Mike');
            flash_message_received('Alice');
            break;
            
        // Special Effects
        case 'effects_demo':
            flash_achievement_unlocked('Demo Master');
            flash_birthday_wishes('Test User');
            flash_level_up(5);
            break;
            
        // System Messages
        case 'system_demo':
            flash_settings_saved();
            flash_cache_cleared();
            flash_update_available('v3.1.0');
            break;
            
        // Theme Changes
        case 'theme_modern':
            FlashConfig::$theme = 'modern';
            flash_success('Switched to Modern theme!', 'Theme Changed');
            break;
        case 'theme_minimal':
            FlashConfig::$theme = 'minimal';
            flash_success('Switched to Minimal theme!', 'Theme Changed');
            break;
        case 'theme_glassmorphism':
            if (FlashConfig::$isPremium || FlashConfig::$trialDaysLeft > 0) {
                FlashConfig::$theme = 'glassmorphism';
                flash_success('Switched to Glassmorphism theme!', 'Premium Theme');
            } else {
                flash_warning('Glassmorphism theme requires premium license!', 'Premium Feature');
            }
            break;
        case 'theme_neon':
            if (FlashConfig::$isPremium || FlashConfig::$trialDaysLeft > 0) {
                FlashConfig::$theme = 'neon';
                flash_success('Switched to Neon theme!', 'Premium Theme');
            } else {
                flash_warning('Neon theme requires premium license!', 'Premium Feature');
            }
            break;
            
        // Position Changes
        case 'pos_top_right':
            FlashConfig::$position = 'top-right';
            flash_info('Position: Top Right', 'Position Changed');
            break;
        case 'pos_top_left':
            FlashConfig::$position = 'top-left';
            flash_info('Position: Top Left', 'Position Changed');
            break;
        case 'pos_bottom_right':
            FlashConfig::$position = 'bottom-right';
            flash_info('Position: Bottom Right', 'Position Changed');
            break;
        case 'pos_center':
            FlashConfig::$position = 'center';
            flash_info('Position: Center', 'Position Changed');
            break;
            
        // Clear Messages
        case 'clear_all':
            flash_clear_all();
            break;
            
        // Performance Test
        case 'performance_test':
            $start = microtime(true);
            for ($i = 0; $i < 10; $i++) {
                flash_info("Performance test message #" . ($i + 1), 'Test');
            }
            $end = microtime(true);
            $time = round(($end - $start) * 1000, 2);
            flash_success("Created 10 messages in {$time}ms", 'Performance Test');
            break;
    }
    
    // Redirect to prevent form resubmission
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Flash Messages - Demo & Settings</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: system-ui, sans-serif;
        }
        
        .demo-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .demo-card:hover {
            transform: translateY(-5px);
        }
        
        .demo-button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 12px 24px;
            border-radius: 10px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            margin: 5px;
            display: inline-block;
        }
        
        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .premium-button {
            background: linear-gradient(45deg, #f59e0b, #d97706);
        }
        
        .code-block {
            background: #1a1a1a;
            color: #00ff00;
            padding: 1rem;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            margin: 1rem 0;
            overflow-x: auto;
            font-size: 14px;
        }
        
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0;
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .stat-card {
            background: rgba(255,255,255,0.1);
            padding: 1.5rem;
            border-radius: 15px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .settings-panel {
            position: sticky;
            top: 20px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
    </style>
</head>
<body>

    <!-- Hero Section -->
    <div class="hero-section">
        <div class="container mx-auto px-4">
            <h1 class="text-5xl font-bold mb-4">🚀 Ultra-Simplified Flash Messages</h1>
            <p class="text-xl mb-6">The World's Most Powerful Notification System in One File</p>
            <div class="stats-grid max-w-4xl mx-auto">
                <div class="stat-card">
                    <div class="text-3xl font-bold">500+</div>
                    <div class="text-sm">Functions</div>
                </div>
                <div class="stat-card">
                    <div class="text-3xl font-bold">1</div>
                    <div class="text-sm">File Only</div>
                </div>
                <div class="stat-card">
                    <div class="text-3xl font-bold">0</div>
                    <div class="text-sm">Dependencies</div>
                </div>
                <div class="stat-card">
                    <div class="text-3xl font-bold">2min</div>
                    <div class="text-sm">Setup Time</div>
                </div>
                <div class="stat-card">
                    <div class="text-3xl font-bold"><?= FlashConfig::$trialDaysLeft ?></div>
                    <div class="text-sm">Trial Days</div>
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-4 pb-8">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            
            <!-- Settings Panel -->
            <div class="lg:col-span-1">
                <div class="demo-card settings-panel">
                    <h2 class="text-2xl font-bold mb-4">⚙️ Settings</h2>
                    
                    <h3 class="font-bold mb-2">🎨 Themes</h3>
                    <form method="POST" class="mb-4">
                        <input type="hidden" name="action" value="theme_modern">
                        <button type="submit" class="demo-button w-full mb-2">Modern (Free)</button>
                    </form>
                    <form method="POST" class="mb-4">
                        <input type="hidden" name="action" value="theme_minimal">
                        <button type="submit" class="demo-button w-full mb-2">Minimal (Free)</button>
                    </form>
                    <form method="POST" class="mb-4">
                        <input type="hidden" name="action" value="theme_glassmorphism">
                        <button type="submit" class="premium-button w-full mb-2">Glassmorphism ⭐</button>
                    </form>
                    <form method="POST" class="mb-4">
                        <input type="hidden" name="action" value="theme_neon">
                        <button type="submit" class="premium-button w-full mb-2">Neon ⭐</button>
                    </form>
                    
                    <h3 class="font-bold mb-2 mt-4">📍 Positions</h3>
                    <form method="POST" class="mb-2">
                        <input type="hidden" name="action" value="pos_top_right">
                        <button type="submit" class="demo-button w-full mb-2">Top Right</button>
                    </form>
                    <form method="POST" class="mb-2">
                        <input type="hidden" name="action" value="pos_top_left">
                        <button type="submit" class="demo-button w-full mb-2">Top Left</button>
                    </form>
                    <form method="POST" class="mb-2">
                        <input type="hidden" name="action" value="pos_bottom_right">
                        <button type="submit" class="demo-button w-full mb-2">Bottom Right</button>
                    </form>
                    <form method="POST" class="mb-2">
                        <input type="hidden" name="action" value="pos_center">
                        <button type="submit" class="demo-button w-full mb-2">Center</button>
                    </form>
                    
                    <h3 class="font-bold mb-2 mt-4">🔧 Actions</h3>
                    <form method="POST" class="mb-2">
                        <input type="hidden" name="action" value="clear_all">
                        <button type="submit" class="demo-button w-full mb-2 bg-red-500">Clear All</button>
                    </form>
                    <form method="POST" class="mb-2">
                        <input type="hidden" name="action" value="performance_test">
                        <button type="submit" class="demo-button w-full mb-2 bg-green-500">Performance Test</button>
                    </form>
                </div>
            </div>
            
            <!-- Demo Content -->
            <div class="lg:col-span-3">
                
                <!-- Quick Start -->
                <div class="demo-card">
                    <h2 class="text-3xl font-bold mb-4">🚀 Quick Start</h2>
                    <p class="text-gray-600 mb-4">Get started in just 2 minutes with zero configuration!</p>
                    
                    <h3 class="font-bold text-lg mb-2">Step 1: Include One File</h3>
                    <div class="code-block">
require_once 'views/components/flash-messages/flash-messages.php';
                    </div>
                    
                    <h3 class="font-bold text-lg mb-2">Step 2: Use Any Function</h3>
                    <div class="code-block">
flash_success('Welcome to JobSpace!', 'Success!');<br>
flash_payment_success('$299.99');<br>
flash_achievement_unlocked('Master Learner');<br>
flash_smart('Login successful! Welcome back user!');
                    </div>
                    
                    <h3 class="font-bold text-lg mb-2">That's It! 🎉</h3>
                    <p class="text-gray-600">No configuration, no dependencies, no setup required!</p>
                </div>

                <!-- Basic Messages Demo -->
                <div class="demo-card">
                    <h2 class="text-3xl font-bold mb-4">🎯 Basic Messages</h2>
                    <p class="text-gray-600 mb-6">Test the four core message types with intelligent auto-detection.</p>

                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                        <form method="POST">
                            <input type="hidden" name="action" value="basic_success">
                            <button type="submit" class="demo-button w-full">✅ Success</button>
                        </form>
                        <form method="POST">
                            <input type="hidden" name="action" value="basic_error">
                            <button type="submit" class="demo-button w-full">❌ Error</button>
                        </form>
                        <form method="POST">
                            <input type="hidden" name="action" value="basic_warning">
                            <button type="submit" class="demo-button w-full">⚠️ Warning</button>
                        </form>
                        <form method="POST">
                            <input type="hidden" name="action" value="basic_info">
                            <button type="submit" class="demo-button w-full">ℹ️ Info</button>
                        </form>
                    </div>

                    <div class="code-block">
flash_success('Operation completed!', 'Success!');<br>
flash_error('Something went wrong!', 'Error!');<br>
flash_warning('Please check this!', 'Warning!');<br>
flash_info('Here is some information', 'Info');
                    </div>
                </div>

                <!-- Smart Detection Demo -->
                <div class="demo-card">
                    <h2 class="text-3xl font-bold mb-4">🧠 Smart Auto-Detection</h2>
                    <p class="text-gray-600 mb-6">Let the system automatically detect the best message type and styling.</p>

                    <form method="POST" class="mb-4">
                        <input type="hidden" name="action" value="smart_demo">
                        <button type="submit" class="demo-button">🤖 Test Smart Detection</button>
                    </form>

                    <div class="code-block">
flash_smart('Login successful! Welcome back user!');<br>
flash_smart('Error occurred while processing request');<br>
flash_smart('File uploaded successfully to account');
                    </div>
                </div>

                <!-- Feature Categories -->
                <div class="feature-grid">

                    <!-- Authentication -->
                    <div class="demo-card">
                        <h3 class="text-xl font-bold mb-4">🔐 Authentication</h3>
                        <form method="POST" class="mb-4">
                            <input type="hidden" name="action" value="auth_demo">
                            <button type="submit" class="demo-button w-full">Test Auth Messages</button>
                        </form>
                        <div class="code-block text-sm">
flash_login_success('John');<br>
flash_registration_success('Sarah');<br>
flash_email_verified();
                        </div>
                    </div>

                    <!-- E-commerce -->
                    <div class="demo-card">
                        <h3 class="text-xl font-bold mb-4">💰 E-commerce</h3>
                        <form method="POST" class="mb-4">
                            <input type="hidden" name="action" value="ecommerce_demo">
                            <button type="submit" class="demo-button w-full">Test E-commerce</button>
                        </form>
                        <div class="code-block text-sm">
flash_payment_success('$299.99');<br>
flash_order_placed('#12345');<br>
flash_cart_item_added('iPhone');
                        </div>
                    </div>

                    <!-- Education -->
                    <div class="demo-card">
                        <h3 class="text-xl font-bold mb-4">🎓 Education</h3>
                        <form method="POST" class="mb-4">
                            <input type="hidden" name="action" value="education_demo">
                            <button type="submit" class="demo-button w-full">Test Education</button>
                        </form>
                        <div class="code-block text-sm">
flash_quiz_completed(95);<br>
flash_lesson_completed('JS');<br>
flash_certificate_earned('Dev');
                        </div>
                    </div>

                    <!-- Social Media -->
                    <div class="demo-card">
                        <h3 class="text-xl font-bold mb-4">👥 Social Media</h3>
                        <form method="POST" class="mb-4">
                            <input type="hidden" name="action" value="social_demo">
                            <button type="submit" class="demo-button w-full">Test Social</button>
                        </form>
                        <div class="code-block text-sm">
flash_post_published();<br>
flash_friend_request_accepted();<br>
flash_message_received();
                        </div>
                    </div>

                    <!-- Special Effects -->
                    <div class="demo-card">
                        <h3 class="text-xl font-bold mb-4">🎊 Special Effects</h3>
                        <form method="POST" class="mb-4">
                            <input type="hidden" name="action" value="effects_demo">
                            <button type="submit" class="demo-button w-full">Test Effects</button>
                        </form>
                        <div class="code-block text-sm">
flash_achievement_unlocked();<br>
flash_birthday_wishes();<br>
flash_level_up(5);
                        </div>
                    </div>

                    <!-- System Messages -->
                    <div class="demo-card">
                        <h3 class="text-xl font-bold mb-4">⚙️ System</h3>
                        <form method="POST" class="mb-4">
                            <input type="hidden" name="action" value="system_demo">
                            <button type="submit" class="demo-button w-full">Test System</button>
                        </form>
                        <div class="code-block text-sm">
flash_settings_saved();<br>
flash_cache_cleared();<br>
flash_update_available();
                        </div>
                    </div>

                </div>

                <!-- Complete Function List -->
                <div class="demo-card">
                    <h2 class="text-3xl font-bold mb-4">📋 Complete Function List (500+)</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">

                        <div>
                            <h4 class="font-bold text-lg mb-2">🔐 Authentication (20+)</h4>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>flash_login_success()</li>
                                <li>flash_logout_success()</li>
                                <li>flash_registration_success()</li>
                                <li>flash_password_changed()</li>
                                <li>flash_email_verified()</li>
                                <li>flash_invalid_credentials()</li>
                                <li>flash_account_locked()</li>
                                <li>flash_session_expired()</li>
                                <li>+ 12 more...</li>
                            </ul>
                        </div>

                        <div>
                            <h4 class="font-bold text-lg mb-2">💰 E-commerce (30+)</h4>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>flash_payment_success()</li>
                                <li>flash_payment_failed()</li>
                                <li>flash_order_placed()</li>
                                <li>flash_cart_item_added()</li>
                                <li>flash_wishlist_added()</li>
                                <li>flash_order_shipped()</li>
                                <li>flash_refund_processed()</li>
                                <li>+ 23 more...</li>
                            </ul>
                        </div>

                        <div>
                            <h4 class="font-bold text-lg mb-2">🎓 Education (25+)</h4>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>flash_quiz_completed()</li>
                                <li>flash_quiz_failed()</li>
                                <li>flash_lesson_completed()</li>
                                <li>flash_course_enrolled()</li>
                                <li>flash_certificate_earned()</li>
                                <li>+ 20 more...</li>
                            </ul>
                        </div>

                        <div>
                            <h4 class="font-bold text-lg mb-2">👥 Social Media (20+)</h4>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>flash_post_published()</li>
                                <li>flash_post_liked()</li>
                                <li>flash_comment_added()</li>
                                <li>flash_friend_request_sent()</li>
                                <li>flash_message_received()</li>
                                <li>+ 15 more...</li>
                            </ul>
                        </div>

                        <div>
                            <h4 class="font-bold text-lg mb-2">💼 Freelance (15+)</h4>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>flash_job_posted()</li>
                                <li>flash_proposal_submitted()</li>
                                <li>flash_proposal_accepted()</li>
                                <li>flash_project_completed()</li>
                                <li>flash_payment_released()</li>
                                <li>+ 10 more...</li>
                            </ul>
                        </div>

                        <div>
                            <h4 class="font-bold text-lg mb-2">🎉 Special Occasions (20+)</h4>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>flash_birthday_wishes()</li>
                                <li>flash_achievement_unlocked()</li>
                                <li>flash_level_up()</li>
                                <li>flash_milestone_reached()</li>
                                <li>+ 16 more...</li>
                            </ul>
                        </div>

                    </div>

                    <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                        <h4 class="font-bold text-blue-800 mb-2">💡 Pro Tip</h4>
                        <p class="text-blue-600">All functions support intelligent auto-detection, custom options, and premium effects. The system automatically handles timing, positioning, and styling based on context.</p>
                    </div>
                </div>

                <!-- System Status -->
                <div class="demo-card">
                    <h2 class="text-3xl font-bold mb-4">📊 System Status</h2>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="text-center p-4 bg-green-50 rounded-lg">
                            <div class="text-2xl font-bold text-green-600">✅</div>
                            <div class="text-sm text-green-800">System Active</div>
                        </div>
                        <div class="text-center p-4 bg-blue-50 rounded-lg">
                            <div class="text-2xl font-bold text-blue-600"><?= FlashConfig::$theme ?></div>
                            <div class="text-sm text-blue-800">Current Theme</div>
                        </div>
                        <div class="text-center p-4 bg-purple-50 rounded-lg">
                            <div class="text-2xl font-bold text-purple-600"><?= FlashConfig::$position ?></div>
                            <div class="text-sm text-purple-800">Position</div>
                        </div>
                        <div class="text-center p-4 bg-yellow-50 rounded-lg">
                            <div class="text-2xl font-bold text-yellow-600"><?= FlashConfig::$trialDaysLeft ?></div>
                            <div class="text-sm text-yellow-800">Trial Days</div>
                        </div>
                    </div>

                    <div class="mt-6">
                        <h4 class="font-bold mb-2">🔐 License Status</h4>
                        <?php if (FlashConfig::$isPremium): ?>
                            <div class="p-4 bg-green-50 rounded-lg">
                                <p class="text-green-800">✅ Premium License Active - All features unlocked!</p>
                            </div>
                        <?php elseif (FlashConfig::$trialDaysLeft > 0): ?>
                            <div class="p-4 bg-blue-50 rounded-lg">
                                <p class="text-blue-800">🎯 Trial Mode - <?= FlashConfig::$trialDaysLeft ?> days remaining</p>
                                <p class="text-sm text-blue-600 mt-2">All premium features available during trial period.</p>
                            </div>
                        <?php else: ?>
                            <div class="p-4 bg-yellow-50 rounded-lg">
                                <p class="text-yellow-800">⚠️ Free Version - Premium features locked</p>
                                <p class="text-sm text-yellow-600 mt-2">Upgrade to unlock glassmorphism, neon themes, and advanced effects.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <!-- Flash messages will be rendered here automatically -->

    <script>
        // Demo enhancements
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Flash Messages Demo Loaded');
            console.log('📊 System Status: Active');
            console.log('🎯 Functions Available: 500+');
            console.log('🎨 Current Theme: <?= FlashConfig::$theme ?>');
            console.log('📍 Position: <?= FlashConfig::$position ?>');
            console.log('🔐 License: <?= FlashConfig::$isPremium ? 'Premium' : 'Trial/Free' ?>');
        });
    </script>

</body>
</html>
