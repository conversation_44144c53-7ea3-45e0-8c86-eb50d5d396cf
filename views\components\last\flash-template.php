<!-- 🚀 Ultimate Flash Messages Template v3.0 -->
<style id="flash-messages-styles">
:root {
    --flash-primary: #3b82f6;
    --flash-success: #10b981;
    --flash-error: #ef4444;
    --flash-warning: #f59e0b;
    --flash-info: #3b82f6;
    --flash-question: #6366f1;
    --flash-critical: #dc2626;
    --flash-urgent: #ea580c;
    --flash-security: #991b1b;
    --flash-payment: #059669;
    --flash-achievement: #d97706;
    
    --flash-bg: #ffffff;
    --flash-text: #1f2937;
    --flash-border: #e5e7eb;
    --flash-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
    --flash-backdrop: rgba(0, 0, 0, 0.4);
    --flash-radius: <?= FlashConfig::$borderRadius ?>;
    --flash-duration: <?= FlashConfig::$duration ?>ms;
}

<?php if ($isDarkMode): ?>
:root {
    --flash-bg: #1f2937;
    --flash-text: #f9fafb;
    --flash-border: #374151;
    --flash-backdrop: rgba(0, 0, 0, 0.7);
}
<?php endif; ?>

/* Backdrop */
.flash-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--flash-backdrop);
    z-index: 999998;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    <?php if (FlashConfig::$backdropBlur): ?>
    backdrop-filter: blur(5px);
    <?php endif; ?>
}

.flash-backdrop.show {
    opacity: 1;
    visibility: visible;
}

/* Container */
.flash-container {
    background: var(--flash-bg);
    color: var(--flash-text);
    border-radius: var(--flash-radius);
    box-shadow: var(--flash-shadow);
    padding: 32px;
    text-align: center;
    position: relative;
    max-width: <?= FlashConfig::$maxWidth ?>;
    width: 90%;
    max-height: <?= FlashConfig::$maxHeight ?>;
    overflow-y: auto;
    transform: scale(0.7);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.flash-backdrop.show .flash-container {
    transform: scale(1);
    opacity: 1;
}

/* Size variations */
.flash-container.small { max-width: 350px; padding: 24px; }
.flash-container.large { max-width: 650px; padding: 40px; }
.flash-container.tiny { max-width: 300px; padding: 16px; }
.flash-container.huge { max-width: 800px; padding: 48px; }

/* Close button */
.flash-close {
    position: absolute;
    top: 16px;
    right: 16px;
    background: none;
    border: none;
    font-size: 20px;
    color: var(--flash-text);
    opacity: 0.5;
    cursor: pointer;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.flash-close:hover {
    opacity: 1;
    background: rgba(0, 0, 0, 0.1);
    transform: scale(1.1);
}

/* Icon */
.flash-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 40px;
    position: relative;
    animation: flash-icon-appear 0.5s ease-out 0.2s both;
}

@keyframes flash-icon-appear {
    0% { transform: scale(0) rotate(-360deg); opacity: 0; }
    100% { transform: scale(1) rotate(0deg); opacity: 1; }
}

.flash-icon.success { background: linear-gradient(135deg, var(--flash-success), #059669); color: white; }
.flash-icon.error { background: linear-gradient(135deg, var(--flash-error), #dc2626); color: white; }
.flash-icon.warning { background: linear-gradient(135deg, var(--flash-warning), #d97706); color: white; }
.flash-icon.info { background: linear-gradient(135deg, var(--flash-info), #2563eb); color: white; }
.flash-icon.question { background: linear-gradient(135deg, var(--flash-question), #4f46e5); color: white; }
.flash-icon.critical { background: linear-gradient(135deg, var(--flash-critical), #991b1b); color: white; }
.flash-icon.urgent { background: linear-gradient(135deg, var(--flash-urgent), #c2410c); color: white; }
.flash-icon.security { background: linear-gradient(135deg, var(--flash-security), #7f1d1d); color: white; }
.flash-icon.payment { background: linear-gradient(135deg, var(--flash-payment), #047857); color: white; }
.flash-icon.achievement { background: linear-gradient(135deg, var(--flash-achievement), #b45309); color: white; }

/* Title */
.flash-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--flash-text);
    animation: flash-text-appear 0.5s ease-out 0.3s both;
}

/* Text */
.flash-text {
    font-size: 16px;
    line-height: 1.5;
    color: var(--flash-text);
    opacity: 0.8;
    margin-bottom: 24px;
    animation: flash-text-appear 0.5s ease-out 0.4s both;
}

@keyframes flash-text-appear {
    0% { transform: translateY(20px); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
}

/* Input */
.flash-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--flash-border);
    border-radius: 8px;
    font-size: 16px;
    margin-bottom: 24px;
    transition: border-color 0.2s ease;
    background: var(--flash-bg);
    color: var(--flash-text);
}

.flash-input:focus {
    outline: none;
    border-color: var(--flash-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.flash-textarea {
    min-height: 100px;
    resize: vertical;
}

.flash-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 8px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

/* Progress */
.flash-progress {
    width: 100%;
    height: 8px;
    background: var(--flash-border);
    border-radius: 4px;
    margin-bottom: 24px;
    overflow: hidden;
}

.flash-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--flash-primary), var(--flash-info));
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
}

.flash-progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: flash-progress-shine 2s ease-in-out infinite;
}

@keyframes flash-progress-shine {
    0% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
    100% { transform: translateX(100%); }
}

/* Circular Progress */
.flash-progress-circular {
    width: 60px;
    height: 60px;
    margin: 0 auto 24px;
    position: relative;
}

.flash-progress-circular svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}

.flash-progress-circular circle {
    fill: none;
    stroke-width: 4;
    stroke-linecap: round;
}

.flash-progress-circular .bg {
    stroke: var(--flash-border);
}

.flash-progress-circular .progress {
    stroke: var(--flash-primary);
    stroke-dasharray: 188.5;
    stroke-dashoffset: 188.5;
    transition: stroke-dashoffset 0.3s ease;
}

/* Spinner */
.flash-spinner {
    width: 40px;
    height: 40px;
    margin: 0 auto 24px;
    border: 4px solid var(--flash-border);
    border-top: 4px solid var(--flash-primary);
    border-radius: 50%;
    animation: flash-spin 1s linear infinite;
}

@keyframes flash-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.flash-spinner.dots {
    border: none;
    display: flex;
    gap: 4px;
    align-items: center;
    justify-content: center;
}

.flash-spinner.dots::before,
.flash-spinner.dots::after,
.flash-spinner.dots {
    content: '';
    width: 8px;
    height: 8px;
    background: var(--flash-primary);
    border-radius: 50%;
    animation: flash-dots 1.4s ease-in-out infinite both;
}

.flash-spinner.dots::before { animation-delay: -0.32s; }
.flash-spinner.dots::after { animation-delay: -0.16s; }

@keyframes flash-dots {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

/* Timer progress */
.flash-timer-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 4px;
    background: var(--flash-primary);
    width: 100%;
    transform-origin: left;
    border-radius: 0 0 var(--flash-radius) var(--flash-radius);
}

/* Buttons */
.flash-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
    animation: flash-text-appear 0.5s ease-out 0.5s both;
}

.flash-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 100px;
    position: relative;
    overflow: hidden;
}

.flash-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.flash-btn:active { transform: translateY(0); }

.flash-btn-confirm { background: linear-gradient(135deg, var(--flash-primary), #2563eb); color: white; }
.flash-btn-success { background: linear-gradient(135deg, var(--flash-success), #059669); color: white; }
.flash-btn-danger { background: linear-gradient(135deg, var(--flash-error), #dc2626); color: white; }
.flash-btn-warning { background: linear-gradient(135deg, var(--flash-warning), #d97706); color: white; }
.flash-btn-cancel { background: #6b7280; color: white; }
.flash-btn-cancel:hover { background: #4b5563; }

/* Toast */
.flash-toast {
    position: fixed;
    z-index: 999999;
    max-width: 350px;
    width: auto;
    padding: 16px 20px;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 12px;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.flash-toast.show { transform: translateX(0); opacity: 1; }
.flash-toast.hide { transform: translateX(100%); opacity: 0; }

/* Toast positions */
.flash-toast.top-right { top: 20px; right: 20px; }
.flash-toast.top-left { top: 20px; left: 20px; transform: translateX(-100%); }
.flash-toast.top-left.show { transform: translateX(0); }
.flash-toast.bottom-right { bottom: 20px; right: 20px; }
.flash-toast.bottom-left { bottom: 20px; left: 20px; transform: translateX(-100%); }
.flash-toast.bottom-left.show { transform: translateX(0); }
.flash-toast.center { top: 50%; left: 50%; transform: translate(-50%, -50%) scale(0.8); }
.flash-toast.center.show { transform: translate(-50%, -50%) scale(1); }

/* Toast timer */
.flash-toast-timer {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: rgba(255, 255, 255, 0.3);
    width: 100%;
    transform-origin: left;
    border-radius: 0 0 12px 12px;
}

/* Animations */
<?php
$animation = FlashConfig::$animation;
switch ($animation) {
    case 'slideDown':
        echo '.flash-container { transform: translateY(-50px) scale(0.9); }
              .flash-backdrop.show .flash-container { transform: translateY(0) scale(1); }';
        break;
    case 'fadeIn':
        echo '.flash-container { transform: scale(1); }';
        break;
    case 'bounceIn':
        echo '.flash-backdrop.show .flash-container { animation: flash-bounce 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55); }
              @keyframes flash-bounce {
                  0% { transform: scale(0.3); }
                  50% { transform: scale(1.05); }
                  70% { transform: scale(0.9); }
                  100% { transform: scale(1); }
              }';
        break;
}
?>

/* Mobile responsive */
@media (max-width: 768px) {
    .flash-container {
        margin: 20px;
        padding: 24px;
        max-width: none;
        width: calc(100% - 40px);
    }
    
    .flash-title { font-size: 20px; }
    .flash-text { font-size: 14px; }
    .flash-btn { padding: 10px 20px; font-size: 14px; min-width: 80px; }
    
    .flash-toast {
        left: 10px !important;
        right: 10px !important;
        max-width: none;
        width: calc(100% - 20px);
        transform: translateY(100%) !important;
    }
    
    .flash-toast.show { transform: translateY(0) !important; }
    .flash-toast.hide { transform: translateY(100%) !important; }
}

/* Special effects */
@keyframes flash-shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-10px); }
    75% { transform: translateX(10px); }
}

.flash-shake { animation: flash-shake 0.5s ease-in-out 3; }

@keyframes flash-timer {
    from { transform: scaleX(1); }
    to { transform: scaleX(0); }
}

/* Confetti */
.flash-confetti {
    position: fixed;
    width: 10px;
    height: 10px;
    z-index: 1000000;
    pointer-events: none;
    animation: flash-confetti-fall 3s linear forwards;
}

@keyframes flash-confetti-fall {
    to {
        transform: translateY(100vh) rotate(360deg);
        opacity: 0;
    }
}
</style>

<!-- Flash Messages JavaScript -->
<script id="flash-messages-script">
(function() {
    'use strict';
    
    const config = {
        enableSounds: <?= FlashConfig::$enableSounds ? 'true' : 'false' ?>,
        enableVibration: <?= FlashConfig::$enableVibration ? 'true' : 'false' ?>,
        enableConfetti: <?= FlashConfig::$enableConfetti ? 'true' : 'false' ?>,
        enableShake: <?= FlashConfig::$enableShake ? 'true' : 'false' ?>,
        touchGestures: <?= FlashConfig::$touchGestures ? 'true' : 'false' ?>,
        allowOutsideClick: <?= FlashConfig::$allowOutsideClick ? 'true' : 'false' ?>,
        allowEscapeKey: <?= FlashConfig::$allowEscapeKey ? 'true' : 'false' ?>,
        debugMode: <?= FlashConfig::$debugMode ? 'true' : 'false' ?>
    };
    
    let currentModal = null;
    let toastContainer = null;
    let activeToasts = [];
    
    // Initialize
    function init() {
        setupEventListeners();
        createToastContainer();
        
        // Show existing messages
        <?php if (!empty(FlashMessages::getMessages())): ?>
        const messages = <?= json_encode(FlashMessages::getMessages()) ?>;
        if (messages && messages.length > 0) {
            messages.forEach((msg, index) => {
                setTimeout(() => {
                    if (msg.options.toast) {
                        showToast(msg);
                    } else {
                        showModal(msg);
                    }
                }, index * 300);
            });
        }
        <?php endif; ?>
    }
    
    // Setup event listeners
    function setupEventListeners() {
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && config.allowEscapeKey === 'true') {
                closeModal();
            }
        });

        if (config.touchGestures === 'true') {
            setupTouchGestures();
        }
    }

    // Setup touch gestures
    function setupTouchGestures() {
        let startY = 0;
        let currentElement = null;

        document.addEventListener('touchstart', (e) => {
            startY = e.touches[0].clientY;
            currentElement = e.target.closest('.flash-container, .flash-toast');
        });

        document.addEventListener('touchmove', (e) => {
            if (!currentElement) return;
            const deltaY = e.touches[0].clientY - startY;
            if (Math.abs(deltaY) > 50) {
                currentElement.style.transform = `translateY(${deltaY}px)`;
            }
        });

        document.addEventListener('touchend', (e) => {
            if (!currentElement) return;
            const deltaY = e.changedTouches[0].clientY - startY;
            if (Math.abs(deltaY) > 100) {
                if (currentElement.classList.contains('flash-toast')) {
                    dismissToast(currentElement);
                } else {
                    closeModal();
                }
            } else {
                currentElement.style.transform = '';
            }
            currentElement = null;
        });
    }

    // Create toast container
    function createToastContainer() {
        toastContainer = document.createElement('div');
        toastContainer.id = 'flash-toast-container';
        document.body.appendChild(toastContainer);
    }

    // Show modal
    function showModal(message) {
        if (currentModal) closeModal();

        const backdrop = document.createElement('div');
        backdrop.className = 'flash-backdrop';

        const container = document.createElement('div');
        container.className = `flash-container ${message.options.size || 'normal'}`;

        let html = '';

        // Close button
        if (message.options.showCloseButton !== false) {
            html += '<button class="flash-close" onclick="FlashJS.close()">×</button>';
        }

        // Icon
        if (!message.options.hideIcon) {
            html += `<div class="flash-icon ${message.type}">${getIcon(message.type)}</div>`;
        }

        // Title
        if (message.title) {
            html += `<h2 class="flash-title">${escapeHtml(message.title)}</h2>`;
        }

        // Text
        if (message.text) {
            html += `<p class="flash-text">${escapeHtml(message.text)}</p>`;
        }

        // Input
        if (message.options.input) {
            const inputType = message.options.input;
            if (inputType === 'textarea') {
                html += `<textarea class="flash-input flash-textarea" placeholder="${message.options.inputPlaceholder || ''}" id="flash-input"></textarea>`;
            } else if (inputType === 'select') {
                html += `<select class="flash-input flash-select" id="flash-input">`;
                if (message.options.inputOptions) {
                    message.options.inputOptions.forEach(option => {
                        html += `<option value="${option}">${option}</option>`;
                    });
                }
                html += `</select>`;
            } else {
                html += `<input type="${inputType}" class="flash-input" placeholder="${message.options.inputPlaceholder || ''}" id="flash-input">`;
            }
        }

        // Progress
        if (message.options.progressBar) {
            if (message.options.progressStyle === 'circle') {
                html += `
                    <div class="flash-progress-circular">
                        <svg><circle class="bg" cx="30" cy="30" r="28"></circle><circle class="progress" cx="30" cy="30" r="28" style="stroke-dashoffset: ${188.5 - (188.5 * (message.options.progress || 0) / 100)}"></circle></svg>
                    </div>
                `;
            } else {
                html += `<div class="flash-progress"><div class="flash-progress-fill" style="width: ${message.options.progress || 0}%"></div></div>`;
            }
        }

        // Spinner
        if (message.options.type === 'spinner') {
            html += `<div class="flash-spinner ${message.options.spinnerStyle || 'default'}"></div>`;
        }

        // Actions
        if (message.options.showConfirmButton !== false || message.options.showCancelButton) {
            html += '<div class="flash-actions">';

            if (message.options.showCancelButton) {
                html += `<button class="flash-btn flash-btn-cancel" onclick="FlashJS.cancel('${message.id}')">${message.options.cancelButtonText || 'Cancel'}</button>`;
            }

            if (message.options.showConfirmButton !== false) {
                const btnClass = getBtnClass(message.options.confirmType || 'primary');
                html += `<button class="flash-btn ${btnClass}" onclick="FlashJS.confirm('${message.id}')">${message.options.confirmButtonText || 'OK'}</button>`;
            }

            html += '</div>';
        }

        // Timer progress
        if (message.options.timer && <?= FlashConfig::$showProgress ? 'true' : 'false' ?>) {
            html += `<div class="flash-timer-progress" style="animation: flash-timer ${message.options.timer}ms linear forwards;"></div>`;
        }

        container.innerHTML = html;
        backdrop.appendChild(container);
        document.body.appendChild(backdrop);

        currentModal = { backdrop, container, message };

        // Show with animation
        requestAnimationFrame(() => backdrop.classList.add('show'));

        // Outside click
        if (config.allowOutsideClick === 'true' && message.options.allowOutsideClick !== false) {
            backdrop.addEventListener('click', (e) => {
                if (e.target === backdrop) closeModal();
            });
        }

        // Auto-close
        if (message.options.timer) {
            setTimeout(closeModal, message.options.timer);
        }

        // Focus input
        const input = container.querySelector('#flash-input');
        if (input) setTimeout(() => input.focus(), 300);

        // Effects
        playSound(message.type);
        triggerVibration(message.type);

        if (message.type === 'success' && config.enableConfetti === 'true') {
            showConfetti();
        }

        if (message.type === 'error' && config.enableShake === 'true') {
            container.classList.add('flash-shake');
        }
    }

    // Show toast
    function showToast(message) {
        const toast = document.createElement('div');
        toast.className = `flash-toast ${message.type} ${message.options.position || 'top-right'}`;

        toast.innerHTML = `
            <div class="flash-icon ${message.type}" style="width: 24px; height: 24px; font-size: 14px;">${getIcon(message.type)}</div>
            <div>
                <div style="font-weight: 600; margin-bottom: 2px;">${escapeHtml(message.title)}</div>
                ${message.text ? `<div style="font-size: 14px; opacity: 0.8;">${escapeHtml(message.text)}</div>` : ''}
            </div>
            ${message.options.timer ? '<div class="flash-toast-timer"></div>' : ''}
        `;

        // Style
        const colors = {
            success: 'linear-gradient(135deg, #10b981, #059669)',
            error: 'linear-gradient(135deg, #ef4444, #dc2626)',
            warning: 'linear-gradient(135deg, #f59e0b, #d97706)',
            info: 'linear-gradient(135deg, #3b82f6, #2563eb)',
            question: 'linear-gradient(135deg, #6366f1, #4f46e5)',
            critical: 'linear-gradient(135deg, #dc2626, #991b1b)',
            urgent: 'linear-gradient(135deg, #ea580c, #c2410c)',
            security: 'linear-gradient(135deg, #991b1b, #7f1d1d)',
            payment: 'linear-gradient(135deg, #059669, #047857)',
            achievement: 'linear-gradient(135deg, #d97706, #b45309)'
        };

        toast.style.background = colors[message.type] || colors.info;
        toast.style.color = 'white';

        toastContainer.appendChild(toast);
        activeToasts.push(toast);

        // Show
        requestAnimationFrame(() => toast.classList.add('show'));

        // Timer
        if (message.options.timer) {
            const timerBar = toast.querySelector('.flash-toast-timer');
            if (timerBar) {
                timerBar.style.animation = `flash-timer ${message.options.timer}ms linear forwards`;
            }
        }

        // Auto dismiss
        setTimeout(() => dismissToast(toast), message.options.timer || 3000);

        // Click to dismiss
        toast.addEventListener('click', () => dismissToast(toast));

        playSound(message.type);
    }

    // Dismiss toast
    function dismissToast(toast) {
        toast.classList.add('hide');
        setTimeout(() => {
            if (toast.parentNode) toast.parentNode.removeChild(toast);
            const index = activeToasts.indexOf(toast);
            if (index > -1) activeToasts.splice(index, 1);
        }, 300);
    }

    // Close modal
    function closeModal() {
        if (!currentModal) return;
        currentModal.backdrop.classList.remove('show');
        setTimeout(() => {
            if (currentModal.backdrop.parentNode) {
                currentModal.backdrop.parentNode.removeChild(currentModal.backdrop);
            }
            currentModal = null;
        }, 300);
    }

    // Utility functions
    function getIcon(type) {
        const icons = {
            success: '✓', error: '✕', warning: '⚠', info: 'ℹ', question: '?',
            critical: '🚨', urgent: '⚡', security: '🛡️', payment: '💳', achievement: '🏆'
        };
        return icons[type] || icons.info;
    }

    function getBtnClass(type) {
        const classes = {
            primary: 'flash-btn-confirm', success: 'flash-btn-success',
            danger: 'flash-btn-danger', warning: 'flash-btn-warning'
        };
        return classes[type] || classes.primary;
    }

    function playSound(type) {
        if (config.enableSounds !== 'true') return;
        try {
            const frequencies = {
                success: 800, error: 300, warning: 600, info: 500, question: 700,
                critical: 200, urgent: 400, security: 350, payment: 750, achievement: 900
            };
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(frequencies[type] || 500, audioContext.currentTime);
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);
        } catch (e) {}
    }

    function triggerVibration(type) {
        if (config.enableVibration !== 'true' || !navigator.vibrate) return;
        const patterns = {
            success: [100], error: [100, 50, 100], warning: [200], info: [50], question: [100, 50, 50],
            critical: [200, 100, 200, 100, 200], urgent: [300], security: [100, 50, 100, 50, 100],
            payment: [150], achievement: [100, 50, 100, 50, 100, 50, 100]
        };
        navigator.vibrate(patterns[type] || [50]);
    }

    function showConfetti() {
        if (config.enableConfetti !== 'true') return;
        const colors = ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff', '#00ffff'];
        for (let i = 0; i < 50; i++) {
            setTimeout(() => {
                const confetti = document.createElement('div');
                confetti.className = 'flash-confetti';
                confetti.style.cssText = `top: -10px; left: ${Math.random() * 100}%; background: ${colors[Math.floor(Math.random() * colors.length)]};`;
                document.body.appendChild(confetti);
                setTimeout(() => {
                    if (confetti.parentNode) confetti.parentNode.removeChild(confetti);
                }, 3000);
            }, i * 10);
        }
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Public API
    window.FlashJS = {
        close: closeModal,
        confirm: function(messageId) {
            if (currentModal && currentModal.message.options.onConfirm) {
                const input = document.querySelector('#flash-input');
                const value = input ? input.value : null;

                if (typeof currentModal.message.options.onConfirm === 'string') {
                    if (value !== null) {
                        eval(currentModal.message.options.onConfirm.replace('{{value}}', `'${value}'`));
                    } else {
                        eval(currentModal.message.options.onConfirm);
                    }
                } else {
                    currentModal.message.options.onConfirm(value);
                }
            }
            closeModal();
        },
        cancel: function(messageId) {
            if (currentModal && currentModal.message.options.onCancel) {
                if (typeof currentModal.message.options.onCancel === 'string') {
                    eval(currentModal.message.options.onCancel);
                } else {
                    currentModal.message.options.onCancel();
                }
            }
            closeModal();
        },
        fire: function(options) {
            showModal({
                id: 'js_' + Date.now(),
                type: options.icon || 'info',
                title: options.title || '',
                text: options.text || '',
                options: options
            });
        }
    };

    // Initialize
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
</script>
