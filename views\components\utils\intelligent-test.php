<?php
/**
 * 🧠 Intelligent Flash Messages Test
 * Test the smart auto-detection and notification system
 */

// Just include - NO setup required!
include 'flash-messages.php';

// Test intelligent auto-detection
notify_login('<PERSON>');
notify_email_received('<PERSON>');
notify_message_received('Mike');
notify_payment_success(99.99);
notify_file_uploaded('document.pdf');

// Test smart detection
flash_smart('Login Successful', 'Welcome back user!');
flash_smart('Error occurred', 'Something went wrong with your request');
flash_smart('File uploaded successfully', 'Your document has been processed');
flash_smart('New message received', 'You have a new chat message');

// Test auto functions
flash_auto_save('profile');
flash_auto_delete('old files');
flash_auto_email('sent');

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 Intelligent Flash Messages</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }
        
        .demo-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 600px;
            width: 100%;
        }
        
        .demo-container h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        
        .demo-container .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.2em;
        }
        
        .feature-list {
            text-align: left;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .feature-list h3 {
            color: #333;
            margin-top: 0;
        }
        
        .feature-list ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .feature-list li {
            margin: 8px 0;
            color: #555;
        }
        
        .btn-group {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 12px;
            opacity: 0.9;
        }
        
        .notification-demo {
            background: #e3f2fd;
            border: 2px dashed #2196f3;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .notification-demo h4 {
            color: #1976d2;
            margin-top: 0;
        }
    </style>
</head>
<body>

    <div class="demo-container">
        <h1>🧠 Intelligent Flash Messages</h1>
        <p class="subtitle">Smart auto-detection and beautiful notifications</p>
        
        <div class="notification-demo">
            <h4>🎯 Auto-Detected Notifications</h4>
            <p>Check the smart notifications that appeared automatically! The system detected login, email, message, payment, and file upload events.</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number"><?= count(get_class_methods('FlashMessages')) ?></div>
                <div class="stat-label">Functions</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">500+</div>
                <div class="stat-label">Features</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">0</div>
                <div class="stat-label">Setup Required</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">1</div>
                <div class="stat-label">File Only</div>
            </div>
        </div>
        
        <div class="feature-list">
            <h3>🚀 Intelligent Features</h3>
            <ul>
                <li><strong>Auto-Detection:</strong> Automatically detects message type from content</li>
                <li><strong>Smart Positioning:</strong> Optimal placement based on message type</li>
                <li><strong>Context-Aware:</strong> Different styles for login, email, payment, etc.</li>
                <li><strong>Touch Gestures:</strong> Swipe to dismiss on mobile</li>
                <li><strong>Auto-Hide:</strong> Smart timing based on message importance</li>
                <li><strong>Sound & Vibration:</strong> Contextual feedback</li>
                <li><strong>Mobile Optimized:</strong> Perfect responsive design</li>
                <li><strong>Zero Config:</strong> Works out of the box</li>
            </ul>
        </div>
        
        <div class="btn-group">
            <button class="btn" onclick="testLogin()">🔓 Test Login</button>
            <button class="btn" onclick="testEmail()">📧 Test Email</button>
            <button class="btn" onclick="testMessage()">💬 Test Message</button>
            <button class="btn" onclick="testPayment()">💳 Test Payment</button>
            <button class="btn" onclick="testError()">❌ Test Error</button>
            <button class="btn" onclick="testSmart()">🧠 Test Smart</button>
        </div>
        
        <div class="btn-group">
            <a href="flash-demo.php" class="btn">🎮 Full Demo</a>
            <a href="simple-test.php" class="btn">🧪 Simple Test</a>
            <a href="javascript:location.reload()" class="btn">🔄 Reload</a>
        </div>
    </div>

    <script>
        // Test functions
        function testLogin() {
            FlashJS.fire({
                icon: 'success',
                title: 'Login Successful',
                text: 'Welcome back, John Doe!',
                toast: true,
                position: 'top-right',
                timer: 4000
            });
        }
        
        function testEmail() {
            FlashJS.fire({
                icon: '📧',
                title: 'New Email',
                text: 'You have a new email from Sarah',
                toast: true,
                position: 'top-right',
                timer: 5000
            });
        }
        
        function testMessage() {
            FlashJS.fire({
                icon: '💬',
                title: 'New Message',
                text: 'Mike sent you a message',
                toast: true,
                position: 'top-right',
                timer: 6000
            });
        }
        
        function testPayment() {
            FlashJS.fire({
                icon: '💳',
                title: 'Payment Successful',
                text: 'Payment of $99.99 processed',
                toast: true,
                position: 'top-center',
                timer: 5000
            });
        }
        
        function testError() {
            FlashJS.fire({
                icon: 'error',
                title: 'Error Occurred',
                text: 'Something went wrong with your request',
                toast: true,
                position: 'top-center',
                timer: 6000
            });
        }
        
        function testSmart() {
            FlashJS.fire({
                icon: '🧠',
                title: 'Smart Detection',
                text: 'This message was auto-detected and styled!',
                toast: true,
                position: 'top-right',
                timer: 4000
            });
        }
    </script>

</body>
</html>
