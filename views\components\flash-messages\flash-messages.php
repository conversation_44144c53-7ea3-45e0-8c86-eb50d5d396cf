<?php
/**
 * 🚀 ULTIMATE FLASH MESSAGES UI RENDERER
 * Ultra-beautiful, responsive notification display system
 *
 * ⭐ FEATURES:
 * - Ultra-premium design quality
 * - Smooth professional animations
 * - Fully responsive (mobile-first)
 * - Touch gesture support
 * - Multiple stunning themes
 * - Smart positioning system
 * - Accessibility compliant
 * - Enterprise-grade security
 *
 * @version 3.0.0
 * @license Commercial/MIT Dual License
 * <AUTHOR> Development Team
 */

// Ensure configuration is loaded
if (!class_exists('FlashMessagesConfig')) {
    require_once __DIR__ . '/setup.php';
}

// Ensure FlashMessage class is loaded
if (!class_exists('FlashMessage')) {
    require_once __DIR__ . '/flash.php';
}

// Get flash messages from session
$flashMessages = FlashMessage::get();
$config = FlashMessagesConfig::getJSConfig();

// Only render if there are messages or if we need to initialize the system
?>

<!-- Flash Messages System Styles -->
<style id="flash-messages-styles">
<?= FlashMessagesConfig::getCSSVariables() ?>

/* 🎨 Base Styles */
.flash-messages-container {
    position: fixed;
    z-index: 999999;
    pointer-events: none;
    font-family: var(--flash-font-family);
    font-size: var(--flash-font-size);
}

.flash-message {
    pointer-events: auto;
    position: relative;
    margin-bottom: var(--flash-stack-spacing);
    max-width: var(--flash-max-width);
    min-width: var(--flash-min-width);
    border-radius: var(--flash-border-radius);
    box-shadow: var(--flash-shadow);
    backdrop-filter: <?= FlashMessagesConfig::$backdrop === 'blur' ? 'blur(10px)' : 'none' ?>;
    transition: all var(--flash-animation-duration) var(--flash-animation-easing);
    overflow: hidden;
    cursor: <?= FlashMessagesConfig::$clickToDismiss ? 'pointer' : 'default' ?>;
}

/* 🎭 Animation Classes */
.flash-enter {
    opacity: 0;
    transform: translateX(100%);
}

.flash-enter-active {
    opacity: 1;
    transform: translateX(0);
}

.flash-exit {
    opacity: 1;
    transform: translateX(0);
}

.flash-exit-active {
    opacity: 0;
    transform: translateX(100%);
}

/* 📱 Responsive Design */
@media (max-width: <?= FlashMessagesConfig::$mobileBreakpoint ?>px) {
    .flash-messages-container {
        left: 10px !important;
        right: 10px !important;
        width: auto !important;
    }
    
    .flash-message {
        max-width: none;
        min-width: none;
        width: 100%;
    }
}

/* 🌙 Dark Mode */
@media (prefers-color-scheme: dark) {
    .flash-message {
        --flash-bg-opacity: 0.95;
    }
}

/* ♿ Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .flash-message {
        transition-duration: 0.1s;
    }
}

/* 🎨 Theme Styles */
<?php include __DIR__ . '/themes.css'; ?>

/* 🎪 Special Effects */
<?php if (FlashMessagesConfig::$enableGlow): ?>
.flash-message.glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4), var(--flash-shadow);
}
<?php endif; ?>

<?php if (FlashMessagesConfig::$enableShake): ?>
@keyframes flash-shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.flash-message.shake {
    animation: flash-shake 0.5s ease-in-out;
}
<?php endif; ?>

<?php if (FlashMessagesConfig::$enablePulse): ?>
@keyframes flash-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.flash-message.pulse {
    animation: flash-pulse 1s ease-in-out infinite;
}
<?php endif; ?>

/* 🎯 Progress Bar */
<?php if (FlashMessagesConfig::$showProgress): ?>
.flash-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: rgba(255, 255, 255, 0.3);
    transition: width linear;
}
<?php endif; ?>

/* 📱 Touch Gestures */
<?php if (FlashMessagesConfig::$enableGestures): ?>
.flash-message.swiping {
    transition: transform 0.1s ease-out;
}
<?php endif; ?>

/* 🎨 Custom CSS */
<?= FlashMessagesConfig::$customCSS ?>
</style>

<!-- Flash Messages Container -->
<div id="flash-messages-container" class="flash-messages-container"></div>

<!-- 🎯 FLASH MESSAGES CONTAINER -->
<div id="flash-messages-container" class="flash-messages-container flash-position-<?= str_replace('_', '-', FlashMessagesConfig::$defaultPosition) ?> flash-theme-<?= FlashMessagesConfig::$theme ?>">
    <!-- Messages will be dynamically inserted here -->
</div>

<!-- 🔐 LICENSE WATERMARK -->
<?php if (FlashMessagesConfig::$enableWatermark && FlashMessagesConfig::$activationStatus !== 'active'): ?>
<div class="flash-watermark">
    Powered by Ultimate Flash Messages <?= FlashMessagesConfig::$activationStatus === 'trial' ? '(Trial)' : '' ?>
</div>
<?php endif; ?>

<!-- 🚀 ULTIMATE FLASH MESSAGES JAVASCRIPT ENGINE -->
<script id="flash-messages-script">
(function() {
    'use strict';

    // 🔧 Configuration
    const config = <?= json_encode($config) ?>;

    // 🎯 Global Variables
    let messageQueue = [];
    let activeMessages = [];
    let container = null;
    let soundEnabled = config.enableSounds;
    let isDarkMode = false;
    let isRTL = false;
    let messageHistory = [];
    let analytics = [];

    // 🎨 Icons
    const icons = {
        success: '✅',
        error: '❌',
        warning: '⚠️',
        info: 'ℹ️',
        loading: '⏳',
        close: '✕'
    };
    
    // 🚀 Initialize System
    function initFlashSystem() {
        try {
            container = document.getElementById('flash-messages-container');
            if (!container) {
                container = createContainer();
            }
            
            setupContainer();
            detectDarkMode();
            setupKeyboardListeners();
            setupGestureListeners();
            
            // Process existing messages
            <?php if (!empty($flashMessages)): ?>
            const existingMessages = <?= json_encode($flashMessages) ?>;
            existingMessages.forEach((msg, index) => {
                setTimeout(() => {
                    showMessage(msg.type, msg.message, msg.title || '', msg.options || {});
                }, index * config['stagger-delay']);
            });
            <?php endif; ?>
            
            console.log('🚀 Flash Messages System initialized');
        } catch (error) {
            console.error('Flash system initialization failed:', error);
            showEmergencyMessage('Notification system failed to initialize');
        }
    }
    
    // 🏗️ Create Container
    function createContainer() {
        const div = document.createElement('div');
        div.id = 'flash-messages-container';
        div.className = 'flash-messages-container';
        document.body.appendChild(div);
        return div;
    }
    
    // ⚙️ Setup Container
    function setupContainer() {
        const position = config['default-position'];
        const [vAlign, hAlign] = position.split('-');
        
        // Position container
        container.style.cssText = getContainerPosition(vAlign, hAlign);
        
        // Mobile responsive
        if (window.innerWidth <= config['mobile-breakpoint']) {
            setupMobileContainer();
        }
    }
    
    // 📱 Mobile Container Setup
    function setupMobileContainer() {
        if (config['mobile-full-width']) {
            container.style.left = '10px';
            container.style.right = '10px';
            container.style.width = 'auto';
        }
        
        if (config['mobile-position'] !== config['default-position']) {
            const [vAlign, hAlign] = config['mobile-position'].split('-');
            container.style.cssText = getContainerPosition(vAlign, hAlign);
        }
    }
    
    // 📍 Get Container Position
    function getContainerPosition(vAlign, hAlign) {
        let css = '';
        
        // Vertical alignment
        if (vAlign === 'top') {
            css += `top: ${config['offset-y']}px;`;
        } else if (vAlign === 'bottom') {
            css += `bottom: ${config['offset-y']}px;`;
        } else {
            css += 'top: 50%; transform: translateY(-50%);';
        }
        
        // Horizontal alignment
        if (hAlign === 'left') {
            css += `left: ${config['offset-x']}px;`;
        } else if (hAlign === 'right') {
            css += `right: ${config['offset-x']}px;`;
        } else {
            css += 'left: 50%; transform: translateX(-50%);';
            if (vAlign === 'center') {
                css += 'transform: translate(-50%, -50%);';
            }
        }
        
        return css;
    }
    
    // 🎨 Detect Dark Mode
    function detectDarkMode() {
        if (config['enable-dark-mode'] === 'auto') {
            isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
            
            // Listen for changes
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
                isDarkMode = e.matches;
                updateTheme();
            });
        } else {
            isDarkMode = config['enable-dark-mode'] === 'always';
        }
        
        updateTheme();
    }
    
    // 🎨 Update Theme
    function updateTheme() {
        document.documentElement.setAttribute('data-flash-theme', isDarkMode ? 'dark' : 'light');
    }
    
    // ⌨️ Setup Keyboard Listeners
    function setupKeyboardListeners() {
        if (!config['enable-keyboard']) return;
        
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && config['escape-key-dismiss']) {
                dismissAllMessages();
            }
        });
    }
    
    // 👆 Setup Gesture Listeners
    function setupGestureListeners() {
        if (!config['enable-gestures']) return;
        
        let startX = 0;
        let startY = 0;
        let currentMessage = null;
        
        container.addEventListener('touchstart', (e) => {
            if (!config['swipe-to-dismiss']) return;
            
            const touch = e.touches[0];
            startX = touch.clientX;
            startY = touch.clientY;
            currentMessage = e.target.closest('.flash-message');
        });
        
        container.addEventListener('touchmove', (e) => {
            if (!currentMessage || !config['swipe-to-dismiss']) return;
            
            const touch = e.touches[0];
            const deltaX = touch.clientX - startX;
            const deltaY = touch.clientY - startY;
            
            // Only horizontal swipes
            if (Math.abs(deltaY) > Math.abs(deltaX)) return;
            
            currentMessage.style.transform = `translateX(${deltaX}px)`;
            currentMessage.classList.add('swiping');
        });
        
        container.addEventListener('touchend', (e) => {
            if (!currentMessage) return;
            
            const touch = e.changedTouches[0];
            const deltaX = touch.clientX - startX;
            
            currentMessage.classList.remove('swiping');
            
            if (Math.abs(deltaX) > 100) {
                dismissMessage(currentMessage.id);
            } else {
                currentMessage.style.transform = '';
            }
            
            currentMessage = null;
        });
    }
    
    // 💬 Show Message (Main Function)
    window.showMessage = function(type, message, title = '', options = {}) {
        try {
            // Prevent duplicates
            if (config['prevent-duplicates'] && isDuplicate(message)) {
                return null;
            }
            
            // Queue if max stack reached
            if (activeMessages.length >= config['max-stack'] && config['queue-messages']) {
                messageQueue.push({type, message, title, options});
                return null;
            }
            
            const messageObj = createMessage(type, message, title, options);
            activeMessages.push(messageObj);
            
            renderMessage(messageObj);
            playSound(type);
            announceToScreenReader(message, type);
            
            // Auto-hide
            if (options.autoHide !== false && config['auto-hide']) {
                scheduleAutoHide(messageObj);
            }
            
            return messageObj.id;
        } catch (error) {
            console.error('Error showing message:', error);
            showEmergencyMessage('Failed to display notification: ' + message);
            return null;
        }
    };
    
    // 🏗️ Create Message Object
    function createMessage(type, message, title, options) {
        const id = 'flash-msg-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
        const duration = options.duration || config[type + '-duration'] || config['default-duration'];

        return {
            id,
            type,
            message,
            title,
            options,
            duration,
            element: null,
            timer: null,
            createdAt: Date.now()
        };
    }

    // 🎨 Render Message
    function renderMessage(messageObj) {
        const element = createMessageElement(messageObj);
        messageObj.element = element;

        // Add to container
        if (config['stack-direction'] === 'up') {
            container.insertBefore(element, container.firstChild);
        } else {
            container.appendChild(element);
        }

        // Trigger enter animation
        requestAnimationFrame(() => {
            element.classList.add('flash-enter-active');
            element.classList.remove('flash-enter');
        });

        // Setup interactions
        setupMessageInteractions(messageObj);
    }

    // 🎭 Create Message Element
    function createMessageElement(messageObj) {
        const { id, type, message, title, options } = messageObj;

        const element = document.createElement('div');
        element.id = id;
        element.className = `flash-message flash-${type} flash-enter theme-${config.theme}`;

        // Add special effects
        if (type === 'error' && config['enable-shake']) {
            element.classList.add('shake');
        }
        if (options.important && config['enable-pulse']) {
            element.classList.add('pulse');
        }
        if (options.glow && config['enable-glow']) {
            element.classList.add('glow');
        }

        element.innerHTML = createMessageHTML(messageObj);

        return element;
    }

    // 📝 Create Message HTML
    function createMessageHTML(messageObj) {
        const { type, message, title, options } = messageObj;
        const showIcon = options.showIcon !== false;
        const dismissible = options.dismissible !== false && config.dismissible;

        return `
            <div class="flash-content">
                ${showIcon ? `<div class="flash-icon">${getIcon(type)}</div>` : ''}
                <div class="flash-body">
                    ${title ? `<div class="flash-title">${escapeHtml(title)}</div>` : ''}
                    <div class="flash-message">${escapeHtml(message)}</div>
                </div>
                ${dismissible ? `<button class="flash-close" aria-label="Close">${getCloseIcon()}</button>` : ''}
            </div>
            ${config['show-progress'] ? '<div class="flash-progress"></div>' : ''}
        `;
    }

    // 🎵 Play Sound
    function playSound(type) {
        if (!soundEnabled || !config['enable-sounds']) return;

        try {
            const soundFile = config['custom-sounds'][type];
            if (soundFile) {
                const audio = new Audio(config['sound-path'] + soundFile);
                audio.volume = config['sound-volume'];
                audio.play().catch(() => {
                    // Sound failed, continue silently
                });
            }
        } catch (error) {
            // Sound system failed, continue silently
        }
    }

    // ♿ Announce to Screen Reader
    function announceToScreenReader(message, type) {
        if (!config['announce-to-screen-reader'] || !config['enable-a11y']) return;

        const announcement = document.createElement('div');
        announcement.setAttribute('aria-live', type === 'error' ? 'assertive' : 'polite');
        announcement.setAttribute('aria-atomic', 'true');
        announcement.className = 'sr-only';
        announcement.textContent = `${type}: ${message}`;

        document.body.appendChild(announcement);

        setTimeout(() => {
            document.body.removeChild(announcement);
        }, 1000);
    }

    // ⏰ Schedule Auto Hide
    function scheduleAutoHide(messageObj) {
        const duration = messageObj.duration;

        messageObj.timer = setTimeout(() => {
            dismissMessage(messageObj.id);
        }, duration);

        // Progress bar
        if (config['show-progress'] && messageObj.element) {
            const progressBar = messageObj.element.querySelector('.flash-progress');
            if (progressBar) {
                progressBar.style.width = '100%';
                progressBar.style.transitionDuration = duration + 'ms';

                requestAnimationFrame(() => {
                    progressBar.style.width = '0%';
                });
            }
        }
    }

    // 🎯 Setup Message Interactions
    function setupMessageInteractions(messageObj) {
        const element = messageObj.element;

        // Click to dismiss
        if (config['click-to-dismiss']) {
            element.addEventListener('click', (e) => {
                if (!e.target.closest('.flash-close')) {
                    dismissMessage(messageObj.id);
                }
            });
        }

        // Close button
        const closeBtn = element.querySelector('.flash-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                dismissMessage(messageObj.id);
            });
        }

        // Pause on hover
        if (config['pause-on-hover'] && messageObj.timer) {
            element.addEventListener('mouseenter', () => {
                clearTimeout(messageObj.timer);
                const progressBar = element.querySelector('.flash-progress');
                if (progressBar) {
                    progressBar.style.animationPlayState = 'paused';
                }
            });

            element.addEventListener('mouseleave', () => {
                if (config['resume-on-leave']) {
                    scheduleAutoHide(messageObj);
                }
            });
        }
    }

    // 🗑️ Dismiss Message
    window.dismissMessage = function(messageId) {
        const messageObj = activeMessages.find(msg => msg.id === messageId);
        if (!messageObj || !messageObj.element) return;

        const element = messageObj.element;

        // Clear timer
        if (messageObj.timer) {
            clearTimeout(messageObj.timer);
        }

        // Exit animation
        element.classList.add('flash-exit-active');
        element.classList.remove('flash-enter-active');

        setTimeout(() => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }

            // Remove from active messages
            const index = activeMessages.findIndex(msg => msg.id === messageId);
            if (index > -1) {
                activeMessages.splice(index, 1);
            }

            // Process queue
            processQueue();

        }, config['animation-duration']);
    };

    // 🗑️ Dismiss All Messages
    window.dismissAllMessages = function() {
        activeMessages.forEach(msg => dismissMessage(msg.id));
    };

    // 📋 Process Queue
    function processQueue() {
        if (messageQueue.length > 0 && activeMessages.length < config['max-stack']) {
            const next = messageQueue.shift();
            showMessage(next.type, next.message, next.title, next.options);
        }
    }

    // 🔍 Check Duplicate
    function isDuplicate(message) {
        return activeMessages.some(msg => msg.message === message);
    }

    // 🚨 Emergency Message
    window.showEmergencyMessage = function(message) {
        try {
            const emergency = document.createElement('div');
            emergency.innerHTML = `
                <div style="position:fixed;top:20px;right:20px;background:#dc2626;color:white;padding:15px;border-radius:8px;z-index:9999999;max-width:300px;box-shadow:0 4px 6px rgba(0,0,0,0.3);font-family:system-ui,-apple-system,sans-serif;">
                    <strong>⚠️ System Alert:</strong><br>
                    ${escapeHtml(message)}
                    <button onclick="this.parentElement.parentElement.remove()"
                            style="float:right;background:none;border:none;color:white;font-size:18px;cursor:pointer;margin-top:-5px;line-height:1;">&times;</button>
                </div>
            `;
            document.body.appendChild(emergency);

            setTimeout(() => {
                if (emergency.parentNode) {
                    emergency.parentNode.removeChild(emergency);
                }
            }, 10000);
        } catch (error) {
            alert('System Alert: ' + message);
        }
    };

    // 🛠️ Utility Functions
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function getIcon(type) {
        const icons = {
            success: '<svg viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>',
            error: '<svg viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>',
            warning: '<svg viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>',
            info: '<svg viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>'
        };
        return icons[type] || icons.info;
    }

    function getCloseIcon() {
        return '<svg viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>';
    }

    // 🎯 Public API
    window.showSuccess = (msg, title, opts) => showMessage('success', msg, title, opts);
    window.showError = (msg, title, opts) => showMessage('error', msg, title, opts);
    window.showWarning = (msg, title, opts) => showMessage('warning', msg, title, opts);
    window.showInfo = (msg, title, opts) => showMessage('info', msg, title, opts);

    // 🚀 Initialize on DOM ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initFlashSystem);
    } else {
        initFlashSystem();
    }
    
})();
</script>

<?php
// Clear flash messages from session after displaying
FlashMessage::clear();
?>
