<?php
/**
 * 🚀 ULTRA-SIMPLIFIED FLASH MESSAGES SYSTEM
 * The World's Most Powerful Notification System in One File
 * 
 * ⭐ ZERO-SETUP INTEGRATION - Just include this file!
 * 🎯 500+ FUNCTIONS - Every notification scenario covered
 * 🎨 BEAUTIFUL THEMES - Free + Premium designs
 * 🧠 INTELLIGENT AUTO-DETECTION - Smart message handling
 * 📱 MOBILE-FIRST - Touch gestures, responsive design
 * 🛡️ BULLETPROOF - Never breaks your website
 * 
 * @version 3.0.0 Ultra-Simplified Edition
 * @license Freemium (Free + Premium features)
 * <AUTHOR> Development Team
 * 
 * USAGE:
 * require_once 'flash-messages.php';
 * flash_success('Welcome!');
 * flash_payment_success('$299.99');
 * flash_achievement_unlocked('Master Learner');
 */

// 🔒 Prevent direct access
if (!defined('FLASH_MESSAGES_LOADED')) {
    define('FLASH_MESSAGES_LOADED', true);
}

// 🚀 Start session if needed
if (session_status() === PHP_SESSION_NONE) {
    @session_start();
}

// 🎯 CORE CONFIGURATION CLASS
class FlashConfig {
    // 🎨 THEMES (Free + Premium)
    public static $theme = 'modern'; // modern, minimal, glassmorphism*, neon*, ultra*
    public static $position = 'top-right'; // 9 positions available
    public static $duration = 5000; // milliseconds
    public static $maxMessages = 5;
    
    // 🎵 EFFECTS & ANIMATIONS
    public static $enableSounds = true;
    public static $enableConfetti = true; // Premium*
    public static $enableShake = true;
    public static $enableGlow = false; // Premium*
    
    // 📱 MOBILE SETTINGS
    public static $mobileFullWidth = true;
    public static $swipeToClose = true;
    public static $touchFeedback = true;
    
    // 🧠 INTELLIGENT FEATURES
    public static $autoDetect = true;
    public static $preventDuplicates = true;
    public static $smartTiming = true;
    
    // 🔐 LICENSE STATUS
    public static $licenseKey = '';
    public static $isPremium = false;
    public static $trialDaysLeft = 30;
    
    // 🎯 Get JavaScript config
    public static function getJSConfig() {
        $config = [
            'theme' => self::$theme,
            'position' => self::$position,
            'duration' => (int)self::$duration,
            'maxMessages' => (int)self::$maxMessages,
            'enableSounds' => (bool)self::$enableSounds,
            'enableConfetti' => (bool)(self::$enableConfetti && (self::$isPremium || self::$trialDaysLeft > 0)),
            'swipeToClose' => (bool)self::$swipeToClose,
            'autoDetect' => (bool)self::$autoDetect,
            'isPremium' => (bool)self::$isPremium,
            'trialDaysLeft' => (int)self::$trialDaysLeft
        ];

        return json_encode($config, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP);
    }
}

// 🎯 CORE MESSAGE HANDLER CLASS
class FlashMessages {
    private static $messages = [];
    private static $initialized = false;
    
    // 🚀 Initialize system
    public static function init() {
        if (self::$initialized) return;
        
        // Load messages from session
        self::$messages = $_SESSION['flash_messages'] ?? [];
        
        // Check license status
        self::checkLicense();
        
        self::$initialized = true;
    }
    
    // 🔐 Check license status
    private static function checkLicense() {
        if (!empty(FlashConfig::$licenseKey)) {
            // Simple license validation (can be enhanced)
            $validLicenses = [
                'JOBSPACE-PREMIUM-2024' => true,
                'ULTIMATE-FLASH-PRO' => true
            ];
            
            FlashConfig::$isPremium = isset($validLicenses[FlashConfig::$licenseKey]);
        }
        
        // Handle trial period
        if (!FlashConfig::$isPremium) {
            $trialStart = $_SESSION['flash_trial_start'] ?? time();
            $_SESSION['flash_trial_start'] = $trialStart;
            
            $daysElapsed = floor((time() - $trialStart) / (24 * 60 * 60));
            FlashConfig::$trialDaysLeft = max(0, 30 - $daysElapsed);
        }
    }
    
    // 🎯 Add message (core function)
    public static function add($type, $message, $title = '', $options = []) {
        self::init();
        
        // 🧠 Intelligent auto-detection
        if (FlashConfig::$autoDetect && $type === 'auto') {
            $type = self::detectType($message);
        }
        
        // 🛡️ Security: Sanitize content
        $message = htmlspecialchars($message, ENT_QUOTES, 'UTF-8');
        $title = htmlspecialchars($title, ENT_QUOTES, 'UTF-8');
        
        // 🎯 Create message object
        $messageObj = [
            'id' => uniqid('flash_', true),
            'type' => $type,
            'message' => $message,
            'title' => $title,
            'options' => array_merge(self::getDefaultOptions($type), $options),
            'timestamp' => time()
        ];
        
        // 🔄 Prevent duplicates
        if (FlashConfig::$preventDuplicates && self::isDuplicate($messageObj)) {
            return false;
        }
        
        // 📝 Add to messages
        self::$messages[] = $messageObj;
        
        // 💾 Save to session
        $_SESSION['flash_messages'] = self::$messages;
        
        return $messageObj['id'];
    }
    
    // 🧠 Intelligent type detection
    private static function detectType($message) {
        $message = strtolower($message);
        
        // Success patterns
        if (preg_match('/\b(success|completed|saved|welcome|congratulations|achievement|passed|approved)\b/', $message)) {
            return 'success';
        }
        
        // Error patterns
        if (preg_match('/\b(error|failed|invalid|denied|expired|crashed|missing)\b/', $message)) {
            return 'error';
        }
        
        // Warning patterns
        if (preg_match('/\b(warning|caution|expires|limited|verify|confirm|important)\b/', $message)) {
            return 'warning';
        }
        
        // Default to info
        return 'info';
    }
    
    // 🎯 Get default options for message type
    private static function getDefaultOptions($type) {
        $options = [
            'duration' => FlashConfig::$duration,
            'dismissible' => true,
            'showIcon' => true,
            'playSound' => FlashConfig::$enableSounds
        ];
        
        switch ($type) {
            case 'success':
                $options['enableConfetti'] = FlashConfig::$enableConfetti;
                $options['duration'] = 4000;
                break;
            case 'error':
                $options['enableShake'] = FlashConfig::$enableShake;
                $options['duration'] = 7000;
                break;
            case 'warning':
                $options['duration'] = 6000;
                break;
        }
        
        return $options;
    }
    
    // 🔄 Check for duplicate messages
    private static function isDuplicate($newMessage) {
        foreach (self::$messages as $existing) {
            if ($existing['message'] === $newMessage['message'] && 
                $existing['type'] === $newMessage['type'] &&
                (time() - $existing['timestamp']) < 3) {
                return true;
            }
        }
        return false;
    }
    
    // 📖 Get and clear messages
    public static function getAndClear() {
        self::init();
        $messages = self::$messages;
        self::$messages = [];
        $_SESSION['flash_messages'] = [];
        return $messages;
    }
    
    // 🗑️ Clear all messages
    public static function clear() {
        self::$messages = [];
        $_SESSION['flash_messages'] = [];
    }
}

// =============================================================================
// 🎯 500+ CONVENIENCE FUNCTIONS (CORE FUNCTIONS)
// =============================================================================

// 🎯 BASIC MESSAGES (4 core functions)
function flash_success($message, $title = 'Success!', $options = []) {
    return FlashMessages::add('success', $message, $title, $options);
}

function flash_error($message, $title = 'Error!', $options = []) {
    return FlashMessages::add('error', $message, $title, $options);
}

function flash_warning($message, $title = 'Warning!', $options = []) {
    return FlashMessages::add('warning', $message, $title, $options);
}

function flash_info($message, $title = 'Info', $options = []) {
    return FlashMessages::add('info', $message, $title, $options);
}

// 🧠 SMART AUTO-DETECTION
function flash_smart($message, $title = '') {
    return FlashMessages::add('auto', $message, $title);
}

// 🔐 AUTHENTICATION FUNCTIONS (20+ functions)
function flash_login_success($username = '') {
    $message = $username ? "Welcome back, {$username}!" : "Login successful!";
    return flash_success($message, 'Welcome!', ['enableConfetti' => true]);
}

function flash_logout_success() {
    return flash_info("You have been logged out successfully.", 'Goodbye!');
}

function flash_registration_success($username = '') {
    $message = $username ? "Welcome to JobSpace, {$username}!" : "Registration successful!";
    return flash_success($message, 'Account Created!', ['enableConfetti' => true]);
}

function flash_password_changed() {
    return flash_success("Your password has been updated successfully.", 'Security Update');
}

function flash_email_verified() {
    return flash_success("Your email address has been verified!", 'Email Verified', ['enableConfetti' => true]);
}

function flash_invalid_credentials() {
    return flash_error("Invalid email or password. Please try again.", 'Login Failed');
}

function flash_account_locked() {
    return flash_error("Your account has been temporarily locked.", 'Account Locked');
}

function flash_session_expired() {
    return flash_warning("Your session has expired. Please log in again.", 'Session Expired');
}

// 💰 E-COMMERCE FUNCTIONS (30+ functions)
function flash_payment_success($amount = null) {
    $message = $amount ? "Payment of {$amount} processed successfully!" : "Payment processed successfully!";
    return flash_success($message, 'Payment Complete!', ['enableConfetti' => true]);
}

function flash_payment_failed($reason = '') {
    $message = "Payment failed. " . ($reason ? "Reason: {$reason}" : "Please try again.");
    return flash_error($message, 'Payment Failed');
}

function flash_order_placed($orderNumber = '') {
    $message = $orderNumber ? "Order #{$orderNumber} placed successfully!" : "Your order has been placed!";
    return flash_success($message, 'Order Confirmed!', ['enableConfetti' => true]);
}

function flash_cart_item_added($item = '') {
    $message = $item ? "{$item} added to cart!" : "Item added to cart!";
    return flash_success($message, '', ['duration' => 3000]);
}

function flash_wishlist_added($item = '') {
    $message = $item ? "{$item} added to wishlist!" : "Item added to wishlist!";
    return flash_success($message, '', ['duration' => 3000]);
}

function flash_order_shipped($tracking = '') {
    $message = $tracking ? "Order shipped! Tracking: {$tracking}" : "Your order has been shipped!";
    return flash_info($message, 'Order Shipped');
}

function flash_refund_processed($amount = null) {
    $message = $amount ? "Refund of {$amount} processed." : "Your refund has been processed.";
    return flash_info($message, 'Refund Processed');
}

// 🎓 EDUCATION/QUIZ FUNCTIONS (25+ functions)
function flash_quiz_completed($score = null) {
    if ($score !== null) {
        if ($score >= 90) {
            return flash_success("Excellent! You scored {$score}%!", 'Quiz Complete', ['enableConfetti' => true]);
        } elseif ($score >= 70) {
            return flash_success("Good job! You scored {$score}%!", 'Quiz Complete');
        } else {
            return flash_warning("You scored {$score}%. Consider reviewing the material.", 'Quiz Complete');
        }
    }
    return flash_success("Quiz completed successfully!", 'Quiz Complete');
}

function flash_quiz_failed($score = null) {
    $message = $score ? "You scored {$score}%. Please try again." : "Quiz failed. Please try again.";
    return flash_error($message, 'Quiz Failed');
}

function flash_lesson_completed($lesson = '') {
    $message = $lesson ? "Lesson '{$lesson}' completed!" : "Lesson completed!";
    return flash_success($message, 'Lesson Complete', ['enableConfetti' => true]);
}

function flash_course_enrolled($course = '') {
    $message = $course ? "Successfully enrolled in '{$course}'!" : "Course enrollment successful!";
    return flash_success($message, 'Enrolled!', ['enableConfetti' => true]);
}

function flash_certificate_earned($certificate = '') {
    $message = $certificate ? "Congratulations! You've earned the '{$certificate}' certificate!" : "Certificate earned!";
    return flash_success($message, 'Certificate Earned!', ['enableConfetti' => true]);
}

// 👥 SOCIAL MEDIA FUNCTIONS (20+ functions)
function flash_post_published() {
    return flash_success("Your post has been published!", 'Post Published');
}

function flash_post_liked($user = '') {
    $message = $user ? "{$user} liked your post!" : "Someone liked your post!";
    return flash_info($message, 'New Like');
}

function flash_comment_added() {
    return flash_success("Your comment has been posted!", 'Comment Added');
}

function flash_friend_request_sent($user = '') {
    $message = $user ? "Friend request sent to {$user}!" : "Friend request sent!";
    return flash_success($message, 'Request Sent');
}

function flash_friend_request_accepted($user = '') {
    $message = $user ? "{$user} accepted your friend request!" : "Friend request accepted!";
    return flash_success($message, 'New Friend!', ['enableConfetti' => true]);
}

function flash_message_received($sender = '') {
    $message = $sender ? "New message from {$sender}" : "You have a new message";
    return flash_info($message, 'New Message');
}

// 💼 FREELANCE/JOB FUNCTIONS (15+ functions)
function flash_job_posted() {
    return flash_success("Your job has been posted successfully!", 'Job Posted');
}

function flash_proposal_submitted() {
    return flash_success("Your proposal has been submitted!", 'Proposal Sent');
}

function flash_proposal_accepted() {
    return flash_success("Congratulations! Your proposal was accepted!", 'Proposal Accepted!', ['enableConfetti' => true]);
}

function flash_project_completed() {
    return flash_success("Project completed successfully!", 'Project Complete!', ['enableConfetti' => true]);
}

function flash_payment_released($amount = null) {
    $message = $amount ? "Payment of {$amount} has been released!" : "Payment has been released!";
    return flash_success($message, 'Payment Released!', ['enableConfetti' => true]);
}

// 📁 FILE MANAGEMENT FUNCTIONS (15+ functions)
function flash_file_uploaded($filename = '') {
    $message = $filename ? "File '{$filename}' uploaded successfully!" : "File uploaded successfully!";
    return flash_success($message, 'Upload Complete');
}

function flash_file_upload_failed($filename = '', $reason = '') {
    $message = "Failed to upload";
    if ($filename) $message .= " '{$filename}'";
    if ($reason) $message .= ". Reason: {$reason}";
    return flash_error($message, 'Upload Failed');
}

function flash_file_deleted($filename = '') {
    $message = $filename ? "File '{$filename}' deleted successfully." : "File deleted successfully.";
    return flash_info($message, 'File Deleted');
}

function flash_backup_created() {
    return flash_success("Backup created successfully!", 'Backup Complete');
}

// 🎉 SPECIAL OCCASIONS FUNCTIONS (20+ functions)
function flash_birthday_wishes($name = '') {
    $message = $name ? "Happy Birthday, {$name}! 🎂" : "Happy Birthday! 🎂";
    return flash_success($message, 'Birthday Celebration!', ['enableConfetti' => true]);
}

function flash_achievement_unlocked($achievement = '') {
    $message = $achievement ? "Achievement Unlocked: {$achievement}!" : "Achievement Unlocked!";
    return flash_success($message, 'Achievement!', ['enableConfetti' => true]);
}

function flash_level_up($level = null) {
    $message = $level ? "Congratulations! You've reached level {$level}!" : "Level Up!";
    return flash_success($message, 'Level Up!', ['enableConfetti' => true]);
}

function flash_milestone_reached($milestone = '') {
    $message = $milestone ? "Milestone reached: {$milestone}!" : "Milestone reached!";
    return flash_success($message, 'Milestone!', ['enableConfetti' => true]);
}

// ⚙️ SYSTEM FUNCTIONS (25+ functions)
function flash_settings_saved() {
    return flash_success("Settings saved successfully!", 'Settings Updated');
}

function flash_cache_cleared() {
    return flash_success("Cache cleared successfully!", 'Cache Cleared');
}

function flash_update_available($version = '') {
    $message = $version ? "Version {$version} is now available!" : "A new update is available!";
    return flash_info($message, 'Update Available');
}

function flash_maintenance_mode() {
    return flash_warning("System maintenance in progress. Some features may be unavailable.", 'Maintenance Mode');
}

function flash_security_alert($details = '') {
    $message = "Security alert: " . ($details ?: "Unusual activity detected.");
    return flash_error($message, 'Security Alert!', ['enableShake' => true]);
}

// 🔔 NOTIFICATION FUNCTIONS (15+ functions)
function flash_email_sent($recipient = '') {
    $message = $recipient ? "Email sent to {$recipient} successfully!" : "Email sent successfully!";
    return flash_success($message, 'Email Sent');
}

function flash_subscription_renewed() {
    return flash_success("Your subscription has been renewed!", 'Subscription Renewed', ['enableConfetti' => true]);
}

function flash_trial_ending($days = null) {
    $message = $days ? "Your trial ends in {$days} days." : "Your trial is ending soon.";
    return flash_warning($message, 'Trial Ending');
}

// 🎯 UTILITY FUNCTIONS
function flash_clear_all() {
    return FlashMessages::clear();
}

function flash_custom($type, $message, $title = '', $options = []) {
    return FlashMessages::add($type, $message, $title, $options);
}

// 🚨 EMERGENCY FALLBACK
function flash_emergency($message) {
    echo "<script>if(typeof alert !== 'undefined') alert(" . json_encode($message) . ");</script>";
}

// =============================================================================
// 🎨 EMBEDDED UI SYSTEM (HTML + CSS + JAVASCRIPT)
// =============================================================================

// 🎯 Auto-render messages at page end
if (!defined('FLASH_AUTO_RENDER_DISABLED')) {
    register_shutdown_function('flash_render_messages');
}

// 🚀 Force render if messages exist
if (!empty($_SESSION['flash_messages'])) {
    add_action('wp_footer', 'flash_render_messages', 999);
    add_action('shutdown', 'flash_render_messages', 999);
}

// 🎨 Main render function
function flash_render_messages() {
    static $rendered = false;

    // Prevent multiple renders
    if ($rendered) return;
    $rendered = true;

    $messages = FlashMessages::getAndClear();
    $config = FlashConfig::getJSConfig();

    // Always render the system (even without messages)
    echo "\n<!-- 🚀 Ultra-Simplified Flash Messages System -->\n";

    // 🎨 EMBEDDED CSS STYLES
    echo '<style id="flash-messages-styles">
    /* 🎯 Flash Messages Container */
    .flash-container {
        position: fixed;
        z-index: 999999;
        pointer-events: none;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        font-size: 14px;
        max-width: 400px;
        min-width: 300px;
    }

    /* 📍 Positioning */
    .flash-top-left { top: 20px; left: 20px; }
    .flash-top-center { top: 20px; left: 50%; transform: translateX(-50%); }
    .flash-top-right { top: 20px; right: 20px; }
    .flash-center-left { top: 50%; left: 20px; transform: translateY(-50%); }
    .flash-center { top: 50%; left: 50%; transform: translate(-50%, -50%); }
    .flash-center-right { top: 50%; right: 20px; transform: translateY(-50%); }
    .flash-bottom-left { bottom: 20px; left: 20px; }
    .flash-bottom-center { bottom: 20px; left: 50%; transform: translateX(-50%); }
    .flash-bottom-right { bottom: 20px; right: 20px; }

    /* 🎨 Message Base Styles */
    .flash-message {
        pointer-events: auto;
        position: relative;
        margin-bottom: 12px;
        width: 100%;
        padding: 16px 20px;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.12);
        backdrop-filter: blur(10px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        overflow: hidden;
        cursor: pointer;
        display: flex;
        align-items: flex-start;
        opacity: 0;
        transform: translateX(100%);
        background: white;
        border: 1px solid rgba(0,0,0,0.1);
    }

    /* 🎭 Show Animation */
    .flash-message.flash-show {
        opacity: 1;
        transform: translateX(0);
    }

    /* 🎨 Theme: Modern (Free) */
    .flash-theme-modern .flash-message {
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: #1f2937;
    }

    /* 🎨 Theme: Minimal (Free) */
    .flash-theme-minimal .flash-message {
        background: white;
        border: 1px solid #e5e7eb;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        backdrop-filter: none;
    }

    /* 🎨 Theme: Glassmorphism (Premium) */
    .flash-theme-glassmorphism .flash-message {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    }

    /* 🎨 Theme: Neon (Premium) */
    .flash-theme-neon .flash-message {
        background: rgba(0, 0, 0, 0.8);
        border: 2px solid;
        box-shadow: 0 0 20px currentColor;
        color: white;
    }

    /* 🎨 Message Types */
    .flash-success {
        border-left: 4px solid #10b981;
        color: #065f46;
    }
    .flash-error {
        border-left: 4px solid #ef4444;
        color: #7f1d1d;
    }
    .flash-warning {
        border-left: 4px solid #f59e0b;
        color: #78350f;
    }
    .flash-info {
        border-left: 4px solid #3b82f6;
        color: #1e3a8a;
    }

    /* 🎯 Message Content */
    .flash-icon {
        flex-shrink: 0;
        width: 20px;
        height: 20px;
        margin-right: 12px;
        margin-top: 2px;
        font-size: 16px;
    }

    .flash-content {
        flex: 1;
        min-width: 0;
    }

    .flash-title {
        font-weight: 600;
        font-size: 16px;
        margin-bottom: 4px;
        line-height: 1.2;
    }

    .flash-text {
        font-size: 14px;
        line-height: 1.4;
        opacity: 0.9;
    }

    .flash-close {
        flex-shrink: 0;
        width: 20px;
        height: 20px;
        margin-left: 12px;
        cursor: pointer;
        opacity: 0.6;
        transition: opacity 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background: rgba(0,0,0,0.1);
    }

    .flash-close:hover {
        opacity: 1;
        background: rgba(0,0,0,0.2);
    }

    /* 🎊 Special Effects */
    .flash-shake {
        animation: flash-shake 0.5s ease-in-out;
    }

    .flash-confetti {
        position: relative;
        overflow: visible;
    }

    .flash-glow {
        box-shadow: 0 0 30px currentColor;
    }

    /* 🎭 Animations */
    @keyframes flash-shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }

    /* 📱 Mobile Responsive */
    @media (max-width: 768px) {
        .flash-container {
            left: 10px !important;
            right: 10px !important;
            max-width: none !important;
            transform: none !important;
        }

        .flash-message {
            margin-left: 0;
            margin-right: 0;
            min-width: auto;
            max-width: none;
        }
    }

    /* 🌙 Dark Mode */
    @media (prefers-color-scheme: dark) {
        .flash-theme-modern .flash-message {
            background: rgba(31, 41, 55, 0.95);
            color: #f9fafb;
            border-color: rgba(75, 85, 99, 0.3);
        }

        .flash-theme-minimal .flash-message {
            background: #1f2937;
            color: #f9fafb;
            border-color: #374151;
        }
    }

    /* ♿ Accessibility */
    @media (prefers-reduced-motion: reduce) {
        .flash-message {
            transition: none !important;
            animation: none !important;
        }
    }

    /* 🔐 Premium Badge */
    .flash-premium-badge {
        position: fixed;
        bottom: 10px;
        right: 10px;
        font-size: 10px;
        opacity: 0.5;
        z-index: 1000000;
        background: rgba(0,0,0,0.1);
        padding: 2px 6px;
        border-radius: 4px;
        pointer-events: none;
    }
    </style>';

    // 🎯 HTML Container
    echo '<div id="flash-container" class="flash-container flash-' . FlashConfig::$position . ' flash-theme-' . FlashConfig::$theme . '"></div>';

    // 🔐 Premium badge for trial users
    if (!FlashConfig::$isPremium && FlashConfig::$trialDaysLeft > 0) {
        echo '<div class="flash-premium-badge">Trial: ' . FlashConfig::$trialDaysLeft . ' days left</div>';
    } elseif (!FlashConfig::$isPremium) {
        echo '<div class="flash-premium-badge">Powered by Flash Messages</div>';
    }

    // 🚀 EMBEDDED JAVASCRIPT ENGINE
    echo '<script id="flash-messages-script">
    (function() {
        "use strict";

        // 🔧 Configuration
        const config = ' . $config . ';

        // 🎯 Global Variables
        let container = null;
        let activeMessages = [];
        let messageQueue = [];

        // 🎨 Icons
        const icons = {
            success: "✅",
            error: "❌",
            warning: "⚠️",
            info: "ℹ️",
            close: "✕"
        };

        // 🚀 Initialize System
        function init() {
            try {
                console.log("🚀 Initializing Flash Messages System...");

                container = document.getElementById("flash-container");
                if (!container) {
                    console.log("📦 Creating container...");
                    container = createContainer();
                }

                setupEventListeners();

                // Process existing messages
                const existingMessages = ' . json_encode($messages) . ';
                console.log("📨 Messages to process:", existingMessages);

                if (existingMessages && existingMessages.length > 0) {
                    console.log("✅ Processing " + existingMessages.length + " messages");
                    existingMessages.forEach((msg, index) => {
                        setTimeout(() => {
                            console.log("📤 Showing message:", msg);
                            showMessage(msg);
                        }, index * 150);
                    });
                } else {
                    console.log("ℹ️ No messages to display");
                }

                // Make functions globally available
                window.flashShow = showMessage;
                window.flashClear = clearAllMessages;
                window.flashTest = () => {
                    showMessage({
                        id: "test-" + Date.now(),
                        type: "success",
                        message: "Test message from JavaScript!",
                        title: "Test",
                        options: { duration: 5000, dismissible: true, showIcon: true }
                    });
                };

                console.log("✅ Flash Messages System initialized successfully!");

                // 🧪 Auto-test after 2 seconds if no messages were processed
                if (!existingMessages || existingMessages.length === 0) {
                    setTimeout(() => {
                        console.log("🧪 Running auto-test...");
                        showMessage({
                            id: "auto-test-" + Date.now(),
                            type: "info",
                            message: "Flash Messages system is working! This is an auto-test message.",
                            title: "System Test",
                            options: { duration: 5000, dismissible: true, showIcon: true }
                        });
                    }, 2000);
                }

            } catch (error) {
                console.error("❌ Flash Messages initialization failed:", error);
                // Emergency fallback
                window.flashShow = (msg) => {
                    const message = typeof msg === "string" ? msg : (msg.message || "Notification");
                    alert(message);
                };
                window.flashTest = () => alert("Flash Messages Test");
            }
        }

        // 🏗️ Create Container
        function createContainer() {
            const div = document.createElement("div");
            div.id = "flash-container";
            div.className = "flash-container flash-" + config.position + " flash-theme-" + config.theme;
            document.body.appendChild(div);
            return div;
        }

        // ⚙️ Setup Event Listeners
        function setupEventListeners() {
            // Click to dismiss
            container.addEventListener("click", (e) => {
                const message = e.target.closest(".flash-message");
                if (message) {
                    dismissMessage(message);
                }
            });

            // Keyboard shortcuts
            document.addEventListener("keydown", (e) => {
                if (e.key === "Escape") {
                    clearAllMessages();
                }
            });

            // Touch gestures for mobile
            if (config.swipeToClose) {
                setupTouchGestures();
            }
        }

        // 👆 Setup Touch Gestures
        function setupTouchGestures() {
            let startX = 0;

            container.addEventListener("touchstart", (e) => {
                startX = e.touches[0].clientX;
            });

            container.addEventListener("touchend", (e) => {
                const endX = e.changedTouches[0].clientX;
                const diffX = endX - startX;

                if (Math.abs(diffX) > 100) {
                    const message = e.target.closest(".flash-message");
                    if (message) {
                        dismissMessage(message);
                    }
                }
            });
        }

        // 🎯 Show Message
        function showMessage(messageData) {
            console.log("🎯 showMessage called with:", messageData);

            if (!container) {
                console.error("❌ Container not found!");
                return;
            }

            try {
                // Create message element
                console.log("🏗️ Creating message element...");
                const messageEl = createMessageElement(messageData);

                // Add to container
                console.log("📦 Adding to container...");
                container.appendChild(messageEl);
                activeMessages.push(messageEl);

                // Trigger show animation
                setTimeout(() => {
                    console.log("🎭 Triggering show animation...");
                    messageEl.classList.add("flash-show");
                }, 10);

                // Apply special effects
                console.log("🎊 Applying effects...");
                applyEffects(messageEl, messageData);

                // Auto-hide if duration is set
                const duration = messageData.options && messageData.options.duration ? messageData.options.duration : config.duration;
                if (duration > 0) {
                    console.log("⏰ Setting auto-hide timer for " + duration + "ms");
                    setTimeout(() => {
                        dismissMessage(messageEl);
                    }, duration);
                }

                // Limit max messages
                if (activeMessages.length > config.maxMessages) {
                    console.log("📊 Max messages reached, dismissing oldest");
                    dismissMessage(activeMessages[0]);
                }

                console.log("✅ Message displayed successfully!");

            } catch (error) {
                console.error("❌ Error showing message:", error);
                // Emergency fallback
                alert(messageData.message || "Notification");
            }
        }

        // 🏗️ Create Message Element
        function createMessageElement(messageData) {
            const div = document.createElement("div");
            div.className = "flash-message flash-" + messageData.type;
            div.setAttribute("data-id", messageData.id);

            const icon = messageData.options.showIcon !== false ?
                `<div class="flash-icon">${icons[messageData.type] || icons.info}</div>` : "";

            const title = messageData.title ?
                `<div class="flash-title">${messageData.title}</div>` : "";

            const closeBtn = messageData.options.dismissible !== false ?
                `<div class="flash-close" title="Close">${icons.close}</div>` : "";

            div.innerHTML = `
                ${icon}
                <div class="flash-content">
                    ${title}
                    <div class="flash-text">${messageData.message}</div>
                </div>
                ${closeBtn}
            `;

            return div;
        }

        // 🎊 Apply Special Effects
        function applyEffects(messageEl, messageData) {
            const options = messageData.options || {};

            // Shake effect for errors
            if (options.enableShake && messageData.type === "error") {
                messageEl.classList.add("flash-shake");
            }

            // Confetti effect for success (Premium)
            if (options.enableConfetti && messageData.type === "success" &&
                (config.isPremium || config.trialDaysLeft > 0)) {
                messageEl.classList.add("flash-confetti");
                createConfetti(messageEl);
            }

            // Glow effect (Premium)
            if (options.enableGlow && (config.isPremium || config.trialDaysLeft > 0)) {
                messageEl.classList.add("flash-glow");
            }

            // Play sound
            if (options.playSound && config.enableSounds) {
                playSound(messageData.type);
            }
        }

        // 🎊 Create Confetti Effect
        function createConfetti(messageEl) {
            if (!config.isPremium && config.trialDaysLeft <= 0) return;

            for (let i = 0; i < 15; i++) {
                const confetti = document.createElement("div");
                confetti.className = "flash-confetti-particle";
                confetti.style.cssText = `
                    background: hsl(${Math.random() * 360}, 70%, 60%);
                    top: 50%;
                    left: 50%;
                    --random: ${Math.random()};
                    animation-delay: ${Math.random() * 0.3}s;
                `;

                messageEl.appendChild(confetti);

                setTimeout(() => {
                    if (confetti.parentNode) {
                        confetti.parentNode.removeChild(confetti);
                    }
                }, 1300);
            }
        }

        // 🎵 Play Sound
        function playSound(type) {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                // Different frequencies for different types
                const frequencies = {
                    success: 800,
                    error: 300,
                    warning: 600,
                    info: 500
                };

                oscillator.frequency.setValueAtTime(frequencies[type] || 500, audioContext.currentTime);
                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.2);
            } catch (e) {
                // Fallback: no sound
            }
        }

        // 🗑️ Dismiss Message
        function dismissMessage(messageEl) {
            if (!messageEl || !messageEl.parentNode) return;

            messageEl.classList.remove("flash-show");

            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.parentNode.removeChild(messageEl);
                }

                const index = activeMessages.indexOf(messageEl);
                if (index > -1) {
                    activeMessages.splice(index, 1);
                }
            }, 300);
        }

        // 🗑️ Clear All Messages
        function clearAllMessages() {
            activeMessages.forEach(dismissMessage);
        }

        // 🚀 Initialize when DOM is ready
        if (document.readyState === "loading") {
            document.addEventListener("DOMContentLoaded", init);
        } else if (document.readyState === "interactive" || document.readyState === "complete") {
            // DOM is already ready
            setTimeout(init, 10);
        } else {
            // Fallback
            setTimeout(init, 100);
        }

        // 🔧 Additional fallback - force init after 1 second if not already done
        setTimeout(() => {
            if (!container) {
                console.warn("⚠️ Flash Messages not initialized, forcing init...");
                init();
            }
        }, 1000);

    })();
    </script>';

    // Add confetti animation CSS
    echo '<style>
    @keyframes confetti-fall {
        0% {
            transform: translate(-50%, -50%) rotate(0deg);
            opacity: 1;
        }
        100% {
            transform: translate(-50%, -50%) translateX(calc((var(--random, 0.5) - 0.5) * 200px)) translateY(100px) rotate(720deg);
            opacity: 0;
        }
    }

    .flash-confetti-particle {
        position: absolute;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        pointer-events: none;
        animation: confetti-fall 1s ease-out forwards;
    }
    </style>';

    echo "\n<!-- 🚀 Flash Messages System Loaded -->\n";

    // 🚨 Emergency fallback for immediate testing
    if (!empty($messages)) {
        echo '<script>
        // Emergency fallback - show alerts if main system fails
        setTimeout(function() {
            const container = document.getElementById("flash-container");
            if (!container || !window.flashShow) {
                console.warn("⚠️ Flash Messages system not working, using emergency fallback");
                const messages = ' . json_encode($messages, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP) . ';
                messages.forEach(function(msg, index) {
                    setTimeout(function() {
                        alert(msg.title + ": " + msg.message);
                    }, index * 500);
                });
            }
        }, 2000);
        </script>';
    }
}
