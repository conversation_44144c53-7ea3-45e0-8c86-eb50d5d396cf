<?php
/**
 * 🔍 DEBUG TEST - Flash Messages System
 * Debug page to identify and fix issues
 */

// Include the main flash messages system
require_once __DIR__ . '/flash-messages.php';

// Force add a test message
flash_success('Debug test message - if you see this, the system is working!', 'Debug Test');
flash_error('This is a test error message', 'Error Test');
flash_info('This is a test info message', 'Info Test');

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Debug Test - Flash Messages</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f0f0f0;
        }
        
        .debug-container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .debug-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .debug-button:hover {
            background: #005a87;
        }
        
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
        }
        
        .status-good { color: #28a745; }
        .status-bad { color: #dc3545; }
        .status-warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔍 Debug Test - Flash Messages System</h1>
        
        <div class="debug-info">
            <h3>📊 System Status Check</h3>
            <p><strong>PHP Session Status:</strong> 
                <span class="<?= session_status() === PHP_SESSION_ACTIVE ? 'status-good' : 'status-bad' ?>">
                    <?= session_status() === PHP_SESSION_ACTIVE ? 'Active ✅' : 'Inactive ❌' ?>
                </span>
            </p>
            
            <p><strong>Flash Messages in Session:</strong> 
                <span class="<?= !empty($_SESSION['flash_messages']) ? 'status-good' : 'status-warning' ?>">
                    <?= !empty($_SESSION['flash_messages']) ? count($_SESSION['flash_messages']) . ' messages ✅' : 'No messages ⚠️' ?>
                </span>
            </p>
            
            <p><strong>FlashConfig Class:</strong> 
                <span class="<?= class_exists('FlashConfig') ? 'status-good' : 'status-bad' ?>">
                    <?= class_exists('FlashConfig') ? 'Loaded ✅' : 'Not Found ❌' ?>
                </span>
            </p>
            
            <p><strong>FlashMessages Class:</strong> 
                <span class="<?= class_exists('FlashMessages') ? 'status-good' : 'status-bad' ?>">
                    <?= class_exists('FlashMessages') ? 'Loaded ✅' : 'Not Found ❌' ?>
                </span>
            </p>
            
            <p><strong>Current Theme:</strong> <?= FlashConfig::$theme ?></p>
            <p><strong>Current Position:</strong> <?= FlashConfig::$position ?></p>
            <p><strong>License Status:</strong> <?= FlashConfig::$isPremium ? 'Premium' : 'Free/Trial' ?></p>
        </div>
        
        <div class="debug-info">
            <h3>📨 Messages in Session</h3>
            <?php if (!empty($_SESSION['flash_messages'])): ?>
                <pre><?= htmlspecialchars(print_r($_SESSION['flash_messages'], true)) ?></pre>
            <?php else: ?>
                <p class="status-warning">No messages found in session</p>
            <?php endif; ?>
        </div>
        
        <h3>🧪 Manual Tests</h3>
        <p>Click these buttons to test the system manually:</p>
        
        <button class="debug-button" onclick="testJavaScript()">🔧 Test JavaScript</button>
        <button class="debug-button" onclick="testManualMessage()">📤 Test Manual Message</button>
        <button class="debug-button" onclick="testContainer()">📦 Test Container</button>
        <button class="debug-button" onclick="showConsole()">🖥️ Show Console</button>
        
        <div class="debug-info">
            <h3>💡 Troubleshooting Steps</h3>
            <ol>
                <li><strong>Check Browser Console:</strong> Press F12 and look for errors</li>
                <li><strong>Verify Session:</strong> Make sure PHP sessions are working</li>
                <li><strong>Check JavaScript:</strong> Ensure no JavaScript errors are blocking execution</li>
                <li><strong>Test Container:</strong> Verify the flash container is created</li>
                <li><strong>Manual Test:</strong> Try creating messages manually via JavaScript</li>
            </ol>
        </div>
        
        <div class="debug-info">
            <h3>🔧 Quick Fixes</h3>
            <ul>
                <li><strong>If no messages show:</strong> Check browser console for JavaScript errors</li>
                <li><strong>If container missing:</strong> JavaScript may not be running</li>
                <li><strong>If session issues:</strong> Ensure session_start() is called</li>
                <li><strong>If CSS issues:</strong> Check if styles are loading properly</li>
            </ul>
        </div>
        
        <h3>📋 Expected Behavior</h3>
        <p>You should see <strong>3 test messages</strong> appear on this page:</p>
        <ul>
            <li>✅ Green success message: "Debug test message"</li>
            <li>❌ Red error message: "This is a test error message"</li>
            <li>ℹ️ Blue info message: "This is a test info message"</li>
        </ul>
        
        <p><strong>If you don't see these messages, check the browser console (F12) for errors.</strong></p>
        
        <div id="debug-output" class="debug-info" style="display: none;">
            <h3>🖥️ Console Output</h3>
            <div id="console-content"></div>
        </div>
    </div>

    <!-- Flash messages will be rendered here automatically -->

    <script>
        // Debug JavaScript functions
        function testJavaScript() {
            console.log('🧪 Testing JavaScript...');
            
            if (typeof window.flashTest === 'function') {
                console.log('✅ flashTest function found');
                window.flashTest();
            } else {
                console.error('❌ flashTest function not found');
                alert('❌ Flash Messages JavaScript not loaded properly');
            }
        }
        
        function testManualMessage() {
            console.log('📤 Testing manual message...');
            
            if (typeof window.flashShow === 'function') {
                window.flashShow({
                    id: 'manual-test-' + Date.now(),
                    type: 'success',
                    message: 'Manual test message created via JavaScript!',
                    title: 'Manual Test',
                    options: {
                        duration: 5000,
                        dismissible: true,
                        showIcon: true
                    }
                });
                console.log('✅ Manual message sent');
            } else {
                console.error('❌ flashShow function not found');
                alert('❌ Flash Messages not initialized');
            }
        }
        
        function testContainer() {
            const container = document.getElementById('flash-container');
            if (container) {
                console.log('✅ Flash container found:', container);
                alert('✅ Flash container exists and is ready');
            } else {
                console.error('❌ Flash container not found');
                alert('❌ Flash container missing - JavaScript may not be running');
            }
        }
        
        function showConsole() {
            const output = document.getElementById('debug-output');
            const content = document.getElementById('console-content');
            
            // Capture console logs
            const logs = [];
            const originalLog = console.log;
            const originalError = console.error;
            
            console.log = function(...args) {
                logs.push('LOG: ' + args.join(' '));
                originalLog.apply(console, args);
                updateConsoleDisplay();
            };
            
            console.error = function(...args) {
                logs.push('ERROR: ' + args.join(' '));
                originalError.apply(console, args);
                updateConsoleDisplay();
            };
            
            function updateConsoleDisplay() {
                content.innerHTML = logs.map(log => '<div>' + log + '</div>').join('');
            }
            
            output.style.display = 'block';
            updateConsoleDisplay();
            
            console.log('🖥️ Console capture started');
        }
        
        // Auto-run diagnostics
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔍 Debug page loaded');
            console.log('📊 Running automatic diagnostics...');
            
            // Check if flash system is loaded
            setTimeout(() => {
                console.log('⏰ Checking system after 1 second...');
                
                const container = document.getElementById('flash-container');
                const hasFlashShow = typeof window.flashShow === 'function';
                const hasFlashTest = typeof window.flashTest === 'function';
                
                console.log('📦 Container exists:', !!container);
                console.log('🔧 flashShow function:', hasFlashShow);
                console.log('🧪 flashTest function:', hasFlashTest);
                
                if (!container || !hasFlashShow) {
                    console.error('❌ Flash Messages system not properly initialized');
                } else {
                    console.log('✅ Flash Messages system appears to be working');
                }
            }, 1000);
        });
    </script>

</body>
</html>
