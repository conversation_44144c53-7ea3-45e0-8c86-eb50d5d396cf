<?php
/**
 * 🚀 SUPER SIMPLE FLASH MESSAGES
 * 100% Guaranteed to work - Uses alerts as fallback
 */

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Initialize messages array
if (!isset($_SESSION['super_flash'])) {
    $_SESSION['super_flash'] = [];
}

// Add message function
function super_flash($type, $message, $title = '') {
    $_SESSION['super_flash'][] = [
        'type' => $type,
        'message' => $message,
        'title' => $title,
        'time' => time()
    ];
}

// Convenience functions
function super_success($message, $title = 'Success!') {
    super_flash('success', $message, $title);
}

function super_error($message, $title = 'Error!') {
    super_flash('error', $message, $title);
}

function super_warning($message, $title = 'Warning!') {
    super_flash('warning', $message, $title);
}

function super_info($message, $title = 'Info') {
    super_flash('info', $message, $title);
}

// Display messages function
function super_flash_show() {
    if (empty($_SESSION['super_flash'])) {
        return;
    }
    
    $messages = $_SESSION['super_flash'];
    $_SESSION['super_flash'] = []; // Clear messages
    
    // Method 1: Try modern notifications
    echo '<div id="super-flash-modern" style="position:fixed;top:20px;right:20px;z-index:999999;"></div>';
    
    // Method 2: Fallback to alerts
    echo '<script>';
    echo 'var superMessages = ' . json_encode($messages) . ';';
    echo '
    // Try modern notifications first
    function showModernNotification(msg) {
        try {
            var container = document.getElementById("super-flash-modern");
            if (!container) return false;
            
            var div = document.createElement("div");
            div.style.cssText = `
                background: white;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 10px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
                border-left: 4px solid ` + getColor(msg.type) + `;
                max-width: 350px;
                font-family: Arial, sans-serif;
                animation: slideIn 0.3s ease;
                cursor: pointer;
            `;
            
            var icon = getIcon(msg.type);
            div.innerHTML = `
                <div style="display:flex;align-items:flex-start;">
                    <div style="font-size:18px;margin-right:10px;">${icon}</div>
                    <div style="flex:1;">
                        <div style="font-weight:bold;margin-bottom:5px;">${msg.title}</div>
                        <div style="color:#666;">${msg.message}</div>
                    </div>
                    <div style="margin-left:10px;cursor:pointer;font-size:16px;color:#999;" onclick="this.parentElement.parentElement.remove()">✕</div>
                </div>
            `;
            
            container.appendChild(div);
            
            // Auto-remove after 5 seconds
            setTimeout(function() {
                if (div.parentElement) {
                    div.remove();
                }
            }, 5000);
            
            return true;
        } catch (e) {
            console.error("Modern notification failed:", e);
            return false;
        }
    }
    
    function getColor(type) {
        switch(type) {
            case "success": return "#28a745";
            case "error": return "#dc3545";
            case "warning": return "#ffc107";
            case "info": return "#17a2b8";
            default: return "#6c757d";
        }
    }
    
    function getIcon(type) {
        switch(type) {
            case "success": return "✅";
            case "error": return "❌";
            case "warning": return "⚠️";
            case "info": return "ℹ️";
            default: return "📢";
        }
    }
    
    function showAlertFallback(msg) {
        var text = msg.title + ": " + msg.message;
        alert(text);
    }
    
    // Add CSS for animations
    var style = document.createElement("style");
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    `;
    document.head.appendChild(style);
    
    // Show messages
    function displayMessages() {
        if (superMessages.length === 0) return;
        
        console.log("Displaying", superMessages.length, "messages");
        
        superMessages.forEach(function(msg, index) {
            setTimeout(function() {
                var success = showModernNotification(msg);
                if (!success) {
                    console.log("Modern notification failed, using alert");
                    showAlertFallback(msg);
                }
            }, index * 300);
        });
    }
    
    // Try to display immediately
    if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", displayMessages);
    } else {
        displayMessages();
    }
    
    // Fallback - if nothing shows in 2 seconds, use alerts
    setTimeout(function() {
        var container = document.getElementById("super-flash-modern");
        if (container && container.children.length === 0 && superMessages.length > 0) {
            console.log("No modern notifications shown, using alert fallback");
            superMessages.forEach(function(msg) {
                showAlertFallback(msg);
            });
        }
    }, 2000);
    ';
    echo '</script>';
}

// Auto-display messages
register_shutdown_function('super_flash_show');

// Test if accessed directly
if (basename($_SERVER['PHP_SELF']) === 'super-simple.php') {
    super_success('Super Simple system is working!', 'Success Test');
    super_error('This is an error test', 'Error Test');
    super_warning('This is a warning test', 'Warning Test');
    super_info('This is an info test', 'Info Test');
}
?>

<?php if (basename($_SERVER['PHP_SELF']) === 'super-simple.php'): ?>
<!DOCTYPE html>
<html>
<head>
    <title>🚀 Super Simple Flash Messages</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f0f0f0;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .btn {
            background: #007cba;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 8px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            min-width: 150px;
        }
        
        .btn:hover { background: #005a87; }
        .btn-success { background: #28a745; }
        .btn-error { background: #dc3545; }
        .btn-warning { background: #ffc107; color: #000; }
        .btn-info { background: #17a2b8; }
        
        .alert {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Super Simple Flash Messages</h1>
        
        <div class="alert alert-success">
            <strong>✅ System Status: ACTIVE</strong><br>
            You should see 4 test messages appear (or alerts if modern notifications fail).
        </div>
        
        <h3>🧪 Manual Tests</h3>
        <p>Click these buttons to test manually:</p>
        
        <button class="btn btn-success" onclick="testSuccess()">✅ Test Success</button>
        <button class="btn btn-error" onclick="testError()">❌ Test Error</button>
        <button class="btn btn-warning" onclick="testWarning()">⚠️ Test Warning</button>
        <button class="btn btn-info" onclick="testInfo()">ℹ️ Test Info</button>
        
        <br><br>
        
        <button class="btn" onclick="testAlert()">🚨 Test Alert</button>
        <button class="btn" onclick="testConfirm()">❓ Test Confirm</button>
        <button class="btn" onclick="testPrompt()">📝 Test Prompt</button>
        
        <h3>📋 Features</h3>
        <ul>
            <li>✅ <strong>Dual System:</strong> Modern notifications + Alert fallback</li>
            <li>✅ <strong>Always Works:</strong> If modern fails, alerts will show</li>
            <li>✅ <strong>Auto-Hide:</strong> Modern notifications disappear after 5 seconds</li>
            <li>✅ <strong>Click to Close:</strong> Click ✕ to dismiss</li>
            <li>✅ <strong>Zero Dependencies:</strong> Pure PHP + JavaScript</li>
        </ul>
        
        <div class="alert alert-info">
            <strong>💡 How it works:</strong><br>
            1. Tries to show modern notifications<br>
            2. If that fails, shows browser alerts<br>
            3. Guaranteed to show something!
        </div>
        
        <h3>📝 Usage</h3>
        <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px;"><code>&lt;?php
require_once 'super-simple.php';

super_success('Success message!', 'Success!');
super_error('Error message!', 'Error!');
super_warning('Warning message!', 'Warning!');
super_info('Info message!', 'Info');
?&gt;</code></pre>
    </div>

    <script>
        function testSuccess() {
            showTestMessage('success', 'Manual success test!', 'Success Test');
        }
        
        function testError() {
            showTestMessage('error', 'Manual error test!', 'Error Test');
        }
        
        function testWarning() {
            showTestMessage('warning', 'Manual warning test!', 'Warning Test');
        }
        
        function testInfo() {
            showTestMessage('info', 'Manual info test!', 'Info Test');
        }
        
        function showTestMessage(type, message, title) {
            var msg = {type: type, message: message, title: title};
            
            // Try modern notification
            var container = document.getElementById('super-flash-modern');
            if (container) {
                var success = showModernNotification(msg);
                if (!success) {
                    alert(title + ': ' + message);
                }
            } else {
                alert(title + ': ' + message);
            }
        }
        
        function testAlert() {
            alert('This is a test alert message!');
        }
        
        function testConfirm() {
            var result = confirm('Do you want to continue?');
            alert('You clicked: ' + (result ? 'OK' : 'Cancel'));
        }
        
        function testPrompt() {
            var result = prompt('Enter your name:');
            if (result !== null) {
                alert('Hello, ' + result + '!');
            }
        }
        
        // Debug info
        console.log('🚀 Super Simple Flash Messages loaded');
        console.log('📊 Test messages should appear now');
    </script>
</body>
</html>
<?php endif; ?>
