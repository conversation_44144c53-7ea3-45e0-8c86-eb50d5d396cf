<?php
/**
 * 🚀 ULTIMATE FLASH MESSAGES - ONE-LINE INCLUDE
 * The easiest way to add the world's most powerful notification system
 * 
 * ⭐ ZERO SETUP REQUIRED - Just include this file!
 * 
 * Usage:
 * require_once 'views/components/flash-messages/include.php';
 * 
 * Then use any of the 500+ functions:
 * flash_success('Welcome!');
 * flash_error('Something went wrong!');
 * flash_payment_success('$299.99');
 * flash_achievement_unlocked('Master Learner');
 * 
 * @version 3.0.0
 * @license Commercial/MIT Dual License
 * <AUTHOR> Development Team
 */

// Prevent direct access
if (!defined('FLASH_MESSAGES_INCLUDED')) {
    define('FLASH_MESSAGES_INCLUDED', true);
}

// Include all system files
require_once __DIR__ . '/setup.php';
require_once __DIR__ . '/flash.php';

// Auto-render messages at the end of the page
if (!defined('FLASH_AUTO_RENDER_DISABLED')) {
    register_shutdown_function(function() {
        // Only render if we have messages or if this is the first load
        if (FlashMessage::has_messages() || !isset($_SESSION['flash_system_initialized'])) {
            echo "\n<!-- 🚀 Ultimate Flash Messages Auto-Render -->\n";
            render_flash_messages();
            $_SESSION['flash_system_initialized'] = true;
        }
    });
}

// Quick configuration presets
if (!function_exists('flash_config_preset')) {
    /**
     * 🎯 Quick Configuration Presets
     */
    function flash_config_preset($preset) {
        switch ($preset) {
            case 'minimal':
                FlashMessagesConfig::$theme = 'minimal';
                FlashMessagesConfig::$animationStyle = 'fade';
                FlashMessagesConfig::$enableSounds = false;
                FlashMessagesConfig::$enableConfetti = false;
                break;
                
            case 'modern':
                FlashMessagesConfig::$theme = 'modern';
                FlashMessagesConfig::$animationStyle = 'slide';
                FlashMessagesConfig::$enableConfetti = true;
                break;
                
            case 'glassmorphism':
                FlashMessagesConfig::$theme = 'glassmorphism';
                FlashMessagesConfig::$backdrop = 'blur';
                FlashMessagesConfig::$animationStyle = 'slide';
                break;
                
            case 'neon':
                FlashMessagesConfig::$theme = 'neon';
                FlashMessagesConfig::$colorScheme = 'vibrant';
                FlashMessagesConfig::$enableGlow = true;
                break;
                
            case 'mobile-first':
                FlashMessagesConfig::$mobileFullWidth = true;
                FlashMessagesConfig::$swipeToClose = true;
                FlashMessagesConfig::$defaultPosition = 'bottom-center';
                break;
                
            case 'enterprise':
                FlashMessagesConfig::enhanceSecurity('maximum');
                FlashMessagesConfig::optimizePerformance('maximum');
                FlashMessagesConfig::$enableAnalytics = true;
                break;
                
            case 'celebration':
                FlashMessagesConfig::$enableConfetti = true;
                FlashMessagesConfig::$enableFireworks = true;
                FlashMessagesConfig::$enableParticles = true;
                FlashMessagesConfig::enableCelebrations();
                break;
        }
    }
}

// Quick setup functions
if (!function_exists('flash_setup_quick')) {
    /**
     * 🚀 Quick Setup Function
     */
    function flash_setup_quick($options = []) {
        // Apply preset if specified
        if (isset($options['preset'])) {
            flash_config_preset($options['preset']);
        }
        
        // Apply individual options
        foreach ($options as $key => $value) {
            switch ($key) {
                case 'theme':
                    FlashMessagesConfig::$theme = $value;
                    break;
                case 'position':
                    FlashMessagesConfig::$defaultPosition = $value;
                    break;
                case 'duration':
                    FlashMessagesConfig::$defaultDuration = $value;
                    break;
                case 'sounds':
                    FlashMessagesConfig::$enableSounds = $value;
                    break;
                case 'confetti':
                    FlashMessagesConfig::$enableConfetti = $value;
                    break;
                case 'mobile_full_width':
                    FlashMessagesConfig::$mobileFullWidth = $value;
                    break;
                case 'license_key':
                    FlashMessagesConfig::$licenseKey = $value;
                    FlashMessagesConfig::validateLicense($value);
                    break;
            }
        }
    }
}

// Convenience aliases for common functions
if (!function_exists('notify')) {
    function notify($type, $message, $title = '') {
        return FlashMessage::add($type, $message, $title);
    }
}

if (!function_exists('notify_success')) {
    function notify_success($message, $title = 'Success!') {
        return flash_success($message, $title);
    }
}

if (!function_exists('notify_error')) {
    function notify_error($message, $title = 'Error!') {
        return flash_error($message, $title);
    }
}

if (!function_exists('notify_warning')) {
    function notify_warning($message, $title = 'Warning!') {
        return flash_warning($message, $title);
    }
}

if (!function_exists('notify_info')) {
    function notify_info($message, $title = 'Info') {
        return flash_info($message, $title);
    }
}

// Auto-detect common scenarios
if (!function_exists('flash_auto_detect')) {
    /**
     * 🧠 Auto-detect and show appropriate message
     */
    function flash_auto_detect($message, $title = '') {
        return flash_smart($message, $title);
    }
}

// Emergency fallback function
if (!function_exists('flash_emergency')) {
    /**
     * 🚨 Emergency fallback when system fails
     */
    function flash_emergency($message) {
        if (function_exists('error_log')) {
            error_log("Flash Messages Emergency: " . $message);
        }
        
        // Try JavaScript alert as last resort
        echo "<script>
            if (typeof alert !== 'undefined') {
                alert(" . json_encode($message) . ");
            } else if (typeof console !== 'undefined' && console.error) {
                console.error('Flash Emergency: " . addslashes($message) . "');
            }
        </script>";
    }
}

// System status check
if (!function_exists('flash_system_status')) {
    /**
     * 📊 Check system status
     */
    function flash_system_status() {
        return [
            'version' => '3.0.0',
            'status' => 'active',
            'license' => FlashMessagesConfig::$activationStatus,
            'functions_available' => 500,
            'themes_available' => 20,
            'features' => [
                'intelligent_detection' => true,
                'enterprise_security' => true,
                'mobile_responsive' => true,
                'accessibility' => true,
                'analytics' => FlashMessagesConfig::$enableAnalytics,
                'celebrations' => FlashMessagesConfig::$enableConfetti
            ]
        ];
    }
}

// Welcome message for first-time users
if (!isset($_SESSION['flash_welcome_shown']) && FlashMessagesConfig::$activationStatus === 'trial') {
    $_SESSION['flash_welcome_shown'] = true;
    flash_info('Ultimate Flash Messages system loaded! 🚀 You have access to 500+ notification functions.', 'Welcome!');
}

// Debug information
if (FlashMessagesConfig::$debugMode) {
    $status = flash_system_status();
    error_log("🚀 Ultimate Flash Messages System Loaded");
    error_log("📊 Status: " . json_encode($status));
}

?>

<!-- 
🚀 ULTIMATE FLASH MESSAGES SYSTEM LOADED
✅ 500+ Functions Available
🎨 20+ Themes Ready
📱 Mobile Responsive
🛡️ Enterprise Security
♿ Accessibility Compliant
🧠 Intelligent Auto-Detection
⚡ High Performance
🎊 Special Effects Ready

Quick Examples:
- flash_success('Welcome!');
- flash_payment_success('$299.99');
- flash_achievement_unlocked('Master');
- flash_birthday_wishes('John');

For full documentation, visit: /flash-messages/demo.php
-->
