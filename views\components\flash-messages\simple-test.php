<?php
/**
 * 🧪 SIMPLE TEST - Flash Messages
 * Basic test to verify the system works
 */

// Start session
session_start();

// Include the main flash messages system
require_once __DIR__ . '/flash-messages.php';

// Add test messages
flash_success('✅ Success message test!', 'Success Test');
flash_error('❌ Error message test!', 'Error Test');
flash_warning('⚠️ Warning message test!', 'Warning Test');
flash_info('ℹ️ Info message test!', 'Info Test');

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Test - Flash Messages</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-box {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="test-box">
        <h1>🧪 Simple Flash Messages Test</h1>
        
        <div class="info status">
            <strong>Expected Result:</strong><br>
            You should see 4 notification messages appear on this page:<br>
            ✅ Success, ❌ Error, ⚠️ Warning, ℹ️ Info
        </div>
        
        <div class="success status">
            <strong>System Status:</strong><br>
            <?php if (class_exists('FlashMessages')): ?>
                ✅ FlashMessages class loaded<br>
            <?php else: ?>
                ❌ FlashMessages class not found<br>
            <?php endif; ?>
            
            <?php if (!empty($_SESSION['flash_messages'])): ?>
                ✅ <?= count($_SESSION['flash_messages']) ?> messages in session<br>
            <?php else: ?>
                ❌ No messages in session<br>
            <?php endif; ?>
            
            ✅ Theme: <?= FlashConfig::$theme ?><br>
            ✅ Position: <?= FlashConfig::$position ?>
        </div>
        
        <div class="info status">
            <strong>Troubleshooting:</strong><br>
            If you don't see messages, press <strong>F12</strong> to open browser console and check for errors.
        </div>
        
        <p>
            <a href="debug-test.php" style="color: #007cba; text-decoration: none; font-weight: bold;">🔍 Advanced Debug</a> |
            <a href="quick-test.php" style="color: #007cba; text-decoration: none; font-weight: bold;">🧪 Interactive Test</a>
        </p>
    </div>

    <!-- Flash messages will be rendered here -->

    <script>
        // Simple test script
        console.log('🧪 Simple test page loaded');
        
        // Check if flash system loaded after 1 second
        setTimeout(function() {
            const container = document.getElementById('flash-container');
            const hasFlashShow = typeof window.flashShow === 'function';
            
            console.log('📦 Container exists:', !!container);
            console.log('🔧 flashShow function exists:', hasFlashShow);
            
            if (container && hasFlashShow) {
                console.log('✅ Flash Messages system is working!');
                
                // Test manual message
                window.flashShow({
                    id: 'manual-test',
                    type: 'info',
                    message: 'Manual JavaScript test message!',
                    title: 'JS Test',
                    options: { duration: 5000, dismissible: true, showIcon: true }
                });
            } else {
                console.error('❌ Flash Messages system not working');
            }
        }, 1000);
    </script>

</body>
</html>
