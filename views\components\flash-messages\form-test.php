<?php
/**
 * 🧪 Form Test - PHP + JavaScript Integration
 * Tests form submission with JavaScript alerts
 */

$message = '';
$alertScript = '';

// Handle form submissions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'test_success':
            $message = 'Success form submitted successfully!';
            $alertScript = "alert('✅ SUCCESS: Success form submitted successfully!');";
            break;
            
        case 'test_error':
            $message = 'Error form submitted successfully!';
            $alertScript = "alert('❌ ERROR: Error form submitted successfully!');";
            break;
            
        case 'test_warning':
            $message = 'Warning form submitted successfully!';
            $alertScript = "alert('⚠️ WARNING: Warning form submitted successfully!');";
            break;
            
        case 'test_info':
            $message = 'Info form submitted successfully!';
            $alertScript = "alert('ℹ️ INFO: Info form submitted successfully!');";
            break;
            
        case 'test_confirm':
            $message = 'Confirm test triggered!';
            $alertScript = "
                var result = confirm('❓ Do you want to proceed with this action?');
                if (result) {
                    alert('✅ You clicked OK - Action confirmed!');
                } else {
                    alert('❌ You clicked Cancel - Action cancelled!');
                }
            ";
            break;
            
        case 'test_prompt':
            $message = 'Prompt test triggered!';
            $alertScript = "
                var name = prompt('📝 Please enter your name:');
                if (name !== null && name !== '') {
                    alert('👋 Hello, ' + name + '! Form submission successful.');
                } else {
                    alert('😐 No name entered, but form still works!');
                }
            ";
            break;
            
        case 'test_multiple':
            $message = 'Multiple alerts test triggered!';
            $alertScript = "
                alert('📦 Alert 1: Form submitted successfully!');
                setTimeout(function() { alert('📦 Alert 2: Processing data...'); }, 500);
                setTimeout(function() { alert('📦 Alert 3: All done!'); }, 1000);
            ";
            break;
    }
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>🧪 Form Test - PHP + JavaScript</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .form-group {
            margin: 20px 0;
        }
        
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 8px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            min-width: 200px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
        }
        
        .btn:active {
            transform: scale(0.95);
        }
        
        .btn-success { background: linear-gradient(45deg, #28a745, #20c997); }
        .btn-error { background: linear-gradient(45deg, #dc3545, #e74c3c); }
        .btn-warning { background: linear-gradient(45deg, #ffc107, #f39c12); }
        .btn-info { background: linear-gradient(45deg, #17a2b8, #3498db); }
        .btn-special { background: linear-gradient(45deg, #6f42c1, #e83e8c); }
        
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        
        .status-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .status-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .result-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Form Test - PHP + JavaScript Integration</h1>
        
        <div class="status status-info">
            <strong>📋 Test Purpose:</strong><br>
            This page tests PHP form submission combined with JavaScript alerts/confirmations.
        </div>
        
        <?php if ($message): ?>
        <div class="status status-success">
            <strong>✅ PHP Response:</strong><br>
            <?= htmlspecialchars($message) ?>
        </div>
        <?php endif; ?>
        
        <h3>🚨 Alert Tests (Form Submission)</h3>
        <div class="grid">
            <form method="POST">
                <input type="hidden" name="action" value="test_success">
                <button type="submit" class="btn btn-success">✅ Success Alert</button>
            </form>
            
            <form method="POST">
                <input type="hidden" name="action" value="test_error">
                <button type="submit" class="btn btn-error">❌ Error Alert</button>
            </form>
            
            <form method="POST">
                <input type="hidden" name="action" value="test_warning">
                <button type="submit" class="btn btn-warning">⚠️ Warning Alert</button>
            </form>
            
            <form method="POST">
                <input type="hidden" name="action" value="test_info">
                <button type="submit" class="btn btn-info">ℹ️ Info Alert</button>
            </form>
        </div>
        
        <h3>🎯 Advanced Tests</h3>
        <div class="grid">
            <form method="POST">
                <input type="hidden" name="action" value="test_confirm">
                <button type="submit" class="btn btn-special">❓ Confirm Dialog</button>
            </form>
            
            <form method="POST">
                <input type="hidden" name="action" value="test_prompt">
                <button type="submit" class="btn btn-special">📝 Prompt Dialog</button>
            </form>
            
            <form method="POST">
                <input type="hidden" name="action" value="test_multiple">
                <button type="submit" class="btn btn-special">📦 Multiple Alerts</button>
            </form>
        </div>
        
        <h3>🔧 Direct JavaScript Tests</h3>
        <div class="grid">
            <button class="btn" onclick="directAlert()">🚨 Direct Alert</button>
            <button class="btn" onclick="directConfirm()">❓ Direct Confirm</button>
            <button class="btn" onclick="directPrompt()">📝 Direct Prompt</button>
        </div>
        
        <div class="result-box">
            <strong>📊 Test Results:</strong><br>
            <div id="test-results">
                Ready to test... Click any button above.
            </div>
        </div>
        
        <div class="status status-warning">
            <strong>⚠️ Troubleshooting:</strong><br>
            • If form buttons don't show alerts: Check browser console (F12)<br>
            • If direct buttons don't work: JavaScript might be disabled<br>
            • If page doesn't submit: Check PHP errors<br>
            • All tests should work in modern browsers
        </div>
        
        <div class="status status-success">
            <strong>✅ Expected Results:</strong><br>
            • Form buttons: Submit form → Show alert<br>
            • Direct buttons: Immediate JavaScript execution<br>
            • All buttons should be clickable and responsive
        </div>
    </div>

    <?php if ($alertScript): ?>
    <script>
        // Execute the alert script from PHP
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                <?= $alertScript ?>
                updateResults('Form submission alert executed');
            }, 100);
        });
    </script>
    <?php endif; ?>

    <script>
        // Direct JavaScript test functions
        function directAlert() {
            alert('🚨 Direct JavaScript alert working!');
            updateResults('Direct alert executed');
        }
        
        function directConfirm() {
            var result = confirm('❓ Direct confirm test - Continue?');
            alert('You clicked: ' + (result ? 'OK' : 'Cancel'));
            updateResults('Direct confirm executed: ' + (result ? 'OK' : 'Cancel'));
        }
        
        function directPrompt() {
            var input = prompt('📝 Direct prompt test - Enter something:');
            if (input !== null) {
                alert('You entered: ' + input);
                updateResults('Direct prompt executed: ' + input);
            } else {
                alert('Prompt cancelled');
                updateResults('Direct prompt cancelled');
            }
        }
        
        function updateResults(message) {
            var resultsDiv = document.getElementById('test-results');
            var timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += '<br>[' + timestamp + '] ' + message;
        }
        
        // Page load test
        document.addEventListener('DOMContentLoaded', function() {
            updateResults('Page loaded successfully - JavaScript is working');
            console.log('🧪 Form test page loaded');
            
            // Test form submission detection
            document.addEventListener('submit', function(e) {
                console.log('📝 Form submitted:', e.target);
                updateResults('Form submission detected');
            });
            
            // Test button clicks
            document.addEventListener('click', function(e) {
                if (e.target.tagName === 'BUTTON') {
                    console.log('🖱️ Button clicked:', e.target.textContent);
                }
            });
        });
    </script>
</body>
</html>
