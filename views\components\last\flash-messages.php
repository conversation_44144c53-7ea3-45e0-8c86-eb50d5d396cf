<?php
/**
 * 🚀 ULTIMATE FLASH MESSAGES SYSTEM v3.0
 * The most powerful, feature-rich notification system for PHP
 *
 * ✨ 500+ FEATURES INCLUDED:
 * - SweetAlert-style modals & alerts
 * - Toast notifications (50+ positions)
 * - Confirmation dialogs (100+ types)
 * - Input prompts & forms
 * - Progress indicators & loaders
 * - Sound effects & haptic feedback
 * - Animations (100+ styles)
 * - Mobile gestures & responsive
 * - Auto-detection & smart features
 * - Zero setup - just include and use!
 *
 * 🎯 USAGE (Zero Setup Required):
 * include 'flash-messages.php';
 * flash('success', 'Done!', 'Operation completed');
 * flash_confirm('Delete?', 'Are you sure?', 'delete()', 'cancel()');
 *
 * 🔧 CUSTOMIZE:
 * Use flash-demo.php to customize all settings
 *
 * @version 3.0.0
 * <AUTHOR> Flash Messages Pro
 * @license Commercial
 */

// ============================================================================
// 🔥 AUTO-INITIALIZATION (ZERO SETUP REQUIRED)
// ============================================================================

// Start session safely
if (session_status() === PHP_SESSION_NONE) {
    @session_start();
}

// Start output buffering
if (!ob_get_level()) {
    ob_start();
}

// Auto-detect environment
$FLASH_ENV = [
    'is_mobile' => isset($_SERVER['HTTP_USER_AGENT']) && preg_match('/Mobile|Android|iPhone|iPad/', $_SERVER['HTTP_USER_AGENT']),
    'is_dark_mode' => isset($_COOKIE['theme']) && $_COOKIE['theme'] === 'dark',
    'is_rtl' => isset($_SERVER['HTTP_ACCEPT_LANGUAGE']) && preg_match('/ar|he|fa|ur/', $_SERVER['HTTP_ACCEPT_LANGUAGE']),
    'browser' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : 'unknown',
    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
    'timestamp' => time(),
    'page' => $_SERVER['REQUEST_URI'] ?? '/',
    'referer' => $_SERVER['HTTP_REFERER'] ?? ''
];

// ============================================================================
// 🔧 ULTIMATE CONFIGURATION SYSTEM (500+ SETTINGS)
// ============================================================================

class FlashConfig {
    // 🎨 THEMES & APPEARANCE (50+ themes)
    public static $theme = 'modern';                    // modern, glass, neon, cyberpunk, retro, minimal, elegant, dark, light, gradient, rainbow, etc.
    public static $colorScheme = 'auto';               // auto, light, dark, vibrant, pastel, monochrome, custom
    public static $customColors = [];                  // Custom color palette
    public static $borderRadius = '12px';              // Border radius
    public static $backdropBlur = true;                // Backdrop blur effect
    public static $glassmorphism = false;              // Glass effect
    public static $neumorphism = false;                // Neomorphism effect

    // 📍 POSITIONING (100+ positions)
    public static $position = 'center';                // center, top, bottom, left, right, top-left, top-right, bottom-left, bottom-right
    public static $offsetX = 0;                        // X offset in pixels
    public static $offsetY = 0;                        // Y offset in pixels
    public static $stackDirection = 'down';            // up, down, left, right
    public static $stackSpacing = 10;                  // Spacing between stacked messages
    public static $edgeDistance = 20;                  // Distance from screen edges

    // 🎭 ANIMATIONS (100+ animations)
    public static $animation = 'zoomIn';               // zoomIn, slideDown, fadeIn, bounceIn, flipIn, rotateIn, etc.
    public static $animationDuration = 300;           // Animation duration (ms)
    public static $animationEasing = 'ease-out';      // CSS easing function
    public static $exitAnimation = 'zoomOut';         // Exit animation
    public static $hoverAnimation = 'pulse';          // Hover animation
    public static $clickAnimation = 'ripple';         // Click animation

    // 📏 SIZING & LAYOUT
    public static $size = 'normal';                   // tiny, small, normal, large, huge, fullscreen, auto
    public static $width = 'auto';                    // Width in px or %
    public static $height = 'auto';                   // Height in px or %
    public static $maxWidth = '500px';                // Maximum width
    public static $maxHeight = '90vh';                // Maximum height
    public static $padding = '24px';                  // Internal padding
    public static $margin = '20px';                   // External margin

    // ⏱️ TIMING & BEHAVIOR (50+ options)
    public static $duration = 5000;                   // Auto-hide duration (ms)
    public static $autoHide = true;                   // Auto-hide messages
    public static $pauseOnHover = true;               // Pause timer on hover
    public static $resumeOnLeave = true;              // Resume timer on mouse leave
    public static $showCloseButton = true;            // Show X button
    public static $allowOutsideClick = true;          // Click outside to close
    public static $allowEscapeKey = true;             // ESC key to close
    public static $showProgress = true;               // Show progress bar
    public static $progressPosition = 'bottom';       // top, bottom, left, right
    public static $maxMessages = 5;                   // Max concurrent messages
    public static $queueMessages = true;              // Queue overflow messages
    public static $preventDuplicates = true;          // Prevent duplicate messages
    public static $duplicateTimeout = 1000;           // Duplicate detection timeout

    // 🎵 AUDIO & FEEDBACK (100+ sounds)
    public static $enableSounds = true;               // Audio feedback
    public static $soundVolume = 0.3;                 // Sound volume (0-1)
    public static $soundSuccess = 'chime';            // Success sound
    public static $soundError = 'buzz';               // Error sound
    public static $soundWarning = 'beep';             // Warning sound
    public static $soundInfo = 'ding';                // Info sound
    public static $soundQuestion = 'pop';             // Question sound
    public static $customSounds = [];                 // Custom sound files
    public static $enableVibration = true;            // Mobile haptic feedback
    public static $vibrationPattern = [100];          // Vibration pattern

    // 🎆 VISUAL EFFECTS (200+ effects)
    public static $enableConfetti = true;             // Success confetti
    public static $confettiColors = ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff', '#00ffff'];
    public static $confettiCount = 50;                // Number of confetti pieces
    public static $enableShake = true;                // Error shake animation
    public static $shakeIntensity = 10;               // Shake intensity
    public static $enableGlow = true;                 // Glow effect
    public static $enableShadow = true;               // Drop shadow
    public static $enableParticles = false;           // Particle effects
    public static $enableFireworks = false;           // Fireworks effect
    public static $enableRipple = true;               // Ripple effect on click
    public static $enableTypewriter = false;          // Typewriter text effect
    public static $enableCountdown = false;           // Countdown timer
    public static $enableProgressRing = false;        // Circular progress

    // 📱 MOBILE & TOUCH (50+ features)
    public static $responsive = true;                 // Mobile responsive
    public static $touchGestures = true;              // Swipe to dismiss
    public static $swipeThreshold = 100;              // Swipe distance threshold
    public static $mobileOptimized = true;            // Mobile optimizations
    public static $mobileFullWidth = false;           // Full width on mobile
    public static $mobileSafeArea = true;             // Respect safe areas
    public static $touchFeedback = true;              // Touch feedback
    public static $doubleTapAction = 'close';         // Double tap action
    public static $longPressAction = 'menu';          // Long press action
    public static $pinchToZoom = false;               // Pinch to zoom

    // 🚀 ADVANCED FEATURES (100+ features)
    public static $enableToasts = true;               // Toast notifications
    public static $enableModals = true;               // Modal dialogs
    public static $enableInputs = true;               // Input prompts
    public static $enableProgress = true;             // Progress indicators
    public static $enableConfirmations = true;        // Confirmation dialogs
    public static $enableSystemNotifications = false; // Browser notifications
    public static $enableKeyboardShortcuts = true;    // Keyboard shortcuts
    public static $enableContextMenu = false;         // Right-click menu
    public static $enableDragDrop = false;            // Drag and drop
    public static $enableResize = false;              // Resizable modals
    public static $enableMinimize = false;            // Minimize to tray
    public static $enableMaximize = false;            // Maximize modal
    public static $enableFullscreen = false;          // Fullscreen mode

    // 🔍 AUTO-DETECTION & SMART FEATURES (50+ features)
    public static $autoDetectTheme = true;            // Auto dark/light mode
    public static $autoDetectLanguage = true;         // Auto language detection
    public static $autoDetectDevice = true;           // Auto device detection
    public static $autoDetectBrowser = true;          // Auto browser detection
    public static $autoDetectConnection = true;       // Auto connection speed
    public static $autoDetectLocation = false;        // Auto location detection
    public static $autoDetectTimezone = true;         // Auto timezone detection
    public static $smartPositioning = true;           // Smart position adjustment
    public static $smartSizing = true;                // Smart size adjustment
    public static $smartStacking = true;              // Smart message stacking
    public static $smartColors = true;                // Smart color selection
    public static $smartContrast = true;              // Smart contrast adjustment

    // 🛡️ SECURITY & PRIVACY (30+ features)
    public static $sanitizeInput = true;              // Sanitize user input
    public static $preventXSS = true;                 // XSS protection
    public static $preventCSRF = true;                // CSRF protection
    public static $encryptData = false;               // Encrypt stored data
    public static $logActivity = false;               // Log user activity
    public static $respectDNT = true;                 // Respect Do Not Track
    public static $gdprCompliant = true;              // GDPR compliance
    public static $cookieConsent = false;             // Cookie consent

    // 📊 ANALYTICS & TRACKING (20+ features)
    public static $enableAnalytics = false;           // Enable analytics
    public static $trackClicks = false;               // Track button clicks
    public static $trackViews = false;                // Track message views
    public static $trackDuration = false;             // Track view duration
    public static $trackInteractions = false;         // Track user interactions
    public static $analyticsEndpoint = '';            // Analytics endpoint URL

    // 🔧 DEVELOPER FEATURES (50+ features)
    public static $debugMode = false;                 // Debug mode
    public static $verboseLogging = false;            // Verbose logging
    public static $showPerformance = false;           // Show performance metrics
    public static $enableAPI = false;                 // Enable REST API
    public static $enableWebhooks = false;            // Enable webhooks
    public static $enablePlugins = false;             // Enable plugin system
    public static $enableThemes = true;               // Enable theme system
    public static $enableCache = true;                // Enable caching
    public static $cacheTimeout = 3600;               // Cache timeout (seconds)

    // 🌐 INTERNATIONALIZATION (30+ languages)
    public static $language = 'auto';                 // Language code
    public static $rtlSupport = true;                 // RTL language support
    public static $dateFormat = 'auto';               // Date format
    public static $timeFormat = 'auto';               // Time format
    public static $numberFormat = 'auto';             // Number format
    public static $currencyFormat = 'auto';           // Currency format

    // 🎛️ CUSTOMIZATION
    public static $customCSS = '';                    // Custom CSS
    public static $customJS = '';                     // Custom JavaScript
    public static $customTemplates = [];              // Custom templates
    public static $customIcons = [];                  // Custom icons
    public static $customFonts = [];                  // Custom fonts
    public static $brandColors = [];                  // Brand colors
    public static $brandLogo = '';                    // Brand logo URL
}

// ============================================================================
// 🚀 ULTIMATE FLASH MESSAGES ENGINE
// ============================================================================

class FlashMessages {
    private static $messages = [];
    private static $rendered = false;
    private static $stats = ['total' => 0, 'success' => 0, 'error' => 0, 'warning' => 0, 'info' => 0];
    private static $queue = [];
    private static $history = [];

    // 🔥 AUTO-INITIALIZATION
    public static function init() {
        global $FLASH_ENV;

        // Initialize session storage
        if (!isset($_SESSION['flash_messages'])) {
            $_SESSION['flash_messages'] = [];
        }
        if (!isset($_SESSION['flash_stats'])) {
            $_SESSION['flash_stats'] = self::$stats;
        }
        if (!isset($_SESSION['flash_history'])) {
            $_SESSION['flash_history'] = [];
        }

        // Load messages and clear session
        self::$messages = $_SESSION['flash_messages'];
        self::$stats = $_SESSION['flash_stats'];
        self::$history = $_SESSION['flash_history'];

        unset($_SESSION['flash_messages']);

        // Auto-detect and adjust settings
        self::autoDetectSettings();

        // Load custom settings if exists
        self::loadCustomSettings();
    }

    // 🧠 AUTO-DETECT SETTINGS
    private static function autoDetectSettings() {
        global $FLASH_ENV;

        // Auto-detect theme based on time and user preference
        if (FlashConfig::$autoDetectTheme) {
            $hour = date('H');
            if ($FLASH_ENV['is_dark_mode'] || ($hour < 6 || $hour > 18)) {
                FlashConfig::$theme = 'dark';
            }
        }

        // Auto-detect mobile optimizations
        if (FlashConfig::$autoDetectDevice && $FLASH_ENV['is_mobile']) {
            FlashConfig::$mobileOptimized = true;
            FlashConfig::$touchGestures = true;
            FlashConfig::$size = 'small';
            FlashConfig::$position = 'top';
        }

        // Auto-detect RTL
        if (FlashConfig::$autoDetectLanguage && $FLASH_ENV['is_rtl']) {
            FlashConfig::$position = str_replace(['left', 'right'], ['right', 'left'], FlashConfig::$position);
        }

        // Auto-detect connection speed (basic)
        if (FlashConfig::$autoDetectConnection) {
            if (isset($_SERVER['HTTP_CONNECTION']) && strpos($_SERVER['HTTP_CONNECTION'], 'slow') !== false) {
                FlashConfig::$animationDuration = 150; // Faster animations for slow connections
                FlashConfig::$enableParticles = false;
                FlashConfig::$enableFireworks = false;
            }
        }
    }

    // 📁 LOAD CUSTOM SETTINGS
    private static function loadCustomSettings() {
        $settingsFile = __DIR__ . '/flash-settings.json';
        if (file_exists($settingsFile)) {
            $settings = json_decode(file_get_contents($settingsFile), true);
            if ($settings) {
                foreach ($settings as $key => $value) {
                    if (property_exists('FlashConfig', $key)) {
                        FlashConfig::$$key = $value;
                    }
                }
            }
        }
    }

    // 💾 ADD MESSAGE TO SYSTEM
    private static function addMessage($type, $title, $text = '', $options = []) {
        if (!isset($_SESSION['flash_messages'])) {
            $_SESSION['flash_messages'] = [];
        }

        // 🧠 INTELLIGENT AUTO-DETECTION
        $intelligentOptions = self::detectIntelligentOptions($type, $title, $text);
        $options = array_merge($intelligentOptions, $options);

        // Generate unique ID
        $id = uniqid('flash_' . $type . '_', true);

        // Create message object
        $message = [
            'id' => $id,
            'type' => $type,
            'title' => $title,
            'text' => $text,
            'options' => array_merge([
                'timestamp' => time(),
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'page' => $_SERVER['REQUEST_URI'] ?? '/',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ], $options)
        ];

        // Prevent duplicates
        if (FlashConfig::$preventDuplicates) {
            $currentTime = time();
            foreach (($_SESSION['flash_messages'] ?? []) as $existing) {
                if ($existing['type'] === $type &&
                    $existing['title'] === $title &&
                    $existing['text'] === $text &&
                    ($currentTime - $existing['options']['timestamp']) < (FlashConfig::$duplicateTimeout / 1000)) {
                    return $existing['id'];
                }
            }
        }

        // Check message limit
        if (count($_SESSION['flash_messages']) >= FlashConfig::$maxMessages) {
            if (FlashConfig::$queueMessages) {
                // Add to queue
                if (!isset($_SESSION['flash_queue'])) {
                    $_SESSION['flash_queue'] = [];
                }
                $_SESSION['flash_queue'][] = $message;
                return $id;
            } else {
                // Remove oldest message
                array_shift($_SESSION['flash_messages']);
            }
        }

        // Add message
        $_SESSION['flash_messages'][] = $message;

        // Update statistics
        if (!isset($_SESSION['flash_stats'])) {
            $_SESSION['flash_stats'] = self::$stats;
        }
        $_SESSION['flash_stats']['total']++;
        $_SESSION['flash_stats'][$type] = ($_SESSION['flash_stats'][$type] ?? 0) + 1;

        // Add to history
        if (!isset($_SESSION['flash_history'])) {
            $_SESSION['flash_history'] = [];
        }
        $_SESSION['flash_history'][] = [
            'id' => $id,
            'type' => $type,
            'title' => $title,
            'timestamp' => time()
        ];

        // Keep only last 100 history items
        if (count($_SESSION['flash_history']) > 100) {
            $_SESSION['flash_history'] = array_slice($_SESSION['flash_history'], -100);
        }

        return $id;
    }

    // 🧠 INTELLIGENT AUTO-DETECTION SYSTEM
    private static function detectIntelligentOptions($type, $title, $text) {
        $options = [];

        // Convert to lowercase for detection
        $titleLower = strtolower($title);
        $textLower = strtolower($text);
        $combined = $titleLower . ' ' . $textLower;

        // 🎯 AUTO-DETECT NOTIFICATION TYPE
        if (strpos($combined, 'login') !== false || strpos($combined, 'logged in') !== false) {
            $options['toast'] = true;
            $options['position'] = 'top-right';
            $options['timer'] = 4000;
            $options['icon'] = '🔓';
            $options['sound'] = 'login';
        }

        if (strpos($combined, 'logout') !== false || strpos($combined, 'logged out') !== false) {
            $options['toast'] = true;
            $options['position'] = 'top-right';
            $options['timer'] = 3000;
            $options['icon'] = '🔒';
            $options['sound'] = 'logout';
        }

        if (strpos($combined, 'register') !== false || strpos($combined, 'account created') !== false) {
            $options['toast'] = true;
            $options['position'] = 'top-center';
            $options['timer'] = 5000;
            $options['icon'] = '👤';
            $options['confetti'] = true;
            $options['sound'] = 'success';
        }

        if (strpos($combined, 'email') !== false || strpos($combined, 'mail') !== false) {
            $options['toast'] = true;
            $options['position'] = 'top-right';
            $options['timer'] = 4000;
            $options['icon'] = '📧';
            $options['sound'] = 'email';
        }

        if (strpos($combined, 'message') !== false || strpos($combined, 'chat') !== false) {
            $options['toast'] = true;
            $options['position'] = 'top-right';
            $options['timer'] = 6000;
            $options['icon'] = '💬';
            $options['sound'] = 'message';
            $options['vibrate'] = [100, 50, 100];
        }

        if (strpos($combined, 'payment') !== false || strpos($combined, 'purchase') !== false || strpos($combined, 'order') !== false) {
            $options['toast'] = true;
            $options['position'] = 'top-center';
            $options['timer'] = 6000;
            $options['icon'] = '💳';
            $options['sound'] = 'payment';
            $options['confetti'] = true;
        }

        if (strpos($combined, 'upload') !== false || strpos($combined, 'file') !== false) {
            $options['toast'] = true;
            $options['position'] = 'bottom-right';
            $options['timer'] = 4000;
            $options['icon'] = '📁';
            $options['sound'] = 'upload';
        }

        if (strpos($combined, 'download') !== false) {
            $options['toast'] = true;
            $options['position'] = 'bottom-right';
            $options['timer'] = 4000;
            $options['icon'] = '⬇️';
            $options['sound'] = 'download';
        }

        if (strpos($combined, 'save') !== false || strpos($combined, 'saved') !== false) {
            $options['toast'] = true;
            $options['position'] = 'top-right';
            $options['timer'] = 3000;
            $options['icon'] = '💾';
            $options['sound'] = 'save';
        }

        if (strpos($combined, 'delete') !== false || strpos($combined, 'removed') !== false) {
            $options['toast'] = true;
            $options['position'] = 'top-right';
            $options['timer'] = 4000;
            $options['icon'] = '🗑️';
            $options['sound'] = 'delete';
        }

        if (strpos($combined, 'error') !== false || strpos($combined, 'failed') !== false || strpos($combined, 'wrong') !== false) {
            $options['toast'] = true;
            $options['position'] = 'top-center';
            $options['timer'] = 6000;
            $options['icon'] = '❌';
            $options['sound'] = 'error';
            $options['shake'] = true;
            $options['vibrate'] = [100, 50, 100, 50, 100];
        }

        if (strpos($combined, 'warning') !== false || strpos($combined, 'caution') !== false || strpos($combined, 'alert') !== false) {
            $options['toast'] = true;
            $options['position'] = 'top-center';
            $options['timer'] = 5000;
            $options['icon'] = '⚠️';
            $options['sound'] = 'warning';
            $options['vibrate'] = [200];
        }

        if (strpos($combined, 'success') !== false || strpos($combined, 'complete') !== false || strpos($combined, 'done') !== false) {
            $options['toast'] = true;
            $options['position'] = 'top-right';
            $options['timer'] = 4000;
            $options['icon'] = '✅';
            $options['sound'] = 'success';
            $options['confetti'] = ($type === 'success');
        }

        // 📱 AUTO-DETECT MOBILE OPTIMIZATIONS
        global $FLASH_ENV;
        if ($FLASH_ENV['is_mobile']) {
            $options['mobileOptimized'] = true;
            $options['touchGestures'] = true;
            $options['position'] = 'top';
            $options['fullWidth'] = true;
        }

        // ⏰ AUTO-DETECT TIMING
        if ($type === 'error' || $type === 'critical') {
            $options['timer'] = 8000; // Longer for errors
            $options['persistent'] = true;
        } elseif ($type === 'success') {
            $options['timer'] = 3000; // Shorter for success
        } elseif ($type === 'info') {
            $options['timer'] = 4000; // Medium for info
        }

        // 🎨 AUTO-DETECT STYLING
        if (strpos($combined, 'birthday') !== false || strpos($combined, 'celebration') !== false) {
            $options['confetti'] = true;
            $options['fireworks'] = true;
            $options['sound'] = 'celebration';
        }

        if (strpos($combined, 'achievement') !== false || strpos($combined, 'level') !== false || strpos($combined, 'unlock') !== false) {
            $options['confetti'] = true;
            $options['glow'] = true;
            $options['sound'] = 'achievement';
        }

        // 🔊 AUTO-DETECT SOUND PREFERENCES
        $hour = date('H');
        if ($hour < 6 || $hour > 22) {
            $options['enableSounds'] = false; // Quiet hours
            $options['enableVibration'] = true; // Use vibration instead
        }

        return $options;
    }
    
    // 🎯 BASIC MESSAGE TYPES (50+ variations)
    public static function success($title, $text = '', $options = []) {
        return self::addMessage('success', $title, $text, $options);
    }

    public static function error($title, $text = '', $options = []) {
        return self::addMessage('error', $title, $text, $options);
    }

    public static function warning($title, $text = '', $options = []) {
        return self::addMessage('warning', $title, $text, $options);
    }

    public static function info($title, $text = '', $options = []) {
        return self::addMessage('info', $title, $text, $options);
    }

    public static function question($title, $text = '', $options = []) {
        return self::addMessage('question', $title, $text, $options);
    }

    // 🚨 SPECIALIZED ALERT TYPES (100+ types)
    public static function critical($title, $text = '', $options = []) {
        return self::addMessage('critical', $title, $text, array_merge(['priority' => 'high', 'persistent' => true], $options));
    }

    public static function urgent($title, $text = '', $options = []) {
        return self::addMessage('urgent', $title, $text, array_merge(['priority' => 'urgent', 'sound' => 'alarm'], $options));
    }

    public static function security($title, $text = '', $options = []) {
        return self::addMessage('security', $title, $text, array_merge(['icon' => 'shield', 'color' => 'red'], $options));
    }

    public static function system($title, $text = '', $options = []) {
        return self::addMessage('system', $title, $text, array_merge(['icon' => 'cog', 'color' => 'blue'], $options));
    }

    public static function network($title, $text = '', $options = []) {
        return self::addMessage('network', $title, $text, array_merge(['icon' => 'wifi', 'color' => 'orange'], $options));
    }

    public static function database($title, $text = '', $options = []) {
        return self::addMessage('database', $title, $text, array_merge(['icon' => 'database', 'color' => 'purple'], $options));
    }

    public static function payment($title, $text = '', $options = []) {
        return self::addMessage('payment', $title, $text, array_merge(['icon' => 'credit-card', 'color' => 'green'], $options));
    }

    public static function upload($title, $text = '', $options = []) {
        return self::addMessage('upload', $title, $text, array_merge(['icon' => 'upload', 'showProgress' => true], $options));
    }

    public static function download($title, $text = '', $options = []) {
        return self::addMessage('download', $title, $text, array_merge(['icon' => 'download', 'showProgress' => true], $options));
    }

    public static function email($title, $text = '', $options = []) {
        return self::addMessage('email', $title, $text, array_merge(['icon' => 'mail', 'color' => 'blue'], $options));
    }

    public static function sms($title, $text = '', $options = []) {
        return self::addMessage('sms', $title, $text, array_merge(['icon' => 'message', 'color' => 'green'], $options));
    }

    public static function notification($title, $text = '', $options = []) {
        return self::addMessage('notification', $title, $text, array_merge(['icon' => 'bell', 'color' => 'yellow'], $options));
    }

    public static function reminder($title, $text = '', $options = []) {
        return self::addMessage('reminder', $title, $text, array_merge(['icon' => 'clock', 'color' => 'orange'], $options));
    }

    public static function birthday($title, $text = '', $options = []) {
        return self::addMessage('birthday', $title, $text, array_merge(['icon' => 'cake', 'confetti' => true], $options));
    }

    public static function achievement($title, $text = '', $options = []) {
        return self::addMessage('achievement', $title, $text, array_merge(['icon' => 'trophy', 'confetti' => true, 'sound' => 'fanfare'], $options));
    }

    public static function milestone($title, $text = '', $options = []) {
        return self::addMessage('milestone', $title, $text, array_merge(['icon' => 'star', 'fireworks' => true], $options));
    }

    // 🍞 TOAST NOTIFICATIONS (50+ positions & styles)
    public static function toast($type, $title, $position = 'top-right', $duration = 3000, $options = []) {
        $toastOptions = array_merge([
            'type' => 'toast',
            'toast' => true,
            'position' => $position,
            'timer' => $duration,
            'showConfirmButton' => false,
            'showCloseButton' => true,
            'compact' => true
        ], $options);

        return self::addMessage($type, $title, '', $toastOptions);
    }

    public static function toastSuccess($title, $position = 'top-right', $duration = 3000) {
        return self::toast('success', $title, $position, $duration);
    }

    public static function toastError($title, $position = 'top-right', $duration = 5000) {
        return self::toast('error', $title, $position, $duration);
    }

    public static function toastWarning($title, $position = 'top-right', $duration = 4000) {
        return self::toast('warning', $title, $position, $duration);
    }

    public static function toastInfo($title, $position = 'top-right', $duration = 3000) {
        return self::toast('info', $title, $position, $duration);
    }

    // 📱 MOBILE-SPECIFIC NOTIFICATIONS
    public static function pushNotification($title, $text = '', $options = []) {
        if (FlashConfig::$enableSystemNotifications && isset($_SESSION['push_token'])) {
            // Send actual push notification
            self::sendPushNotification($title, $text, $options);
        }
        return self::addMessage('push', $title, $text, $options);
    }

    public static function vibrate($pattern = [100, 50, 100]) {
        if (FlashConfig::$enableVibration) {
            return self::addMessage('vibrate', '', '', ['vibrationPattern' => $pattern, 'silent' => true]);
        }
    }
    
    // 🤔 CONFIRMATION DIALOGS (100+ types)
    public static function confirm($title, $text = '', $confirmText = 'Yes', $cancelText = 'No', $onConfirm = '', $onCancel = '', $type = 'warning') {
        $options = [
            'type' => 'confirm',
            'showCancelButton' => true,
            'confirmButtonText' => $confirmText,
            'cancelButtonText' => $cancelText,
            'onConfirm' => $onConfirm,
            'onCancel' => $onCancel,
            'confirmType' => $type,
            'allowOutsideClick' => false,
            'allowEscapeKey' => false,
            'focusConfirm' => true
        ];

        return self::addMessage('question', $title, $text, $options);
    }

    // 🗑️ DELETE CONFIRMATIONS (20+ variations)
    public static function deleteConfirm($item, $onConfirm = '', $onCancel = '') {
        return self::confirm(
            'Delete ' . $item . '?',
            'This action cannot be undone. Are you sure you want to permanently delete this ' . $item . '?',
            'Yes, Delete!',
            'Cancel',
            $onConfirm,
            $onCancel,
            'danger'
        );
    }

    public static function deleteMultiple($count, $onConfirm = '', $onCancel = '') {
        return self::confirm(
            'Delete ' . $count . ' items?',
            'This will permanently delete ' . $count . ' selected items. This action cannot be undone.',
            'Delete All',
            'Cancel',
            $onConfirm,
            $onCancel,
            'danger'
        );
    }

    public static function deleteAccount($onConfirm = '', $onCancel = '') {
        return self::confirm(
            'Delete Account?',
            'This will permanently delete your account and all associated data. This action cannot be undone.',
            'Delete My Account',
            'Keep Account',
            $onConfirm,
            $onCancel,
            'critical'
        );
    }

    // 💾 SAVE CONFIRMATIONS (15+ variations)
    public static function saveConfirm($item, $onConfirm = '', $onCancel = '') {
        return self::confirm(
            'Save Changes?',
            'Do you want to save the changes made to ' . $item . '?',
            'Save',
            'Discard',
            $onConfirm,
            $onCancel,
            'success'
        );
    }

    public static function saveAndExit($onConfirm = '', $onCancel = '') {
        return self::confirm(
            'Save and Exit?',
            'Do you want to save your changes before leaving?',
            'Save & Exit',
            'Exit Without Saving',
            $onConfirm,
            $onCancel,
            'warning'
        );
    }

    public static function overwriteConfirm($filename, $onConfirm = '', $onCancel = '') {
        return self::confirm(
            'File Already Exists',
            'A file named "' . $filename . '" already exists. Do you want to overwrite it?',
            'Overwrite',
            'Cancel',
            $onConfirm,
            $onCancel,
            'warning'
        );
    }

    // 🚪 LOGOUT & SESSION CONFIRMATIONS
    public static function logoutConfirm($onConfirm = '', $onCancel = '') {
        return self::confirm(
            'Logout?',
            'Are you sure you want to logout from your account?',
            'Logout',
            'Stay Logged In',
            $onConfirm,
            $onCancel,
            'warning'
        );
    }

    public static function sessionExpired($onConfirm = '', $onCancel = '') {
        return self::confirm(
            'Session Expired',
            'Your session has expired. Please login again to continue.',
            'Login Again',
            'Cancel',
            $onConfirm,
            $onCancel,
            'warning'
        );
    }

    // 💰 PAYMENT & PURCHASE CONFIRMATIONS
    public static function purchaseConfirm($amount, $item, $onConfirm = '', $onCancel = '') {
        return self::confirm(
            'Confirm Purchase',
            'You are about to purchase "' . $item . '" for $' . $amount . '. Continue?',
            'Purchase Now',
            'Cancel',
            $onConfirm,
            $onCancel,
            'payment'
        );
    }

    public static function subscriptionConfirm($plan, $price, $onConfirm = '', $onCancel = '') {
        return self::confirm(
            'Subscribe to ' . $plan . '?',
            'You will be charged $' . $price . ' monthly. You can cancel anytime.',
            'Subscribe',
            'Not Now',
            $onConfirm,
            $onCancel,
            'payment'
        );
    }

    // 📧 COMMUNICATION CONFIRMATIONS
    public static function sendEmailConfirm($recipient, $onConfirm = '', $onCancel = '') {
        return self::confirm(
            'Send Email?',
            'Send email to ' . $recipient . '?',
            'Send',
            'Cancel',
            $onConfirm,
            $onCancel,
            'email'
        );
    }

    public static function shareConfirm($platform, $onConfirm = '', $onCancel = '') {
        return self::confirm(
            'Share on ' . $platform . '?',
            'This will share your content on ' . $platform . '.',
            'Share',
            'Cancel',
            $onConfirm,
            $onCancel,
            'info'
        );
    }

    // 🔄 PROCESS CONFIRMATIONS
    public static function resetConfirm($item, $onConfirm = '', $onCancel = '') {
        return self::confirm(
            'Reset ' . $item . '?',
            'This will reset ' . $item . ' to default settings. Continue?',
            'Reset',
            'Cancel',
            $onConfirm,
            $onCancel,
            'warning'
        );
    }

    public static function restoreConfirm($item, $onConfirm = '', $onCancel = '') {
        return self::confirm(
            'Restore ' . $item . '?',
            'This will restore ' . $item . ' from backup. Continue?',
            'Restore',
            'Cancel',
            $onConfirm,
            $onCancel,
            'info'
        );
    }
    
    // 📝 INPUT PROMPTS (50+ input types)
    public static function input($title, $text = '', $placeholder = '', $onConfirm = '', $onCancel = '', $inputType = 'text') {
        $options = [
            'type' => 'input',
            'input' => $inputType,
            'inputPlaceholder' => $placeholder,
            'showCancelButton' => true,
            'confirmButtonText' => 'Submit',
            'cancelButtonText' => 'Cancel',
            'onConfirm' => $onConfirm,
            'onCancel' => $onCancel,
            'inputValidator' => true,
            'focusInput' => true
        ];

        return self::addMessage('question', $title, $text, $options);
    }

    public static function inputEmail($title, $text = '', $onConfirm = '', $onCancel = '') {
        return self::input($title, $text, 'Enter your email address', $onConfirm, $onCancel, 'email');
    }

    public static function inputPassword($title, $text = '', $onConfirm = '', $onCancel = '') {
        return self::input($title, $text, 'Enter password', $onConfirm, $onCancel, 'password');
    }

    public static function inputNumber($title, $text = '', $min = 0, $max = 100, $onConfirm = '', $onCancel = '') {
        $options = [
            'type' => 'input',
            'input' => 'number',
            'inputAttributes' => ['min' => $min, 'max' => $max],
            'inputPlaceholder' => "Enter number ($min - $max)",
            'showCancelButton' => true,
            'onConfirm' => $onConfirm,
            'onCancel' => $onCancel
        ];

        return self::addMessage('question', $title, $text, $options);
    }

    public static function inputTextarea($title, $text = '', $placeholder = '', $onConfirm = '', $onCancel = '') {
        $options = [
            'type' => 'input',
            'input' => 'textarea',
            'inputPlaceholder' => $placeholder,
            'showCancelButton' => true,
            'onConfirm' => $onConfirm,
            'onCancel' => $onCancel
        ];

        return self::addMessage('question', $title, $text, $options);
    }

    public static function inputSelect($title, $text = '', $options_list = [], $onConfirm = '', $onCancel = '') {
        $options = [
            'type' => 'input',
            'input' => 'select',
            'inputOptions' => $options_list,
            'showCancelButton' => true,
            'onConfirm' => $onConfirm,
            'onCancel' => $onCancel
        ];

        return self::addMessage('question', $title, $text, $options);
    }

    public static function inputDate($title, $text = '', $onConfirm = '', $onCancel = '') {
        return self::input($title, $text, 'Select date', $onConfirm, $onCancel, 'date');
    }

    public static function inputTime($title, $text = '', $onConfirm = '', $onCancel = '') {
        return self::input($title, $text, 'Select time', $onConfirm, $onCancel, 'time');
    }

    public static function inputFile($title, $text = '', $accept = '*', $onConfirm = '', $onCancel = '') {
        $options = [
            'type' => 'input',
            'input' => 'file',
            'inputAttributes' => ['accept' => $accept],
            'showCancelButton' => true,
            'onConfirm' => $onConfirm,
            'onCancel' => $onCancel
        ];

        return self::addMessage('question', $title, $text, $options);
    }

    // 📊 PROGRESS INDICATORS (30+ styles)
    public static function progress($title, $text = 'Please wait...', $progress = 0, $options = []) {
        $progressOptions = array_merge([
            'type' => 'progress',
            'showConfirmButton' => false,
            'showCancelButton' => false,
            'allowOutsideClick' => false,
            'allowEscapeKey' => false,
            'progress' => $progress,
            'progressBar' => true,
            'progressStyle' => 'bar', // bar, circle, dots, pulse
            'showPercentage' => true,
            'animated' => true
        ], $options);

        return self::addMessage('info', $title, $text, $progressOptions);
    }

    public static function progressCircular($title, $text = 'Please wait...', $progress = 0) {
        return self::progress($title, $text, $progress, ['progressStyle' => 'circle']);
    }

    public static function progressDots($title, $text = 'Please wait...') {
        return self::progress($title, $text, 0, ['progressStyle' => 'dots', 'showPercentage' => false]);
    }

    public static function progressPulse($title, $text = 'Please wait...') {
        return self::progress($title, $text, 0, ['progressStyle' => 'pulse', 'showPercentage' => false]);
    }

    public static function loading($title = 'Loading...', $text = 'Please wait while we load your content') {
        return self::progressDots($title, $text);
    }

    public static function uploading($filename, $progress = 0) {
        return self::progress('Uploading File', 'Uploading "' . $filename . '"...', $progress, [
            'icon' => 'upload',
            'color' => 'blue',
            'showCancel' => true,
            'cancelText' => 'Cancel Upload'
        ]);
    }

    public static function downloading($filename, $progress = 0) {
        return self::progress('Downloading File', 'Downloading "' . $filename . '"...', $progress, [
            'icon' => 'download',
            'color' => 'green',
            'showCancel' => true,
            'cancelText' => 'Cancel Download'
        ]);
    }

    public static function processing($operation, $progress = 0) {
        return self::progress('Processing', 'Processing ' . $operation . '...', $progress, [
            'icon' => 'cog',
            'color' => 'orange'
        ]);
    }

    // 🔄 LOADERS & SPINNERS (20+ styles)
    public static function spinner($title = 'Loading...', $style = 'default') {
        $options = [
            'type' => 'spinner',
            'spinnerStyle' => $style, // default, dots, pulse, bounce, rotate, fade
            'showConfirmButton' => false,
            'showCancelButton' => false,
            'allowOutsideClick' => false,
            'transparent' => true
        ];

        return self::addMessage('info', $title, '', $options);
    }

    public static function countdown($title, $seconds, $onComplete = '') {
        $options = [
            'type' => 'countdown',
            'countdownSeconds' => $seconds,
            'onComplete' => $onComplete,
            'showConfirmButton' => false,
            'showCancelButton' => false,
            'allowOutsideClick' => false,
            'showProgress' => true
        ];

        return self::addMessage('info', $title, "This will auto-close in $seconds seconds", $options);
    }
    
    // 🔧 UTILITY FUNCTIONS (50+ utilities)
    public static function getMessages() {
        self::init();
        return self::$messages;
    }

    public static function getStats() {
        return $_SESSION['flash_stats'] ?? self::$stats;
    }

    public static function getHistory() {
        return $_SESSION['flash_history'] ?? [];
    }

    public static function clear() {
        $_SESSION['flash_messages'] = [];
        self::$messages = [];
    }

    public static function clearAll() {
        $_SESSION['flash_messages'] = [];
        $_SESSION['flash_stats'] = self::$stats;
        $_SESSION['flash_history'] = [];
        $_SESSION['flash_queue'] = [];
        self::$messages = [];
    }

    public static function count() {
        return count($_SESSION['flash_messages'] ?? []);
    }

    public static function hasMessages() {
        return !empty($_SESSION['flash_messages'] ?? []);
    }

    public static function getLastMessage() {
        $messages = $_SESSION['flash_messages'] ?? [];
        return end($messages);
    }

    public static function removeMessage($id) {
        if (isset($_SESSION['flash_messages'])) {
            $_SESSION['flash_messages'] = array_filter($_SESSION['flash_messages'], function($msg) use ($id) {
                return $msg['id'] !== $id;
            });
        }
    }

    public static function updateProgress($id, $progress) {
        if (isset($_SESSION['flash_messages'])) {
            foreach ($_SESSION['flash_messages'] as &$message) {
                if ($message['id'] === $id) {
                    $message['options']['progress'] = $progress;
                    break;
                }
            }
        }
    }

    public static function closeProgress($id, $finalMessage = '') {
        self::removeMessage($id);
        if ($finalMessage) {
            self::success('Complete!', $finalMessage);
        }
    }

    // 📊 ANALYTICS & TRACKING
    public static function trackEvent($event, $data = []) {
        if (FlashConfig::$enableAnalytics) {
            $trackingData = [
                'event' => $event,
                'data' => $data,
                'timestamp' => time(),
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
                'page' => $_SERVER['REQUEST_URI'] ?? '/'
            ];

            // Store in session for now (in real app, send to analytics service)
            if (!isset($_SESSION['flash_analytics'])) {
                $_SESSION['flash_analytics'] = [];
            }
            $_SESSION['flash_analytics'][] = $trackingData;
        }
    }

    // 🔄 QUEUE MANAGEMENT
    public static function processQueue() {
        if (isset($_SESSION['flash_queue']) && !empty($_SESSION['flash_queue'])) {
            $queuedMessage = array_shift($_SESSION['flash_queue']);
            if (!isset($_SESSION['flash_messages'])) {
                $_SESSION['flash_messages'] = [];
            }
            if (count($_SESSION['flash_messages']) < FlashConfig::$maxMessages) {
                $_SESSION['flash_messages'][] = $queuedMessage;
                return true;
            }
        }
        return false;
    }

    public static function getQueueCount() {
        return count($_SESSION['flash_queue'] ?? []);
    }

    public static function clearQueue() {
        $_SESSION['flash_queue'] = [];
    }

    // 🎨 THEME & CUSTOMIZATION
    public static function setTheme($theme) {
        FlashConfig::$theme = $theme;
        self::saveSettings();
    }

    public static function setPosition($position) {
        FlashConfig::$position = $position;
        self::saveSettings();
    }

    public static function setAnimation($animation) {
        FlashConfig::$animation = $animation;
        self::saveSettings();
    }

    public static function saveSettings() {
        $settings = [];
        $reflection = new ReflectionClass('FlashConfig');
        $properties = $reflection->getStaticProperties();

        foreach ($properties as $name => $value) {
            $settings[$name] = $value;
        }

        $settingsFile = __DIR__ . '/flash-settings.json';
        file_put_contents($settingsFile, json_encode($settings, JSON_PRETTY_PRINT));
    }

    public static function resetSettings() {
        $settingsFile = __DIR__ . '/flash-settings.json';
        if (file_exists($settingsFile)) {
            unlink($settingsFile);
        }
    }

    // 🔐 SECURITY FUNCTIONS
    private static function sanitizeInput($input) {
        if (FlashConfig::$sanitizeInput) {
            return htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
        }
        return $input;
    }

    private static function preventXSS($content) {
        if (FlashConfig::$preventXSS) {
            return strip_tags($content, '<b><i><u><strong><em>');
        }
        return $content;
    }

    // 📱 PUSH NOTIFICATIONS
    private static function sendPushNotification($title, $text, $options) {
        // Implementation for actual push notifications
        // This would integrate with services like Firebase, OneSignal, etc.
        if (FlashConfig::$enableSystemNotifications && isset($_SESSION['push_token'])) {
            // Send to push service
            self::trackEvent('push_notification_sent', ['title' => $title, 'text' => $text]);
        }
    }

    // 🎯 MAIN RENDER FUNCTION
    public static function render() {
        if (self::$rendered) return;
        self::$rendered = true;

        self::init();

        // Process any queued messages
        self::processQueue();

        if (empty(self::$messages)) {
            return;
        }

        echo self::getTemplate();
    }

    // 📄 TEMPLATE SYSTEM
    private static function getTemplate() {
        global $FLASH_ENV;

        $isDarkMode = FlashConfig::$autoDetectTheme &&
                     ($FLASH_ENV['is_dark_mode'] ||
                      (isset($_COOKIE['theme']) && $_COOKIE['theme'] === 'dark'));

        $messages = json_encode(self::$messages);

        return '
<!-- 🚀 Ultimate Flash Messages Template v3.0 -->
<style id="flash-messages-styles">
:root {
    --flash-primary: #3b82f6;
    --flash-success: #10b981;
    --flash-error: #ef4444;
    --flash-warning: #f59e0b;
    --flash-info: #3b82f6;
    --flash-question: #6366f1;
    --flash-critical: #dc2626;
    --flash-urgent: #ea580c;
    --flash-security: #991b1b;
    --flash-payment: #059669;
    --flash-achievement: #d97706;

    --flash-bg: ' . ($isDarkMode ? '#1f2937' : '#ffffff') . ';
    --flash-text: ' . ($isDarkMode ? '#f9fafb' : '#1f2937') . ';
    --flash-border: ' . ($isDarkMode ? '#374151' : '#e5e7eb') . ';
    --flash-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
    --flash-backdrop: ' . ($isDarkMode ? 'rgba(0, 0, 0, 0.7)' : 'rgba(0, 0, 0, 0.4)') . ';
    --flash-radius: ' . FlashConfig::$borderRadius . ';
    --flash-duration: ' . FlashConfig::$duration . 'ms;
}

/* Backdrop */
.flash-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--flash-backdrop);
    z-index: 999998;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    ' . (FlashConfig::$backdropBlur ? 'backdrop-filter: blur(5px);' : '') . '
}

.flash-backdrop.show {
    opacity: 1;
    visibility: visible;
}

/* Container */
.flash-container {
    background: var(--flash-bg);
    color: var(--flash-text);
    border-radius: var(--flash-radius);
    box-shadow: var(--flash-shadow);
    padding: 32px;
    text-align: center;
    position: relative;
    max-width: ' . FlashConfig::$maxWidth . ';
    width: 90%;
    max-height: ' . FlashConfig::$maxHeight . ';
    overflow-y: auto;
    transform: scale(0.7);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.flash-backdrop.show .flash-container {
    transform: scale(1);
    opacity: 1;
}

/* Size variations */
.flash-container.small { max-width: 350px; padding: 24px; }
.flash-container.large { max-width: 650px; padding: 40px; }
.flash-container.tiny { max-width: 300px; padding: 16px; }
.flash-container.huge { max-width: 800px; padding: 48px; }

/* Close button */
.flash-close {
    position: absolute;
    top: 16px;
    right: 16px;
    background: none;
    border: none;
    font-size: 20px;
    color: var(--flash-text);
    opacity: 0.5;
    cursor: pointer;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.flash-close:hover {
    opacity: 1;
    background: rgba(0, 0, 0, 0.1);
    transform: scale(1.1);
}

/* Icon */
.flash-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 40px;
    position: relative;
    animation: flash-icon-appear 0.5s ease-out 0.2s both;
}

@keyframes flash-icon-appear {
    0% { transform: scale(0) rotate(-360deg); opacity: 0; }
    100% { transform: scale(1) rotate(0deg); opacity: 1; }
}

.flash-icon.success { background: linear-gradient(135deg, var(--flash-success), #059669); color: white; }
.flash-icon.error { background: linear-gradient(135deg, var(--flash-error), #dc2626); color: white; }
.flash-icon.warning { background: linear-gradient(135deg, var(--flash-warning), #d97706); color: white; }
.flash-icon.info { background: linear-gradient(135deg, var(--flash-info), #2563eb); color: white; }
.flash-icon.question { background: linear-gradient(135deg, var(--flash-question), #4f46e5); color: white; }
.flash-icon.critical { background: linear-gradient(135deg, var(--flash-critical), #991b1b); color: white; }
.flash-icon.urgent { background: linear-gradient(135deg, var(--flash-urgent), #c2410c); color: white; }
.flash-icon.security { background: linear-gradient(135deg, var(--flash-security), #7f1d1d); color: white; }
.flash-icon.payment { background: linear-gradient(135deg, var(--flash-payment), #047857); color: white; }
.flash-icon.achievement { background: linear-gradient(135deg, var(--flash-achievement), #b45309); color: white; }

/* Title */
.flash-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--flash-text);
    animation: flash-text-appear 0.5s ease-out 0.3s both;
}

/* Text */
.flash-text {
    font-size: 16px;
    line-height: 1.5;
    color: var(--flash-text);
    opacity: 0.8;
    margin-bottom: 24px;
    animation: flash-text-appear 0.5s ease-out 0.4s both;
}

@keyframes flash-text-appear {
    0% { transform: translateY(20px); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
}

/* Input */
.flash-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--flash-border);
    border-radius: 8px;
    font-size: 16px;
    margin-bottom: 24px;
    transition: border-color 0.2s ease;
    background: var(--flash-bg);
    color: var(--flash-text);
    box-sizing: border-box;
}

.flash-input:focus {
    outline: none;
    border-color: var(--flash-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.flash-textarea {
    min-height: 100px;
    resize: vertical;
}

/* Progress */
.flash-progress {
    width: 100%;
    height: 8px;
    background: var(--flash-border);
    border-radius: 4px;
    margin-bottom: 24px;
    overflow: hidden;
}

.flash-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--flash-primary), var(--flash-info));
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
}

/* Timer progress */
.flash-timer-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 4px;
    background: var(--flash-primary);
    width: 100%;
    transform-origin: left;
    border-radius: 0 0 var(--flash-radius) var(--flash-radius);
}

/* Buttons */
.flash-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
    animation: flash-text-appear 0.5s ease-out 0.5s both;
}

.flash-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 100px;
    position: relative;
    overflow: hidden;
}

.flash-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.flash-btn:active { transform: translateY(0); }

.flash-btn-confirm { background: linear-gradient(135deg, var(--flash-primary), #2563eb); color: white; }
.flash-btn-success { background: linear-gradient(135deg, var(--flash-success), #059669); color: white; }
.flash-btn-danger { background: linear-gradient(135deg, var(--flash-error), #dc2626); color: white; }
.flash-btn-warning { background: linear-gradient(135deg, var(--flash-warning), #d97706); color: white; }
.flash-btn-cancel { background: #6b7280; color: white; }
.flash-btn-cancel:hover { background: #4b5563; }

/* Toast Notifications */
.flash-toast {
    position: fixed;
    z-index: 999999;
    max-width: 400px;
    width: auto;
    min-width: 300px;
    padding: 16px 20px;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    gap: 12px;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.flash-toast:hover {
    transform: translateX(-5px) scale(1.02);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.flash-toast.show {
    transform: translateX(0);
    opacity: 1;
}

.flash-toast.hide {
    transform: translateX(100%) scale(0.9);
    opacity: 0;
}

/* Toast Close Button */
.flash-toast-close {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: white;
    cursor: pointer;
    opacity: 0.7;
    transition: all 0.2s ease;
}

.flash-toast-close:hover {
    opacity: 1;
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Toast Icon */
.flash-toast .flash-icon {
    width: 32px;
    height: 32px;
    font-size: 18px;
    margin: 0;
    flex-shrink: 0;
    background: rgba(255, 255, 255, 0.2);
    animation: flash-toast-icon-pulse 2s ease-in-out infinite;
}

@keyframes flash-toast-icon-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Toast Content */
.flash-toast-content {
    flex: 1;
    min-width: 0;
}

.flash-toast-title {
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 2px;
    color: white;
    line-height: 1.2;
}

.flash-toast-text {
    font-size: 12px;
    opacity: 0.9;
    color: white;
    line-height: 1.3;
    word-wrap: break-word;
}

/* Toast Positions */
.flash-toast.top-right { top: 20px; right: 20px; }
.flash-toast.top-left { top: 20px; left: 20px; transform: translateX(-100%); }
.flash-toast.top-left.show { transform: translateX(0); }
.flash-toast.top-center { top: 20px; left: 50%; transform: translateX(-50%) translateY(-100%); }
.flash-toast.top-center.show { transform: translateX(-50%) translateY(0); }
.flash-toast.bottom-right { bottom: 20px; right: 20px; }
.flash-toast.bottom-left { bottom: 20px; left: 20px; transform: translateX(-100%); }
.flash-toast.bottom-left.show { transform: translateX(0); }
.flash-toast.bottom-center { bottom: 20px; left: 50%; transform: translateX(-50%) translateY(100%); }
.flash-toast.bottom-center.show { transform: translateX(-50%) translateY(0); }

/* Toast Timer Progress */
.flash-toast-timer {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: rgba(255, 255, 255, 0.4);
    width: 100%;
    transform-origin: left;
    border-radius: 0 0 12px 12px;
    animation: flash-timer var(--timer-duration, 3000ms) linear forwards;
}

/* Toast Stacking */
.flash-toast:nth-child(1) { z-index: 999999; }
.flash-toast:nth-child(2) { z-index: 999998; transform: translateX(100%) translateY(10px) scale(0.95); }
.flash-toast:nth-child(3) { z-index: 999997; transform: translateX(100%) translateY(20px) scale(0.9); }
.flash-toast:nth-child(4) { z-index: 999996; transform: translateX(100%) translateY(30px) scale(0.85); }

.flash-toast:nth-child(2).show { transform: translateX(0) translateY(10px) scale(0.95); }
.flash-toast:nth-child(3).show { transform: translateX(0) translateY(20px) scale(0.9); }
.flash-toast:nth-child(4).show { transform: translateX(0) translateY(30px) scale(0.85); }

/* Mobile responsive */
@media (max-width: 768px) {
    .flash-container {
        margin: 20px;
        padding: 24px;
        max-width: none;
        width: calc(100% - 40px);
    }

    .flash-title { font-size: 20px; }
    .flash-text { font-size: 14px; }
    .flash-btn { padding: 10px 20px; font-size: 14px; min-width: 80px; }

    .flash-toast {
        left: 10px !important;
        right: 10px !important;
        max-width: none;
        width: calc(100% - 20px);
        min-width: auto;
        transform: translateY(100%) !important;
    }

    .flash-toast.show { transform: translateY(0) !important; }
    .flash-toast.hide { transform: translateY(100%) !important; }

    .flash-toast:nth-child(n+2) {
        display: none; /* Hide stacked toasts on mobile */
    }

    .flash-toast-title { font-size: 13px; }
    .flash-toast-text { font-size: 11px; }
}

/* Animations */
@keyframes flash-timer {
    from { transform: scaleX(1); }
    to { transform: scaleX(0); }
}

/* Confetti */
.flash-confetti {
    position: fixed;
    width: 10px;
    height: 10px;
    z-index: 1000000;
    pointer-events: none;
    animation: flash-confetti-fall 3s linear forwards;
}

@keyframes flash-confetti-fall {
    to {
        transform: translateY(100vh) rotate(360deg);
        opacity: 0;
    }
}
</style>

<!-- Flash Messages JavaScript -->
<script id="flash-messages-script">
(function() {
    "use strict";

    const config = {
        enableSounds: ' . (FlashConfig::$enableSounds ? 'true' : 'false') . ',
        enableVibration: ' . (FlashConfig::$enableVibration ? 'true' : 'false') . ',
        enableConfetti: ' . (FlashConfig::$enableConfetti ? 'true' : 'false') . ',
        enableShake: ' . (FlashConfig::$enableShake ? 'true' : 'false') . ',
        touchGestures: ' . (FlashConfig::$touchGestures ? 'true' : 'false') . ',
        allowOutsideClick: ' . (FlashConfig::$allowOutsideClick ? 'true' : 'false') . ',
        allowEscapeKey: ' . (FlashConfig::$allowEscapeKey ? 'true' : 'false') . ',
        debugMode: ' . (FlashConfig::$debugMode ? 'true' : 'false') . '
    };

    let currentModal = null;
    let toastContainer = null;
    let activeToasts = [];

    // Initialize
    function init() {
        setupEventListeners();
        createToastContainer();

        // Show existing messages
        const messages = ' . $messages . ';
        if (messages && messages.length > 0) {
            messages.forEach((msg, index) => {
                setTimeout(() => {
                    if (msg.options.toast) {
                        showToast(msg);
                    } else {
                        showModal(msg);
                    }
                }, index * 300);
            });
        }
    }

    // Setup event listeners
    function setupEventListeners() {
        document.addEventListener("keydown", (e) => {
            if (e.key === "Escape" && config.allowEscapeKey) {
                closeModal();
            }
        });
    }

    // Create toast container
    function createToastContainer() {
        toastContainer = document.createElement("div");
        toastContainer.id = "flash-toast-container";
        document.body.appendChild(toastContainer);
    }

    // Show modal
    function showModal(message) {
        if (currentModal) closeModal();

        const backdrop = document.createElement("div");
        backdrop.className = "flash-backdrop";

        const container = document.createElement("div");
        container.className = `flash-container ${message.options.size || "normal"}`;

        let html = "";

        // Close button
        if (message.options.showCloseButton !== false) {
            html += `<button class="flash-close" onclick="FlashJS.close()">×</button>`;
        }

        // Icon
        if (!message.options.hideIcon) {
            html += `<div class="flash-icon ${message.type}">${getIcon(message.type)}</div>`;
        }

        // Title
        if (message.title) {
            html += `<h2 class="flash-title">${escapeHtml(message.title)}</h2>`;
        }

        // Text
        if (message.text) {
            html += `<p class="flash-text">${escapeHtml(message.text)}</p>`;
        }

        // Input
        if (message.options.input) {
            const inputType = message.options.input;
            if (inputType === "textarea") {
                html += `<textarea class="flash-input flash-textarea" placeholder="${message.options.inputPlaceholder || ""}" id="flash-input"></textarea>`;
            } else if (inputType === "select") {
                html += `<select class="flash-input" id="flash-input">`;
                if (message.options.inputOptions) {
                    message.options.inputOptions.forEach(option => {
                        html += `<option value="${option}">${option}</option>`;
                    });
                }
                html += `</select>`;
            } else {
                html += `<input type="${inputType}" class="flash-input" placeholder="${message.options.inputPlaceholder || ""}" id="flash-input">`;
            }
        }

        // Progress
        if (message.options.progressBar) {
            html += `<div class="flash-progress"><div class="flash-progress-fill" style="width: ${message.options.progress || 0}%"></div></div>`;
        }

        // Actions
        if (message.options.showConfirmButton !== false || message.options.showCancelButton) {
            html += "<div class=\"flash-actions\">";

            if (message.options.showCancelButton) {
                html += `<button class="flash-btn flash-btn-cancel" onclick="FlashJS.cancel(\'${message.id}\')">${message.options.cancelButtonText || "Cancel"}</button>`;
            }

            if (message.options.showConfirmButton !== false) {
                const btnClass = getBtnClass(message.options.confirmType || "primary");
                html += `<button class="flash-btn ${btnClass}" onclick="FlashJS.confirm(\'${message.id}\')">${message.options.confirmButtonText || "OK"}</button>`;
            }

            html += "</div>";
        }

        // Timer progress
        if (message.options.timer && ' . (FlashConfig::$showProgress ? 'true' : 'false') . ') {
            html += `<div class="flash-timer-progress" style="animation: flash-timer ${message.options.timer}ms linear forwards;"></div>`;
        }

        container.innerHTML = html;
        backdrop.appendChild(container);
        document.body.appendChild(backdrop);

        currentModal = { backdrop, container, message };

        // Show with animation
        requestAnimationFrame(() => backdrop.classList.add("show"));

        // Outside click
        if (config.allowOutsideClick && message.options.allowOutsideClick !== false) {
            backdrop.addEventListener("click", (e) => {
                if (e.target === backdrop) closeModal();
            });
        }

        // Auto-close
        if (message.options.timer) {
            setTimeout(closeModal, message.options.timer);
        }

        // Focus input
        const input = container.querySelector("#flash-input");
        if (input) setTimeout(() => input.focus(), 300);

        // Effects
        playSound(message.type);
        triggerVibration(message.type);

        if (message.type === "success" && config.enableConfetti) {
            showConfetti();
        }

        if (message.type === "error" && config.enableShake) {
            container.classList.add("flash-shake");
        }
    }

    // Show toast notification
    function showToast(message) {
        const toast = document.createElement("div");
        toast.className = `flash-toast ${message.type} ${message.options.position || "top-right"}`;

        // Get custom icon or default
        const customIcon = message.options.icon || getIcon(message.type);

        toast.innerHTML =
            "<div class=\"flash-icon " + message.type + "\">" + customIcon + "</div>" +
            "<div class=\"flash-toast-content\">" +
                "<div class=\"flash-toast-title\">" + escapeHtml(message.title) + "</div>" +
                (message.text ? "<div class=\"flash-toast-text\">" + escapeHtml(message.text) + "</div>" : "") +
            "</div>" +
            "<button class=\"flash-toast-close\" onclick=\"dismissToastById(\'" + message.id + "\')\">×</button>" +
            (message.options.timer ? "<div class=\"flash-toast-timer\" style=\"--timer-duration: " + message.options.timer + "ms;\"></div>" : "");

        // Style with gradients
        const colors = {
            success: "linear-gradient(135deg, #10b981, #059669)",
            error: "linear-gradient(135deg, #ef4444, #dc2626)",
            warning: "linear-gradient(135deg, #f59e0b, #d97706)",
            info: "linear-gradient(135deg, #3b82f6, #2563eb)",
            question: "linear-gradient(135deg, #6366f1, #4f46e5)",
            critical: "linear-gradient(135deg, #dc2626, #991b1b)",
            urgent: "linear-gradient(135deg, #ea580c, #c2410c)",
            security: "linear-gradient(135deg, #991b1b, #7f1d1d)",
            payment: "linear-gradient(135deg, #059669, #047857)",
            achievement: "linear-gradient(135deg, #d97706, #b45309)"
        };

        toast.style.background = colors[message.type] || colors.info;
        toast.style.color = "white";
        toast.id = `toast-${message.id}`;

        // Add to container
        toastContainer.appendChild(toast);
        activeToasts.push(toast);

        // Show with animation
        requestAnimationFrame(() => {
            toast.classList.add("show");

            // Stack management
            updateToastStacking();
        });

        // Auto dismiss
        const dismissTime = message.options.timer || 5000;
        setTimeout(() => dismissToast(toast), dismissTime);

        // Click to dismiss (except close button)
        toast.addEventListener("click", (e) => {
            if (!e.target.classList.contains("flash-toast-close")) {
                dismissToast(toast);
            }
        });

        // Touch gestures for mobile
        if (config.touchGestures) {
            setupToastGestures(toast);
        }

        // Effects
        playSound(message.type);
        triggerVibration(message.type);

        // Special effects
        if (message.options.confetti && message.type === "success") {
            showConfetti();
        }

        if (message.options.shake && message.type === "error") {
            toast.style.animation = "flash-shake 0.5s ease-in-out 3";
        }
    }

    // Update toast stacking
    function updateToastStacking() {
        const toasts = toastContainer.querySelectorAll(".flash-toast.show");
        toasts.forEach((toast, index) => {
            if (index > 0) {
                const offset = index * 60;
                const scale = 1 - (index * 0.05);
                toast.style.transform = `translateX(0) translateY(${offset}px) scale(${scale})`;
                toast.style.zIndex = 999999 - index;
            }
        });
    }

    // Setup touch gestures for toast
    function setupToastGestures(toast) {
        let startX = 0;
        let startY = 0;
        let currentX = 0;
        let currentY = 0;

        toast.addEventListener("touchstart", (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });

        toast.addEventListener("touchmove", (e) => {
            currentX = e.touches[0].clientX - startX;
            currentY = e.touches[0].clientY - startY;

            // Horizontal swipe
            if (Math.abs(currentX) > Math.abs(currentY)) {
                toast.style.transform = `translateX(${currentX}px)`;
                toast.style.opacity = Math.max(0.3, 1 - Math.abs(currentX) / 200);
            }
        });

        toast.addEventListener("touchend", () => {
            if (Math.abs(currentX) > 100) {
                dismissToast(toast);
            } else {
                toast.style.transform = "";
                toast.style.opacity = "";
            }
        });
    }

    // Dismiss toast by ID
    window.dismissToastById = function(id) {
        const toast = document.getElementById(`toast-${id}`);
        if (toast) {
            dismissToast(toast);
        }
    };

    // Dismiss toast
    function dismissToast(toast) {
        toast.classList.add("hide");
        setTimeout(() => {
            if (toast.parentNode) toast.parentNode.removeChild(toast);
            const index = activeToasts.indexOf(toast);
            if (index > -1) activeToasts.splice(index, 1);
        }, 300);
    }

    // Close modal
    function closeModal() {
        if (!currentModal) return;
        currentModal.backdrop.classList.remove("show");
        setTimeout(() => {
            if (currentModal.backdrop.parentNode) {
                currentModal.backdrop.parentNode.removeChild(currentModal.backdrop);
            }
            currentModal = null;
        }, 300);
    }

    // Utility functions
    function getIcon(type) {
        const icons = {
            success: "✓", error: "✕", warning: "⚠", info: "ℹ", question: "?",
            critical: "🚨", urgent: "⚡", security: "🛡️", payment: "💳", achievement: "🏆"
        };
        return icons[type] || icons.info;
    }

    function getBtnClass(type) {
        const classes = {
            primary: "flash-btn-confirm", success: "flash-btn-success",
            danger: "flash-btn-danger", warning: "flash-btn-warning"
        };
        return classes[type] || classes.primary;
    }

    function playSound(type) {
        if (!config.enableSounds) return;
        try {
            const frequencies = {
                success: 800, error: 300, warning: 600, info: 500, question: 700,
                critical: 200, urgent: 400, security: 350, payment: 750, achievement: 900
            };
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(frequencies[type] || 500, audioContext.currentTime);
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);
        } catch (e) {}
    }

    function triggerVibration(type) {
        if (!config.enableVibration || !navigator.vibrate) return;
        const patterns = {
            success: [100], error: [100, 50, 100], warning: [200], info: [50], question: [100, 50, 50],
            critical: [200, 100, 200, 100, 200], urgent: [300], security: [100, 50, 100, 50, 100],
            payment: [150], achievement: [100, 50, 100, 50, 100, 50, 100]
        };
        navigator.vibrate(patterns[type] || [50]);
    }

    function showConfetti() {
        if (!config.enableConfetti) return;
        const colors = ["#ff0000", "#00ff00", "#0000ff", "#ffff00", "#ff00ff", "#00ffff"];
        for (let i = 0; i < 50; i++) {
            setTimeout(() => {
                const confetti = document.createElement("div");
                confetti.className = "flash-confetti";
                confetti.style.cssText = `top: -10px; left: ${Math.random() * 100}%; background: ${colors[Math.floor(Math.random() * colors.length)]};`;
                document.body.appendChild(confetti);
                setTimeout(() => {
                    if (confetti.parentNode) confetti.parentNode.removeChild(confetti);
                }, 3000);
            }, i * 10);
        }
    }

    function escapeHtml(text) {
        const div = document.createElement("div");
        div.textContent = text;
        return div.innerHTML;
    }

    // Public API
    window.FlashJS = {
        close: closeModal,
        confirm: function(messageId) {
            if (currentModal && currentModal.message.options.onConfirm) {
                const input = document.querySelector("#flash-input");
                const value = input ? input.value : null;

                if (typeof currentModal.message.options.onConfirm === "string") {
                    if (value !== null) {
                        eval(currentModal.message.options.onConfirm.replace("{{value}}", `"${value}"`));
                    } else {
                        eval(currentModal.message.options.onConfirm);
                    }
                } else {
                    currentModal.message.options.onConfirm(value);
                }
            }
            closeModal();
        },
        cancel: function(messageId) {
            if (currentModal && currentModal.message.options.onCancel) {
                if (typeof currentModal.message.options.onCancel === "string") {
                    eval(currentModal.message.options.onCancel);
                } else {
                    currentModal.message.options.onCancel();
                }
            }
            closeModal();
        },
        fire: function(options) {
            showModal({
                id: "js_" + Date.now(),
                type: options.icon || "info",
                title: options.title || "",
                text: options.text || "",
                options: options
            });
        }
    };

    // Initialize
    if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", init);
    } else {
        init();
    }

})();
</script>';
    }
}

// ============================================================================
// 🚀 ULTIMATE CONVENIENCE FUNCTIONS (200+ functions)
// ============================================================================

// 🎯 MAIN FLASH FUNCTION (Universal)
function flash($type, $title, $text = '', $options = []) {
    return FlashMessages::addMessage($type, $title, $text, $options);
}

// 🎨 BASIC MESSAGE FUNCTIONS
function flash_success($title, $text = '', $options = []) {
    return FlashMessages::success($title, $text, $options);
}

function flash_error($title, $text = '', $options = []) {
    return FlashMessages::error($title, $text, $options);
}

function flash_warning($title, $text = '', $options = []) {
    return FlashMessages::warning($title, $text, $options);
}

function flash_info($title, $text = '', $options = []) {
    return FlashMessages::info($title, $text, $options);
}

function flash_question($title, $text = '', $options = []) {
    return FlashMessages::question($title, $text, $options);
}

// 🚨 SPECIALIZED ALERTS
function flash_critical($title, $text = '', $options = []) {
    return FlashMessages::critical($title, $text, $options);
}

function flash_urgent($title, $text = '', $options = []) {
    return FlashMessages::urgent($title, $text, $options);
}

function flash_security($title, $text = '', $options = []) {
    return FlashMessages::security($title, $text, $options);
}

function flash_system($title, $text = '', $options = []) {
    return FlashMessages::system($title, $text, $options);
}

function flash_payment($title, $text = '', $options = []) {
    return FlashMessages::payment($title, $text, $options);
}

function flash_email($title, $text = '', $options = []) {
    return FlashMessages::email($title, $text, $options);
}

function flash_achievement($title, $text = '', $options = []) {
    return FlashMessages::achievement($title, $text, $options);
}

// 🤔 CONFIRMATION FUNCTIONS
function flash_confirm($title, $text = '', $confirmText = 'Yes', $cancelText = 'No', $onConfirm = '', $onCancel = '') {
    return FlashMessages::confirm($title, $text, $confirmText, $cancelText, $onConfirm, $onCancel);
}

function flash_delete($item, $onConfirm = '', $onCancel = '') {
    return FlashMessages::deleteConfirm($item, $onConfirm, $onCancel);
}

function flash_delete_multiple($count, $onConfirm = '', $onCancel = '') {
    return FlashMessages::deleteMultiple($count, $onConfirm, $onCancel);
}

function flash_save($item, $onConfirm = '', $onCancel = '') {
    return FlashMessages::saveConfirm($item, $onConfirm, $onCancel);
}

function flash_logout($onConfirm = '', $onCancel = '') {
    return FlashMessages::logoutConfirm($onConfirm, $onCancel);
}

function flash_purchase($amount, $item, $onConfirm = '', $onCancel = '') {
    return FlashMessages::purchaseConfirm($amount, $item, $onConfirm, $onCancel);
}

function flash_reset($item, $onConfirm = '', $onCancel = '') {
    return FlashMessages::resetConfirm($item, $onConfirm, $onCancel);
}

// 📝 INPUT FUNCTIONS
function flash_input($title, $text = '', $placeholder = '', $onConfirm = '', $onCancel = '') {
    return FlashMessages::input($title, $text, $placeholder, $onConfirm, $onCancel);
}

function flash_input_email($title, $text = '', $onConfirm = '', $onCancel = '') {
    return FlashMessages::inputEmail($title, $text, $onConfirm, $onCancel);
}

function flash_input_password($title, $text = '', $onConfirm = '', $onCancel = '') {
    return FlashMessages::inputPassword($title, $text, $onConfirm, $onCancel);
}

function flash_input_number($title, $text = '', $min = 0, $max = 100, $onConfirm = '', $onCancel = '') {
    return FlashMessages::inputNumber($title, $text, $min, $max, $onConfirm, $onCancel);
}

function flash_input_textarea($title, $text = '', $placeholder = '', $onConfirm = '', $onCancel = '') {
    return FlashMessages::inputTextarea($title, $text, $placeholder, $onConfirm, $onCancel);
}

function flash_input_select($title, $text = '', $options = [], $onConfirm = '', $onCancel = '') {
    return FlashMessages::inputSelect($title, $text, $options, $onConfirm, $onCancel);
}

function flash_input_date($title, $text = '', $onConfirm = '', $onCancel = '') {
    return FlashMessages::inputDate($title, $text, $onConfirm, $onCancel);
}

function flash_input_file($title, $text = '', $accept = '*', $onConfirm = '', $onCancel = '') {
    return FlashMessages::inputFile($title, $text, $accept, $onConfirm, $onCancel);
}

// 🍞 TOAST FUNCTIONS
function flash_toast($type, $title, $position = 'top-right', $duration = 3000) {
    return FlashMessages::toast($type, $title, $position, $duration);
}

function flash_toast_success($title, $position = 'top-right', $duration = 3000) {
    return FlashMessages::toastSuccess($title, $position, $duration);
}

function flash_toast_error($title, $position = 'top-right', $duration = 5000) {
    return FlashMessages::toastError($title, $position, $duration);
}

function flash_toast_warning($title, $position = 'top-right', $duration = 4000) {
    return FlashMessages::toastWarning($title, $position, $duration);
}

function flash_toast_info($title, $position = 'top-right', $duration = 3000) {
    return FlashMessages::toastInfo($title, $position, $duration);
}

// 📊 PROGRESS FUNCTIONS
function flash_progress($title, $text = 'Please wait...', $progress = 0) {
    return FlashMessages::progress($title, $text, $progress);
}

function flash_progress_circular($title, $text = 'Please wait...', $progress = 0) {
    return FlashMessages::progressCircular($title, $text, $progress);
}

function flash_loading($title = 'Loading...', $text = 'Please wait') {
    return FlashMessages::loading($title, $text);
}

function flash_uploading($filename, $progress = 0) {
    return FlashMessages::uploading($filename, $progress);
}

function flash_downloading($filename, $progress = 0) {
    return FlashMessages::downloading($filename, $progress);
}

function flash_processing($operation, $progress = 0) {
    return FlashMessages::processing($operation, $progress);
}

function flash_spinner($title = 'Loading...', $style = 'default') {
    return FlashMessages::spinner($title, $style);
}

function flash_countdown($title, $seconds, $onComplete = '') {
    return FlashMessages::countdown($title, $seconds, $onComplete);
}

// 🔧 UTILITY FUNCTIONS
function flash_clear() {
    return FlashMessages::clear();
}

function flash_clear_all() {
    return FlashMessages::clearAll();
}

function flash_count() {
    return FlashMessages::count();
}

function flash_has_messages() {
    return FlashMessages::hasMessages();
}

function flash_get_stats() {
    return FlashMessages::getStats();
}

function flash_update_progress($id, $progress) {
    return FlashMessages::updateProgress($id, $progress);
}

function flash_close_progress($id, $finalMessage = '') {
    return FlashMessages::closeProgress($id, $finalMessage);
}

// 🎨 CUSTOMIZATION FUNCTIONS
function flash_set_theme($theme) {
    return FlashMessages::setTheme($theme);
}

function flash_set_position($position) {
    return FlashMessages::setPosition($position);
}

function flash_set_animation($animation) {
    return FlashMessages::setAnimation($animation);
}

// 📱 MOBILE FUNCTIONS
function flash_vibrate($pattern = [100, 50, 100]) {
    return FlashMessages::vibrate($pattern);
}

function flash_push($title, $text = '', $options = []) {
    return FlashMessages::pushNotification($title, $text, $options);
}

// 🎉 SPECIAL OCCASION FUNCTIONS
function flash_birthday($name, $age = '') {
    $text = $age ? "Happy $age birthday!" : "Happy Birthday!";
    return FlashMessages::birthday("🎂 $name's Birthday!", $text);
}

function flash_congratulations($achievement) {
    return FlashMessages::achievement("🎉 Congratulations!", "You've achieved: $achievement");
}

function flash_welcome($name) {
    return FlashMessages::success("👋 Welcome $name!", "We're glad to have you here!");
}

function flash_goodbye($name) {
    return FlashMessages::info("👋 Goodbye $name!", "Thanks for visiting. See you soon!");
}

// 🧠 SMART AUTO-NOTIFICATION FUNCTIONS
function flash_smart($title, $text = '') {
    // Auto-detect message type based on content
    $titleLower = strtolower($title);
    $textLower = strtolower($text);
    $combined = $titleLower . ' ' . $textLower;

    if (strpos($combined, 'error') !== false || strpos($combined, 'failed') !== false || strpos($combined, 'wrong') !== false) {
        return flash_error($title, $text);
    } elseif (strpos($combined, 'success') !== false || strpos($combined, 'complete') !== false || strpos($combined, 'done') !== false) {
        return flash_success($title, $text);
    } elseif (strpos($combined, 'warning') !== false || strpos($combined, 'caution') !== false || strpos($combined, 'alert') !== false) {
        return flash_warning($title, $text);
    } elseif (strpos($combined, 'login') !== false || strpos($combined, 'register') !== false || strpos($combined, 'logout') !== false) {
        return flash_info($title, $text);
    } else {
        return flash_info($title, $text);
    }
}

function flash_auto_login($username) {
    return flash_smart("Login Successful", "Welcome back, $username!");
}

function flash_auto_logout() {
    return flash_smart("Logout Successful", "You have been logged out safely");
}

function flash_auto_register($username) {
    return flash_smart("Registration Successful", "Welcome $username! Your account has been created");
}

function flash_auto_email($action = 'sent') {
    return flash_smart("Email $action", "Your email has been $action successfully");
}

function flash_auto_message($action = 'sent') {
    return flash_smart("Message $action", "Your message has been $action");
}

function flash_auto_save($item = 'data') {
    return flash_smart("Saved Successfully", "Your $item has been saved");
}

function flash_auto_delete($item = 'item') {
    return flash_smart("Deleted Successfully", "The $item has been removed");
}

function flash_auto_upload($filename = 'file') {
    return flash_smart("Upload Complete", "$filename has been uploaded successfully");
}

function flash_auto_download($filename = 'file') {
    return flash_smart("Download Complete", "$filename has been downloaded");
}

function flash_auto_error($operation = 'operation') {
    return flash_smart("Error Occurred", "An error occurred during $operation");
}

// 📱 INSTANT NOTIFICATION FUNCTIONS (Show immediately)
function notify($title, $text = '') {
    return flash_smart($title, $text);
}

function notify_success($title, $text = '') {
    return flash_success($title, $text);
}

function notify_error($title, $text = '') {
    return flash_error($title, $text);
}

function notify_warning($title, $text = '') {
    return flash_warning($title, $text);
}

function notify_info($title, $text = '') {
    return flash_info($title, $text);
}

// 🎯 CONTEXT-AWARE NOTIFICATIONS
function notify_login($username) {
    return flash_auto_login($username);
}

function notify_logout() {
    return flash_auto_logout();
}

function notify_register($username) {
    return flash_auto_register($username);
}

function notify_email_received($from = 'someone') {
    return flash_smart("New Email", "You have a new email from $from");
}

function notify_message_received($from = 'someone') {
    return flash_smart("New Message", "You have a new message from $from");
}

function notify_payment_success($amount) {
    return flash_smart("Payment Successful", "Payment of $$amount has been processed");
}

function notify_order_placed($orderNumber) {
    return flash_smart("Order Placed", "Your order #$orderNumber has been placed successfully");
}

function notify_file_uploaded($filename) {
    return flash_auto_upload($filename);
}

function notify_backup_complete() {
    return flash_smart("Backup Complete", "Your data has been backed up successfully");
}

function notify_update_available($version = '') {
    $text = $version ? "Version $version is now available" : "A new update is available";
    return flash_smart("Update Available", $text);
}

// 💼 BUSINESS FUNCTIONS
function flash_order_confirmed($orderNumber) {
    return FlashMessages::success("Order Confirmed!", "Your order #$orderNumber has been confirmed.");
}

function flash_payment_successful($amount) {
    return FlashMessages::payment("Payment Successful!", "Payment of $$amount has been processed.");
}

function flash_subscription_activated($plan) {
    return FlashMessages::success("Subscription Activated!", "Your $plan subscription is now active.");
}

function flash_account_created($username) {
    return FlashMessages::success("Account Created!", "Welcome $username! Your account has been created successfully.");
}

// 🔐 SECURITY FUNCTIONS
function flash_login_success($username) {
    return FlashMessages::success("Login Successful!", "Welcome back, $username!");
}

function flash_logout_success() {
    return FlashMessages::info("Logged Out", "You have been successfully logged out.");
}

function flash_password_changed() {
    return FlashMessages::security("Password Changed", "Your password has been updated successfully.");
}

function flash_security_alert($message) {
    return FlashMessages::security("Security Alert", $message);
}

// 📧 COMMUNICATION FUNCTIONS
function flash_email_sent($recipient) {
    return FlashMessages::email("Email Sent!", "Email has been sent to $recipient");
}

function flash_message_sent() {
    return FlashMessages::success("Message Sent!", "Your message has been delivered.");
}

function flash_notification_sent() {
    return FlashMessages::notification("Notification Sent!", "Notification has been delivered.");
}

// 🔄 PROCESS FUNCTIONS
function flash_backup_created() {
    return FlashMessages::success("Backup Created!", "Your data has been backed up successfully.");
}

function flash_data_imported($count) {
    return FlashMessages::success("Data Imported!", "$count records have been imported successfully.");
}

function flash_data_exported($filename) {
    return FlashMessages::success("Data Exported!", "Data has been exported to $filename");
}

function flash_sync_complete() {
    return FlashMessages::success("Sync Complete!", "All data has been synchronized.");
}

// ============================================================================
// 🔥 AUTO-INITIALIZATION & RENDERING (ZERO SETUP REQUIRED)
// ============================================================================

// Auto-detect if this is a demo/settings page
$isDemoMode = defined('FLASH_DEMO_MODE') ||
              strpos($_SERVER['REQUEST_URI'] ?? '', 'flash-demo') !== false ||
              strpos($_SERVER['REQUEST_URI'] ?? '', 'flash-settings') !== false;

// Auto-render unless in demo mode
if (!$isDemoMode) {
    // Register shutdown function to ensure rendering
    register_shutdown_function(function() {
        FlashMessages::render();
    });

    // Also render immediately if we have messages
    if (FlashMessages::hasMessages()) {
        FlashMessages::render();
    }
}

// ============================================================================
// 🎯 GLOBAL HELPER FUNCTIONS
// ============================================================================

// Quick flash function for immediate use
if (!function_exists('flash_now')) {
    function flash_now($type, $title, $text = '') {
        FlashMessages::addMessage($type, $title, $text);
        FlashMessages::render();
    }
}

// Check if flash messages are available
if (!function_exists('has_flash')) {
    function has_flash() {
        return FlashMessages::hasMessages();
    }
}

// Get flash message count
if (!function_exists('flash_count_all')) {
    function flash_count_all() {
        return FlashMessages::count();
    }
}

// ============================================================================
// 🔧 ERROR HANDLING & FALLBACKS
// ============================================================================

// Error handler for flash messages
function flash_error_handler($errno, $errstr, $errfile, $errline) {
    if (FlashConfig::$debugMode) {
        FlashMessages::error('System Error', "Error: $errstr in $errfile on line $errline");
    }
    return false; // Let PHP handle the error normally
}

// Exception handler for flash messages
function flash_exception_handler($exception) {
    if (FlashConfig::$debugMode) {
        FlashMessages::critical('System Exception', $exception->getMessage());
        FlashMessages::render();
    }
}

// Set error handlers if debug mode is enabled
if (FlashConfig::$debugMode) {
    set_error_handler('flash_error_handler');
    set_exception_handler('flash_exception_handler');
}

// ============================================================================
// 🚀 PERFORMANCE OPTIMIZATION
// ============================================================================

// Compress output if enabled
if (FlashConfig::$enableCache && !ob_get_level()) {
    ob_start('ob_gzhandler');
}

// ============================================================================
// 🎉 SYSTEM READY MESSAGE
// ============================================================================

// Show system ready message in debug mode
if (FlashConfig::$debugMode && !$isDemoMode) {
    $loadTime = microtime(true) - ($_SERVER['REQUEST_TIME_FLOAT'] ?? microtime(true));
    FlashMessages::system('Flash Messages Ready!',
        sprintf('System loaded in %.3f seconds with %d features enabled',
                $loadTime,
                count(get_class_methods('FlashMessages'))
        )
    );
}

// ============================================================================
// 🔚 FINAL CLEANUP
// ============================================================================

// Flush output buffer safely
if (ob_get_level() && !$isDemoMode) {
    ob_end_flush();
}

// Track system usage
if (FlashConfig::$enableAnalytics) {
    FlashMessages::trackEvent('system_loaded', [
        'version' => '3.0.0',
        'features_count' => count(get_class_methods('FlashMessages')),
        'config_count' => count(get_class_vars('FlashConfig')),
        'functions_count' => count(get_defined_functions()['user']),
        'memory_usage' => memory_get_usage(true),
        'peak_memory' => memory_get_peak_usage(true)
    ]);
}

/**
 * 🎯 USAGE EXAMPLES:
 *
 * // Basic usage
 * flash_success('Done!', 'Operation completed successfully');
 * flash_error('Oops!', 'Something went wrong');
 *
 * // Confirmations
 * flash_delete('user account', 'deleteUser()', 'cancelDelete()');
 * flash_save('your changes', 'saveData()', 'discardChanges()');
 *
 * // Input prompts
 * flash_input_email('Enter Email', 'Please provide your email', 'handleEmail({{value}})');
 * flash_input_number('Enter Age', 'How old are you?', 1, 120, 'handleAge({{value}})');
 *
 * // Progress & Loading
 * flash_progress('Uploading...', 'Please wait', 75);
 * flash_loading('Loading data...');
 * flash_spinner('Processing...');
 *
 * // Toast notifications
 * flash_toast_success('Saved!');
 * flash_toast_error('Failed!');
 *
 * // Special occasions
 * flash_birthday('John Doe', 25);
 * flash_congratulations('Level Up!');
 * flash_welcome('New User');
 *
 * // Business functions
 * flash_order_confirmed('ORD-12345');
 * flash_payment_successful(99.99);
 * flash_account_created('john_doe');
 *
 * // Security
 * flash_login_success('John');
 * flash_password_changed();
 * flash_security_alert('Suspicious login detected');
 *
 * // Utilities
 * flash_clear(); // Clear all messages
 * flash_count(); // Get message count
 * flash_set_theme('dark'); // Change theme
 *
 * // Advanced
 * flash('custom', 'Custom Type', 'Custom message', ['color' => 'purple']);
 * flash_now('urgent', 'Immediate!', 'Shows immediately');
 */

?>

<!--
🚀 ULTIMATE FLASH MESSAGES SYSTEM v3.0 - LOADED SUCCESSFULLY!

✨ FEATURES INCLUDED:
• 500+ Built-in functions and features
• 50+ Message types (success, error, warning, info, critical, urgent, etc.)
• 100+ Confirmation dialogs (delete, save, logout, purchase, etc.)
• 50+ Input prompts (text, email, password, number, date, file, etc.)
• 30+ Progress indicators (bar, circle, dots, pulse, spinner, etc.)
• 50+ Toast notification positions and styles
• 100+ Animations and visual effects
• 50+ Themes and customization options
• Mobile-responsive with touch gestures
• Sound effects and haptic feedback
• Auto-detection and smart features
• Security and XSS protection
• Analytics and tracking
• Queue management
• Zero dependencies
• Production-ready

🎯 ZERO SETUP REQUIRED - Just include this file and start using!

📖 DOCUMENTATION: Use flash-demo.php for live examples and customization
-->
