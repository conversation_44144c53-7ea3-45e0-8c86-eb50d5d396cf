<?php
/**
 * Component Helper Functions
 * Dynamic component loading system for JobSpace
 * 
 * Usage Examples:
 * 
 * // Load alert component
 * include_component('alerts/success', [
 *     'message' => 'User created successfully!',
 *     'title' => 'Success',
 *     'autoHide' => true
 * ]);
 * 
 * // Load button component
 * include_component('buttons/primary', [
 *     'text' => 'Save Changes',
 *     'type' => 'submit',
 *     'size' => 'lg',
 *     'loading' => false
 * ]);
 * 
 * // Load user card component
 * include_component('cards/user-card', [
 *     'user' => $userData,
 *     'size' => 'md',
 *     'showActions' => true,
 *     'layout' => 'horizontal'
 * ]);
 */

/**
 * Include a component with data
 * 
 * @param string $component Component path (e.g., 'alerts/success', 'buttons/primary')
 * @param array $data Data to pass to the component
 * @param bool $return Whether to return the output instead of echoing
 * @return string|void
 */
function include_component($component, $data = [], $return = false) {
    $componentPath = __DIR__ . '/' . $component . '.php';
    
    if (!file_exists($componentPath)) {
        $error = "Component not found: {$component}";
        if ($return) {
            return "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded'>{$error}</div>";
        } else {
            echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded'>{$error}</div>";
            return;
        }
    }
    
    if ($return) {
        ob_start();
        include $componentPath;
        return ob_get_clean();
    } else {
        include $componentPath;
    }
}

/**
 * Render multiple components
 * 
 * @param array $components Array of components with their data
 * @param bool $return Whether to return the output instead of echoing
 * @return string|void
 */
function render_components($components, $return = false) {
    $output = '';
    
    foreach ($components as $component) {
        $componentName = $component['component'] ?? '';
        $componentData = $component['data'] ?? [];
        
        if ($componentName) {
            if ($return) {
                $output .= include_component($componentName, $componentData, true);
            } else {
                include_component($componentName, $componentData);
            }
        }
    }
    
    if ($return) {
        return $output;
    }
}

/**
 * Load alert component
 * 
 * @param string $type Alert type: success, error, warning, info
 * @param string $message Alert message
 * @param array $options Additional options
 */
function show_alert($type, $message, $options = []) {
    $data = array_merge([
        'message' => $message
    ], $options);
    
    include_component("alerts/{$type}", $data);
}

/**
 * Load button component
 * 
 * @param string $type Button type: primary, secondary, danger, success
 * @param string $text Button text
 * @param array $options Additional options
 */
function render_button($type, $text, $options = []) {
    $data = array_merge([
        'text' => $text
    ], $options);
    
    return include_component("buttons/{$type}", $data, true);
}

/**
 * Load card component
 * 
 * @param string $type Card type: user-card, role-card, stats-card, etc.
 * @param array $data Card data
 * @param array $options Additional options
 */
function render_card($type, $data, $options = []) {
    $componentData = array_merge($data, $options);
    return include_component("cards/{$type}", $componentData, true);
}

/**
 * Load form field component
 * 
 * @param string $type Field type: input-field, select-field, textarea-field, etc.
 * @param array $data Field data
 */
function render_form_field($type, $data) {
    include_component("forms/{$type}", $data);
}

/**
 * Load feed item component
 * 
 * @param array $item Feed item data
 * @param string $type Feed type: quiz, social, ecommerce, freelance
 * @param array $options Additional options
 */
function render_feed_item($item, $type, $options = []) {
    $data = array_merge([
        'item' => $item,
        'type' => $type
    ], $options);
    
    include_component('feed/feed-item', $data);
}

/**
 * Load modal component
 * 
 * @param string $type Modal type: confirm-modal, user-modal, etc.
 * @param array $data Modal data
 */
function render_modal($type, $data) {
    include_component("modals/{$type}", $data);
}

/**
 * Load table component
 * 
 * @param array $data Table data
 * @param array $options Table options
 */
function render_table($data, $options = []) {
    $componentData = array_merge([
        'data' => $data
    ], $options);
    
    include_component('tables/data-table', $componentData);
}

/**
 * Load pagination component
 * 
 * @param int $currentPage Current page number
 * @param int $totalPages Total number of pages
 * @param string $baseUrl Base URL for pagination links
 * @param array $options Additional options
 */
function render_pagination($currentPage, $totalPages, $baseUrl, $options = []) {
    $data = array_merge([
        'currentPage' => $currentPage,
        'totalPages' => $totalPages,
        'baseUrl' => $baseUrl
    ], $options);
    
    include_component('tables/pagination', $data);
}

/**
 * Load loading spinner component
 * 
 * @param string $size Spinner size: sm, md, lg
 * @param string $color Spinner color
 */
function render_loading($size = 'md', $color = 'blue') {
    include_component('utils/loading-spinner', [
        'size' => $size,
        'color' => $color
    ]);
}

/**
 * Load empty state component
 * 
 * @param string $message Empty state message
 * @param string $icon Empty state icon
 * @param array $actions Empty state actions
 */
function render_empty_state($message, $icon = '', $actions = []) {
    include_component('utils/empty-state', [
        'message' => $message,
        'icon' => $icon,
        'actions' => $actions
    ]);
}

/**
 * Check if component exists
 * 
 * @param string $component Component path
 * @return bool
 */
function component_exists($component) {
    $componentPath = __DIR__ . '/' . $component . '.php';
    return file_exists($componentPath);
}

/**
 * Get list of available components
 * 
 * @param string $category Component category (optional)
 * @return array
 */
function get_available_components($category = '') {
    $componentsDir = __DIR__;
    $components = [];
    
    if ($category) {
        $categoryDir = $componentsDir . '/' . $category;
        if (is_dir($categoryDir)) {
            $files = glob($categoryDir . '/*.php');
            foreach ($files as $file) {
                $components[] = $category . '/' . basename($file, '.php');
            }
        }
    } else {
        $categories = glob($componentsDir . '/*', GLOB_ONLYDIR);
        foreach ($categories as $categoryPath) {
            $categoryName = basename($categoryPath);
            $files = glob($categoryPath . '/*.php');
            foreach ($files as $file) {
                if (basename($file) !== 'component-helper.php') {
                    $components[] = $categoryName . '/' . basename($file, '.php');
                }
            }
        }
    }
    
    return $components;
}

/**
 * Render component with error handling
 * 
 * @param string $component Component path
 * @param array $data Component data
 * @param bool $showErrors Whether to show errors
 * @return string|void
 */
function safe_include_component($component, $data = [], $showErrors = true) {
    try {
        return include_component($component, $data, true);
    } catch (Exception $e) {
        if ($showErrors) {
            return "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded'>
                        <strong>Component Error:</strong> {$e->getMessage()}
                    </div>";
        }
        return '';
    }
}

// Auto-load this helper in global scope
if (!function_exists('component')) {
    /**
     * Global component function - shorthand for include_component
     *
     * @param string $component Component path
     * @param array $data Component data
     * @param bool $return Whether to return output
     * @return string|void
     */
    function component($component, $data = [], $return = false) {
        return include_component($component, $data, $return);
    }
}

/**
 * Auto-include flash messages helper
 */
if (!function_exists('flash_success')) {
    require_once __DIR__ . '/../../app/helpers/FlashHelper.php';
}

/**
 * Auto-display flash messages function
 * Call this in your layout files to automatically show flash messages
 */
function auto_display_flash_messages() {
    try {
        if (function_exists('has_flash_messages') && has_flash_messages()) {
            include_component('utils/flash-messages');
        }
    } catch (Exception $e) {
        // Emergency fallback
        echo "<script>
            document.addEventListener('DOMContentLoaded', function() {
                if (typeof showEmergencyMessage === 'function') {
                    showEmergencyMessage('Flash messages system failed to load');
                } else {
                    alert('Notification system error: Flash messages failed to load');
                }
            });
        </script>";
    }
}

/**
 * Initialize flash messages system
 * Call this once in your main layout or bootstrap file
 */
function init_flash_messages() {
    try {
        // Start session if not already started
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Auto-display flash messages
        auto_display_flash_messages();
    } catch (Exception $e) {
        // Emergency fallback
        echo "<div style='position:fixed;top:20px;right:20px;background:#dc2626;color:white;padding:15px;border-radius:8px;z-index:999999;max-width:300px;box-shadow:0 4px 6px rgba(0,0,0,0.3);'>
            <strong>⚠️ System Error:</strong><br>Flash messages system failed to initialize
            <button onclick='this.parentElement.remove()' style='float:right;background:none;border:none;color:white;font-size:18px;cursor:pointer;margin-top:-5px;'>&times;</button>
        </div>";
    }
}

/**
 * Emergency alert function (when everything else fails)
 */
function emergency_alert($message) {
    echo "<script>
        document.addEventListener('DOMContentLoaded', function() {
            try {
                if (typeof showEmergencyMessage === 'function') {
                    showEmergencyMessage('" . addslashes($message) . "');
                } else {
                    alert('System Alert: " . addslashes($message) . "');
                }
            } catch (error) {
                alert('Critical Error: " . addslashes($message) . "');
            }
        });
    </script>";
}

/**
 * Safe component include with error handling
 */
function safe_include_component_with_flash($component, $data = [], $return = false) {
    try {
        return include_component($component, $data, $return);
    } catch (Exception $e) {
        $errorMsg = "Component '{$component}' failed to load";

        if ($return) {
            return "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded'>
                        <strong>Component Error:</strong> {$errorMsg}
                    </div>";
        } else {
            emergency_alert($errorMsg);
        }
    }
}
?>
