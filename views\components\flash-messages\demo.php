<?php
/**
 * 🚀 ULTIMATE FLASH MESSAGES DEMO
 * Live demonstration and testing interface
 * 
 * ⭐ FEATURES DEMONSTRATED:
 * - 500+ notification functions
 * - Intelligent auto-detection
 * - Beautiful themes and animations
 * - Special effects and celebrations
 * - Mobile responsiveness
 * - Accessibility features
 * - Performance optimization
 * 
 * @version 3.0.0
 * @license Commercial/MIT Dual License
 * <AUTHOR> Development Team
 */

// Include the flash messages system
require_once __DIR__ . '/setup.php';
require_once __DIR__ . '/flash.php';

// Handle demo actions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'basic_success':
            flash_success('This is a success message!', 'Success!');
            break;
            
        case 'basic_error':
            flash_error('This is an error message!', 'Error!');
            break;
            
        case 'basic_warning':
            flash_warning('This is a warning message!', 'Warning!');
            break;
            
        case 'basic_info':
            flash_info('This is an info message!', 'Information');
            break;
            
        case 'success_confetti':
            flash_success_confetti('Congratulations! You did it!', 'Amazing!');
            break;
            
        case 'error_shake':
            flash_error_shake('Something went wrong!', 'Critical Error!');
            break;
            
        case 'warning_pulse':
            flash_warning_pulse('Please pay attention to this!', 'Important!');
            break;
            
        case 'info_glow':
            flash_info_glow('This is a special announcement!', 'Special Notice');
            break;
            
        case 'login_demo':
            flash_login_success('John Doe');
            break;
            
        case 'payment_demo':
            flash_payment_success('$299.99');
            break;
            
        case 'quiz_demo':
            flash_quiz_completed(95);
            break;
            
        case 'achievement_demo':
            flash_achievement_unlocked('Master Learner');
            break;
            
        case 'birthday_demo':
            flash_birthday_wishes('Sarah');
            break;
            
        case 'bulk_demo':
            flash_multiple([
                ['type' => 'success', 'message' => 'Profile updated!', 'title' => 'Success'],
                ['type' => 'info', 'message' => 'Check your email', 'title' => 'Info'],
                ['type' => 'warning', 'message' => 'Verify your phone', 'title' => 'Action Required']
            ]);
            break;
            
        case 'smart_demo':
            flash_smart('Login successful! Welcome back user!');
            flash_smart('Error occurred while processing your request');
            flash_smart('File uploaded successfully to your account');
            break;
            
        case 'position_demo':
            flash_top_left('info', 'Top Left Message', 'Position Demo');
            flash_top_center('success', 'Top Center Message', 'Position Demo');
            flash_bottom_right('warning', 'Bottom Right Message', 'Position Demo');
            break;
            
        case 'theme_demo':
            flash_glassmorphism('success', 'Glassmorphism theme!', 'Beautiful!');
            flash_neon('info', 'Neon theme activated!', 'Cyberpunk!');
            flash_minimal('warning', 'Minimal clean design', 'Simple');
            break;
            
        case 'special_effects':
            flash_fireworks('You are amazing!', 'Celebration Time!');
            break;
            
        case 'clear_all':
            flash_clear_all();
            break;
    }
    
    // Redirect to prevent form resubmission
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Ultimate Flash Messages Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .demo-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            color: white;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .demo-button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 12px 24px;
            border-radius: 10px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            margin: 5px;
        }
        
        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .demo-section {
            margin-bottom: 3rem;
        }
        
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            font-family: 'Inter', system-ui, sans-serif;
        }
        
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0;
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .stat-card {
            background: rgba(255,255,255,0.1);
            padding: 1.5rem;
            border-radius: 15px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .code-example {
            background: #1a1a1a;
            color: #00ff00;
            padding: 1rem;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            margin: 1rem 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>

    <!-- Hero Section -->
    <div class="hero-section">
        <div class="container mx-auto px-4">
            <h1 class="text-5xl font-bold mb-4">🚀 Ultimate Flash Messages</h1>
            <p class="text-xl mb-6">The World's Most Powerful Notification System</p>
            <div class="stats-grid max-w-4xl mx-auto">
                <div class="stat-card">
                    <div class="text-3xl font-bold">500+</div>
                    <div class="text-sm">Functions</div>
                </div>
                <div class="stat-card">
                    <div class="text-3xl font-bold">20+</div>
                    <div class="text-sm">Themes</div>
                </div>
                <div class="stat-card">
                    <div class="text-3xl font-bold">15+</div>
                    <div class="text-sm">Animations</div>
                </div>
                <div class="stat-card">
                    <div class="text-3xl font-bold">100%</div>
                    <div class="text-sm">Responsive</div>
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-4 pb-8">
        
        <!-- Basic Messages Demo -->
        <div class="demo-section">
            <div class="feature-card">
                <h2 class="text-3xl font-bold text-gray-800 mb-4">🎯 Basic Messages</h2>
                <p class="text-gray-600 mb-6">Test the four core message types with beautiful styling and animations.</p>
                
                <form method="POST" class="inline">
                    <input type="hidden" name="action" value="basic_success">
                    <button type="submit" class="demo-button">✅ Success Message</button>
                </form>
                
                <form method="POST" class="inline">
                    <input type="hidden" name="action" value="basic_error">
                    <button type="submit" class="demo-button">❌ Error Message</button>
                </form>
                
                <form method="POST" class="inline">
                    <input type="hidden" name="action" value="basic_warning">
                    <button type="submit" class="demo-button">⚠️ Warning Message</button>
                </form>
                
                <form method="POST" class="inline">
                    <input type="hidden" name="action" value="basic_info">
                    <button type="submit" class="demo-button">ℹ️ Info Message</button>
                </form>
                
                <div class="code-example">
// Basic usage examples<br>
flash_success('Operation completed!', 'Success!');<br>
flash_error('Something went wrong!', 'Error!');<br>
flash_warning('Please check this!', 'Warning!');<br>
flash_info('Here is some information', 'Info');
                </div>
            </div>
        </div>

        <!-- Special Effects Demo -->
        <div class="demo-section">
            <div class="feature-card">
                <h2 class="text-3xl font-bold text-gray-800 mb-4">🎊 Special Effects</h2>
                <p class="text-gray-600 mb-6">Experience stunning visual effects that make your notifications unforgettable.</p>
                
                <form method="POST" class="inline">
                    <input type="hidden" name="action" value="success_confetti">
                    <button type="submit" class="demo-button">🎉 Confetti Success</button>
                </form>
                
                <form method="POST" class="inline">
                    <input type="hidden" name="action" value="error_shake">
                    <button type="submit" class="demo-button">📳 Shake Error</button>
                </form>
                
                <form method="POST" class="inline">
                    <input type="hidden" name="action" value="warning_pulse">
                    <button type="submit" class="demo-button">💓 Pulse Warning</button>
                </form>
                
                <form method="POST" class="inline">
                    <input type="hidden" name="action" value="info_glow">
                    <button type="submit" class="demo-button">✨ Glow Info</button>
                </form>
                
                <form method="POST" class="inline">
                    <input type="hidden" name="action" value="special_effects">
                    <button type="submit" class="demo-button">🎆 Fireworks</button>
                </form>
                
                <div class="code-example">
// Special effects examples<br>
flash_success_confetti('Achievement unlocked!');<br>
flash_error_shake('Critical system error!');<br>
flash_warning_pulse('Urgent attention required!');<br>
flash_fireworks('You are amazing!', 'Celebration!');
                </div>
            </div>
        </div>

        <!-- Context-Aware Messages Demo -->
        <div class="demo-section">
            <div class="feature-card">
                <h2 class="text-3xl font-bold text-gray-800 mb-4">🧠 Smart Context-Aware Messages</h2>
                <p class="text-gray-600 mb-6">Intelligent auto-detection that adapts to your content and context.</p>

                <form method="POST" class="inline">
                    <input type="hidden" name="action" value="login_demo">
                    <button type="submit" class="demo-button">🔐 Login Success</button>
                </form>

                <form method="POST" class="inline">
                    <input type="hidden" name="action" value="payment_demo">
                    <button type="submit" class="demo-button">💳 Payment Success</button>
                </form>

                <form method="POST" class="inline">
                    <input type="hidden" name="action" value="quiz_demo">
                    <button type="submit" class="demo-button">🎓 Quiz Complete</button>
                </form>

                <form method="POST" class="inline">
                    <input type="hidden" name="action" value="achievement_demo">
                    <button type="submit" class="demo-button">🏆 Achievement</button>
                </form>

                <form method="POST" class="inline">
                    <input type="hidden" name="action" value="birthday_demo">
                    <button type="submit" class="demo-button">🎂 Birthday</button>
                </form>

                <div class="code-example">
// Context-aware examples<br>
flash_login_success('John Doe');<br>
flash_payment_success('$299.99');<br>
flash_quiz_completed(95);<br>
flash_achievement_unlocked('Master Learner');<br>
flash_birthday_wishes('Sarah');
                </div>
            </div>
        </div>

        <!-- Smart Auto-Detection Demo -->
        <div class="demo-section">
            <div class="feature-card">
                <h2 class="text-3xl font-bold text-gray-800 mb-4">🤖 Intelligent Auto-Detection</h2>
                <p class="text-gray-600 mb-6">Let the system automatically detect the best message type and styling.</p>

                <form method="POST" class="inline">
                    <input type="hidden" name="action" value="smart_demo">
                    <button type="submit" class="demo-button">🧠 Smart Detection</button>
                </form>

                <div class="code-example">
// Smart auto-detection examples<br>
flash_smart('Login successful! Welcome back user!');<br>
flash_smart('Error occurred while processing request');<br>
flash_smart('File uploaded successfully to account');<br>
flash_smart('Payment of $99.99 has been processed');
                </div>
            </div>
        </div>

        <!-- Positioning Demo -->
        <div class="demo-section">
            <div class="feature-card">
                <h2 class="text-3xl font-bold text-gray-800 mb-4">📍 Smart Positioning System</h2>
                <p class="text-gray-600 mb-6">20+ positions with intelligent auto-adjustment for optimal visibility.</p>

                <form method="POST" class="inline">
                    <input type="hidden" name="action" value="position_demo">
                    <button type="submit" class="demo-button">📍 Position Demo</button>
                </form>

                <div class="code-example">
// Positioning examples<br>
flash_top_left('info', 'Top Left Message');<br>
flash_top_center('success', 'Top Center Message');<br>
flash_bottom_right('warning', 'Bottom Right Message');<br>
flash_center('error', 'Center Message');
                </div>
            </div>
        </div>

        <!-- Theme Demo -->
        <div class="demo-section">
            <div class="feature-card">
                <h2 class="text-3xl font-bold text-gray-800 mb-4">🎨 Beautiful Themes</h2>
                <p class="text-gray-600 mb-6">Multiple stunning themes including glassmorphism, neon, and minimal designs.</p>

                <form method="POST" class="inline">
                    <input type="hidden" name="action" value="theme_demo">
                    <button type="submit" class="demo-button">🎨 Theme Showcase</button>
                </form>

                <div class="code-example">
// Theme examples<br>
flash_glassmorphism('success', 'Glassmorphism theme!');<br>
flash_neon('info', 'Neon theme activated!');<br>
flash_minimal('warning', 'Minimal clean design');<br>
flash_gradient('error', 'Beautiful gradient theme');
                </div>
            </div>
        </div>

        <!-- Bulk Operations Demo -->
        <div class="demo-section">
            <div class="feature-card">
                <h2 class="text-3xl font-bold text-gray-800 mb-4">🔄 Bulk Operations</h2>
                <p class="text-gray-600 mb-6">Send multiple messages at once with intelligent stacking and timing.</p>

                <form method="POST" class="inline">
                    <input type="hidden" name="action" value="bulk_demo">
                    <button type="submit" class="demo-button">📦 Bulk Messages</button>
                </form>

                <div class="code-example">
// Bulk operations examples<br>
flash_multiple([<br>
&nbsp;&nbsp;['type' => 'success', 'message' => 'Profile updated!'],<br>
&nbsp;&nbsp;['type' => 'info', 'message' => 'Check your email'],<br>
&nbsp;&nbsp;['type' => 'warning', 'message' => 'Verify phone']<br>
]);
                </div>
            </div>
        </div>

        <!-- Features Overview -->
        <div class="demo-section">
            <div class="feature-card">
                <h2 class="text-3xl font-bold text-gray-800 mb-4">⭐ Key Features</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6">
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h3 class="font-bold text-blue-800">🎯 500+ Functions</h3>
                        <p class="text-blue-600 text-sm">Every possible notification scenario covered</p>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg">
                        <h3 class="font-bold text-green-800">🧠 AI-Powered</h3>
                        <p class="text-green-600 text-sm">Intelligent auto-detection and optimization</p>
                    </div>
                    <div class="bg-purple-50 p-4 rounded-lg">
                        <h3 class="font-bold text-purple-800">🎨 Ultra-Beautiful</h3>
                        <p class="text-purple-600 text-sm">Professional design with stunning effects</p>
                    </div>
                    <div class="bg-red-50 p-4 rounded-lg">
                        <h3 class="font-bold text-red-800">🛡️ Enterprise Security</h3>
                        <p class="text-red-600 text-sm">XSS protection and content sanitization</p>
                    </div>
                    <div class="bg-yellow-50 p-4 rounded-lg">
                        <h3 class="font-bold text-yellow-800">📱 Mobile-First</h3>
                        <p class="text-yellow-600 text-sm">Perfect on all devices with touch support</p>
                    </div>
                    <div class="bg-indigo-50 p-4 rounded-lg">
                        <h3 class="font-bold text-indigo-800">⚡ High Performance</h3>
                        <p class="text-indigo-600 text-sm">Optimized for speed and efficiency</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Usage Instructions -->
        <div class="demo-section">
            <div class="feature-card">
                <h2 class="text-3xl font-bold text-gray-800 mb-4">🚀 Quick Start</h2>
                <div class="bg-gray-50 p-6 rounded-lg">
                    <h3 class="font-bold text-lg mb-3">1. Include the System (One Line!)</h3>
                    <div class="code-example">
require_once 'views/components/flash-messages/flash.php';
                    </div>

                    <h3 class="font-bold text-lg mb-3 mt-6">2. Use Any Function</h3>
                    <div class="code-example">
flash_success('Welcome to JobSpace!', 'Success!');<br>
flash_error('Invalid credentials!', 'Login Failed');<br>
flash_payment_success('$299.99');<br>
flash_achievement_unlocked('Master Learner');
                    </div>

                    <h3 class="font-bold text-lg mb-3 mt-6">3. Render Messages</h3>
                    <div class="code-example">
render_flash_messages(); // Outputs HTML, CSS, and JavaScript
                    </div>
                </div>
            </div>
        </div>

        <!-- Control Panel -->
        <div class="demo-section">
            <div class="feature-card">
                <h2 class="text-3xl font-bold text-gray-800 mb-4">🎛️ Control Panel</h2>
                <p class="text-gray-600 mb-6">Manage your notification system.</p>

                <form method="POST" class="inline">
                    <input type="hidden" name="action" value="clear_all">
                    <button type="submit" class="demo-button bg-red-500 hover:bg-red-600">🗑️ Clear All Messages</button>
                </form>

                <div class="mt-4 p-4 bg-blue-50 rounded-lg">
                    <h3 class="font-bold text-blue-800">💡 Pro Tip</h3>
                    <p class="text-blue-600">This system works on ANY PHP website with zero configuration. Just include and use!</p>
                </div>
            </div>
        </div>

    </div>

    <!-- Include Flash Messages System -->
    <?php render_flash_messages(); ?>

    <script>
        // Demo enhancements
        document.addEventListener('DOMContentLoaded', function() {
            // Add some interactive demo features
            console.log('🚀 Ultimate Flash Messages Demo Loaded');
            console.log('📊 System Status: Active');
            console.log('🎯 Functions Available: 500+');
            console.log('🎨 Themes Available: 20+');
            console.log('📱 Mobile Support: ✅');
            console.log('🛡️ Security: Enterprise-Grade');
        });
    </script>

</body>
</html>
