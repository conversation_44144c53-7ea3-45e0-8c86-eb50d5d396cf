<?php
// Minimal test - just load the system
require_once __DIR__ . '/flash-messages.php';

// Add one simple message
flash_success('System is working!', 'Test Success');
?>
<!DOCTYPE html>
<html>
<head>
    <title>Minimal Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 50px; 
            background: #f0f0f0; 
        }
        .test-info {
            background: white;
            padding: 20px;
            border-radius: 10px;
            max-width: 500px;
            margin: 0 auto;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <h1>🧪 Minimal Test</h1>
        <p>If the system is working, you should see a green success message appear.</p>
        <p><strong>Check browser console (F12) for debug information.</strong></p>
        
        <button onclick="testManual()" style="padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">
            🧪 Test Manual Message
        </button>
    </div>

    <script>
        function testManual() {
            if (typeof window.flashShow === 'function') {
                window.flashShow({
                    id: 'manual-' + Date.now(),
                    type: 'info',
                    message: 'Manual test message!',
                    title: 'Manual Test',
                    options: { duration: 5000, dismissible: true, showIcon: true }
                });
            } else {
                alert('Flash system not loaded');
            }
        }
        
        // Debug info
        setTimeout(() => {
            console.log('=== FLASH MESSAGES DEBUG ===');
            console.log('Container:', document.getElementById('flash-container'));
            console.log('flashShow function:', typeof window.flashShow);
            console.log('flashTest function:', typeof window.flashTest);
            console.log('============================');
        }, 1000);
    </script>
</body>
</html>
