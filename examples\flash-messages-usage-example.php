<?php
/**
 * 🚀 ULTIMATE FLASH MESSAGES - USAGE EXAMPLE
 * Complete example showing how to use the system in any website
 */

// ============================================================================
// METHOD 1: ONE-LINE INCLUDE (EASIEST)
// ============================================================================
include '../views/components/flash-messages/include.php';

// ============================================================================
// METHOD 2: CUSTOM CONFIGURATION
// ============================================================================
/*
require_once '../views/components/flash-messages/setup.php';
require_once '../views/components/flash-messages/helpers.php';

// Custom configuration
FlashMessagesConfig::$theme = 'glassmorphism';
FlashMessagesConfig::$animationStyle = 'elastic';
FlashMessagesConfig::$defaultPosition = 'bottom-center';
FlashMessagesConfig::$enableConfetti = true;
FlashMessagesConfig::$enableShake = true;
FlashMessagesConfig::$enablePulse = true;
FlashMessagesConfig::$soundVolume = 0.5;

// Initialize
init_flash_messages();
*/

// ============================================================================
// SIMULATE SOME ACTIONS FOR DEMO
// ============================================================================

if (isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'login':
            if ($_POST['email'] === '<EMAIL>' && $_POST['password'] === '123456') {
                flash_login_success('Admin User');
                header('Location: ' . $_SERVER['PHP_SELF'] . '?success=login');
                exit;
            } else {
                flash_login_failed();
                header('Location: ' . $_SERVER['PHP_SELF'] . '?error=login');
                exit;
            }
            break;
            
        case 'register':
            if (!empty($_POST['name']) && !empty($_POST['email'])) {
                flash_registration_success();
                header('Location: ' . $_SERVER['PHP_SELF'] . '?success=register');
                exit;
            } else {
                flash_validation_errors(['Name is required', 'Email is required']);
                header('Location: ' . $_SERVER['PHP_SELF'] . '?error=register');
                exit;
            }
            break;
            
        case 'quiz':
            $score = rand(60, 100);
            if ($score >= 70) {
                flash_quiz_completed($score);
            } else {
                flash_quiz_failed($score);
            }
            header('Location: ' . $_SERVER['PHP_SELF'] . '?success=quiz');
            exit;
            break;
            
        case 'purchase':
            flash_payment_success(299.99);
            flash_added_to_cart('iPhone 13 Pro');
            header('Location: ' . $_SERVER['PHP_SELF'] . '?success=purchase');
            exit;
            break;
    }
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Flash Messages - Usage Example</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .form-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
            width: 100%;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .input-field {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .input-field:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
    </style>
</head>
<body>

    <div class="container mx-auto px-4 py-8 max-w-4xl">
        
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-white mb-4">
                🚀 Flash Messages Usage Example
            </h1>
            <p class="text-xl text-white/90">
                See how easy it is to integrate into any website
            </p>
        </div>

        <!-- Forms Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            
            <!-- Login Form -->
            <div class="form-card p-8">
                <h2 class="text-2xl font-bold text-gray-800 mb-6">🔐 Login Form</h2>
                <form method="POST" class="space-y-4">
                    <input type="hidden" name="action" value="login">
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                        <input type="email" name="email" class="input-field" placeholder="<EMAIL>" required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                        <input type="password" name="password" class="input-field" placeholder="123456" required>
                    </div>
                    
                    <button type="submit" class="btn-primary">
                        Login
                    </button>
                    
                    <div class="text-sm text-gray-600 text-center">
                        Demo: <EMAIL> / 123456
                    </div>
                </form>
            </div>

            <!-- Registration Form -->
            <div class="form-card p-8">
                <h2 class="text-2xl font-bold text-gray-800 mb-6">📝 Registration Form</h2>
                <form method="POST" class="space-y-4">
                    <input type="hidden" name="action" value="register">
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                        <input type="text" name="name" class="input-field" placeholder="John Doe" required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                        <input type="email" name="email" class="input-field" placeholder="<EMAIL>" required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                        <input type="password" name="password" class="input-field" placeholder="Password" required>
                    </div>
                    
                    <button type="submit" class="btn-primary">
                        Register
                    </button>
                </form>
            </div>

            <!-- Quiz Simulation -->
            <div class="form-card p-8">
                <h2 class="text-2xl font-bold text-gray-800 mb-6">🎓 Quiz System</h2>
                <p class="text-gray-600 mb-6">Simulate a quiz completion with random score</p>
                
                <form method="POST">
                    <input type="hidden" name="action" value="quiz">
                    <button type="submit" class="btn-primary">
                        Complete Quiz
                    </button>
                </form>
            </div>

            <!-- E-commerce Simulation -->
            <div class="form-card p-8">
                <h2 class="text-2xl font-bold text-gray-800 mb-6">🛒 E-commerce</h2>
                <p class="text-gray-600 mb-6">Simulate a purchase with payment success</p>
                
                <form method="POST">
                    <input type="hidden" name="action" value="purchase">
                    <button type="submit" class="btn-primary">
                        Buy iPhone 13 Pro
                    </button>
                </form>
            </div>

        </div>

        <!-- Instant Alerts Section -->
        <div class="form-card p-8 mt-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 text-center">⚡ Instant JavaScript Alerts</h2>
            <p class="text-gray-600 mb-6 text-center">These alerts appear immediately without page refresh</p>
            
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button onclick="showSuccess('Data saved successfully!', 'Success!')" class="bg-green-500 text-white py-3 px-4 rounded-lg hover:bg-green-600 transition-colors">
                    Success Alert
                </button>
                <button onclick="showError('Failed to save data!', 'Error!')" class="bg-red-500 text-white py-3 px-4 rounded-lg hover:bg-red-600 transition-colors">
                    Error Alert
                </button>
                <button onclick="showWarning('Please check your input!', 'Warning!')" class="bg-yellow-500 text-white py-3 px-4 rounded-lg hover:bg-yellow-600 transition-colors">
                    Warning Alert
                </button>
                <button onclick="showInfo('Here is some information!', 'Info')" class="bg-blue-500 text-white py-3 px-4 rounded-lg hover:bg-blue-600 transition-colors">
                    Info Alert
                </button>
            </div>
        </div>

        <!-- Special Effects Section -->
        <div class="form-card p-8 mt-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 text-center">🎪 Special Effects</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <button onclick="showMessage('success', 'Achievement unlocked! 🎉', 'Congratulations!', {confetti: true, duration: 6000})" class="bg-purple-500 text-white py-3 px-4 rounded-lg hover:bg-purple-600 transition-colors">
                    🎉 Confetti Effect
                </button>
                <button onclick="showMessage('error', 'System malfunction detected!', 'Critical Error', {shake: true, duration: 8000})" class="bg-red-600 text-white py-3 px-4 rounded-lg hover:bg-red-700 transition-colors">
                    📳 Shake Animation
                </button>
                <button onclick="showMessage('warning', 'Urgent action required!', 'Important Notice', {pulse: true, autoHide: false})" class="bg-orange-500 text-white py-3 px-4 rounded-lg hover:bg-orange-600 transition-colors">
                    💓 Pulse Effect
                </button>
                <button onclick="showMessage('info', 'Special announcement!', 'Notice', {glow: true, duration: 7000})" class="bg-indigo-500 text-white py-3 px-4 rounded-lg hover:bg-indigo-600 transition-colors">
                    ✨ Glow Effect
                </button>
            </div>
        </div>

        <!-- Code Examples -->
        <div class="form-card p-8 mt-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 text-center">💻 Code Examples</h2>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                
                <!-- PHP Examples -->
                <div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">🐘 PHP Usage</h3>
                    <div class="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm overflow-x-auto">
<pre>// Include the system (one line!)
include 'flash-messages/include.php';

// Basic usage
flash_success('Login successful!');
flash_error('Invalid credentials!');
flash_warning('Please verify email!');
flash_info('Welcome to JobSpace!');

// Special effects
flash_success_confetti('Achievement!');
flash_error_shake('System error!');
flash_warning_pulse('Important!');
flash_info_glow('Special notice!');

// In controllers
if ($loginSuccess) {
    flash_login_success($user['name']);
    redirect('/dashboard');
} else {
    flash_login_failed();
    redirect('/login');
}</pre>
                    </div>
                </div>

                <!-- JavaScript Examples -->
                <div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">🟨 JavaScript Usage</h3>
                    <div class="bg-gray-900 text-yellow-400 p-4 rounded-lg font-mono text-sm overflow-x-auto">
<pre>// Basic alerts
showSuccess('Data saved!', 'Success!');
showError('Failed to save!', 'Error!');
showWarning('Check input!', 'Warning!');
showInfo('Information!', 'Info');

// Advanced options
showMessage('success', 'Message', 'Title', {
    autoHide: false,
    duration: 10000,
    position: 'top-center',
    confetti: true,
    glow: true
});

// AJAX integration
fetch('/api/save')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess(data.message);
        } else {
            showError(data.error);
        }
    });</pre>
                    </div>
                </div>

            </div>
        </div>

        <!-- Configuration Example -->
        <div class="form-card p-8 mt-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 text-center">⚙️ Configuration Example</h2>
            <div class="bg-gray-900 text-blue-400 p-6 rounded-lg font-mono text-sm overflow-x-auto">
<pre>// Custom configuration in setup.php
FlashMessagesConfig::$theme = 'glassmorphism';
FlashMessagesConfig::$animationStyle = 'elastic';
FlashMessagesConfig::$defaultPosition = 'bottom-center';
FlashMessagesConfig::$enableConfetti = true;
FlashMessagesConfig::$enableShake = true;
FlashMessagesConfig::$enablePulse = true;
FlashMessagesConfig::$soundVolume = 0.5;
FlashMessagesConfig::$defaultDuration = 6000;

// Or use presets
FlashMessagesConfig::usePreset('gaming');     // Neon theme with effects
FlashMessagesConfig::usePreset('minimal');    // Clean, simple design
FlashMessagesConfig::usePreset('professional'); // Business-friendly
FlashMessagesConfig::usePreset('mobile-first'); // Mobile optimized</pre>
            </div>
        </div>

    </div>

    <script>
        // Additional demo functions
        function showBulkDemo() {
            const messages = [
                {type: 'success', message: 'Profile updated successfully!', title: 'Success!'},
                {type: 'info', message: 'Check your email for confirmation', title: 'Info'},
                {type: 'warning', message: 'Please verify your phone number', title: 'Action Required'}
            ];
            
            messages.forEach((msg, index) => {
                setTimeout(() => {
                    showMessage(msg.type, msg.message, msg.title);
                }, index * 300);
            });
        }
        
        // Auto-demo on page load (optional)
        document.addEventListener('DOMContentLoaded', function() {
            // Show a welcome message
            setTimeout(() => {
                showInfo('Welcome to the Flash Messages demo! Try the forms and buttons above.', 'Welcome!', {
                    duration: 8000
                });
            }, 1000);
        });
    </script>

</body>
</html>
