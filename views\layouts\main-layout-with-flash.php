<?php
/**
 * Main Layout with Flash Messages
 * Example layout showing how to integrate flash messages system
 * 
 * This layout automatically handles flash messages for all pages
 * Just include this layout and flash messages will work everywhere
 */

// Include component helper (this auto-loads flash helper too)
require_once __DIR__ . '/../components/component-helper.php';

// Initialize flash messages system
init_flash_messages();

// Get page data
$pageTitle = $data['title'] ?? 'JobSpace';
$pageDescription = $data['description'] ?? 'Learn, Earn, Connect, Trade';
$bodyClass = $data['bodyClass'] ?? '';
$showHeader = $data['showHeader'] ?? true;
$showFooter = $data['showFooter'] ?? true;
$showSidebar = $data['showSidebar'] ?? false;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<?= htmlspecialchars($pageDescription) ?>">
    <title><?= htmlspecialchars($pageTitle) ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <style>
        /* Flash messages positioning */
        .flash-messages-container {
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 9999;
            pointer-events: none;
        }
        
        .flash-message {
            pointer-events: auto;
            margin-bottom: 0.5rem;
            max-width: 24rem;
        }
        
        /* Responsive adjustments */
        @media (max-width: 640px) {
            .flash-messages-container {
                top: 0.5rem;
                right: 0.5rem;
                left: 0.5rem;
            }
            
            .flash-message {
                max-width: none;
            }
        }
        
        /* Animation classes */
        .slide-in-right {
            animation: slideInRight 0.3s ease-out;
        }
        
        .slide-out-right {
            animation: slideOutRight 0.3s ease-in;
        }
        
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
    </style>
</head>
<body class="bg-gray-50 <?= $bodyClass ?>">

    <!-- Flash Messages Container (Auto-loaded) -->
    <!-- Flash messages are automatically displayed here via init_flash_messages() -->

    <!-- Header -->
    <?php if ($showHeader): ?>
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="/" class="flex items-center">
                        <img class="h-8 w-auto" src="/public/assets/images/logo.png" alt="JobSpace">
                        <span class="ml-2 text-xl font-bold text-gray-900">JobSpace</span>
                    </a>
                </div>
                
                <!-- Navigation -->
                <nav class="hidden md:flex space-x-8">
                    <a href="/quiz" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium">Quiz</a>
                    <a href="/social" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium">Social</a>
                    <a href="/ecommerce" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium">Shop</a>
                    <a href="/freelance" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium">Jobs</a>
                </nav>
                
                <!-- User Menu -->
                <div class="flex items-center space-x-4">
                    <?php if (isset($_SESSION['user'])): ?>
                        <div class="relative">
                            <button class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <img class="h-8 w-8 rounded-full" src="/public/uploads/avatars/<?= $_SESSION['user']['avatar'] ?? 'default.png' ?>" alt="Profile">
                                <span class="ml-2 text-gray-700"><?= htmlspecialchars($_SESSION['user']['first_name']) ?></span>
                            </button>
                        </div>
                        <a href="/logout" class="text-gray-700 hover:text-red-600 px-3 py-2 text-sm font-medium">Logout</a>
                    <?php else: ?>
                        <a href="/login" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium">Login</a>
                        <a href="/register" class="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700">Register</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </header>
    <?php endif; ?>

    <!-- Main Content -->
    <main class="<?= $showSidebar ? 'flex' : '' ?>">
        
        <!-- Sidebar -->
        <?php if ($showSidebar): ?>
        <aside class="w-64 bg-white shadow-sm min-h-screen">
            <div class="p-4">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Menu</h3>
                <nav class="space-y-2">
                    <a href="/dashboard" class="block px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-lg">Dashboard</a>
                    <a href="/profile" class="block px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-lg">Profile</a>
                    <a href="/settings" class="block px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-lg">Settings</a>
                </nav>
            </div>
        </aside>
        <?php endif; ?>
        
        <!-- Page Content -->
        <div class="<?= $showSidebar ? 'flex-1' : 'w-full' ?>">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                
                <!-- Page Content Slot -->
                <?php if (isset($content)): ?>
                    <?= $content ?>
                <?php else: ?>
                    <!-- Default content or include page content here -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h1 class="text-2xl font-bold text-gray-900 mb-4">Welcome to JobSpace</h1>
                        <p class="text-gray-700">This is the main layout with flash messages integration.</p>
                        
                        <!-- Flash Message Test Buttons -->
                        <div class="mt-6 space-x-4">
                            <button onclick="testFlashMessage('success')" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                                Test Success
                            </button>
                            <button onclick="testFlashMessage('error')" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                                Test Error
                            </button>
                            <button onclick="testFlashMessage('warning')" class="bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700">
                                Test Warning
                            </button>
                            <button onclick="testFlashMessage('info')" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                                Test Info
                            </button>
                        </div>
                    </div>
                <?php endif; ?>
                
            </div>
        </div>
    </main>

    <!-- Footer -->
    <?php if ($showFooter): ?>
    <footer class="bg-gray-800 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">JobSpace</h3>
                    <p class="text-gray-300">Learn, Earn, Connect, Trade - Your complete platform for growth.</p>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Features</h4>
                    <ul class="space-y-2 text-gray-300">
                        <li><a href="/quiz" class="hover:text-white">Quiz System</a></li>
                        <li><a href="/social" class="hover:text-white">Social Media</a></li>
                        <li><a href="/ecommerce" class="hover:text-white">E-commerce</a></li>
                        <li><a href="/freelance" class="hover:text-white">Freelance</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Support</h4>
                    <ul class="space-y-2 text-gray-300">
                        <li><a href="/help" class="hover:text-white">Help Center</a></li>
                        <li><a href="/contact" class="hover:text-white">Contact Us</a></li>
                        <li><a href="/faq" class="hover:text-white">FAQ</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Legal</h4>
                    <ul class="space-y-2 text-gray-300">
                        <li><a href="/privacy" class="hover:text-white">Privacy Policy</a></li>
                        <li><a href="/terms" class="hover:text-white">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-300">
                <p>&copy; 2024 JobSpace. All rights reserved.</p>
            </div>
        </div>
    </footer>
    <?php endif; ?>

    <!-- JavaScript -->
    <script>
        // Test flash messages function
        function testFlashMessage(type) {
            const messages = {
                'success': { message: 'This is a success message!', title: 'Success!' },
                'error': { message: 'This is an error message!', title: 'Error!' },
                'warning': { message: 'This is a warning message!', title: 'Warning!' },
                'info': { message: 'This is an info message!', title: 'Info' }
            };
            
            const msg = messages[type];
            if (msg && typeof addFlashMessage === 'function') {
                addFlashMessage(type, msg.message, msg.title);
            }
        }
        
        // Auto-hide flash messages on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Any additional JavaScript initialization
            console.log('JobSpace layout loaded with flash messages support');
        });
    </script>

</body>
</html>
