<!DOCTYPE html>
<html>
<head>
    <title>🧪 Basic JavaScript Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .btn {
            background: #007cba;
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            min-width: 200px;
            display: block;
            margin: 15px auto;
        }
        
        .btn:hover {
            background: #005a87;
            transform: translateY(-2px);
        }
        
        .btn:active {
            transform: scale(0.95);
        }
        
        .btn-success { background: #28a745; }
        .btn-error { background: #dc3545; }
        .btn-warning { background: #ffc107; color: #000; }
        .btn-info { background: #17a2b8; }
        
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .status-success { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
        .status-info { background: #d1ecf1; color: #0c5460; }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            min-height: 50px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Basic JavaScript Test</h1>
        
        <div class="status status-info">
            <strong>📋 Test Purpose:</strong><br>
            This page tests if basic JavaScript functions work in your browser.
        </div>
        
        <h3>🚨 Alert Tests</h3>
        <button class="btn btn-success" onclick="testAlert()">✅ Test Alert</button>
        
        <h3>❓ Confirm Tests</h3>
        <button class="btn btn-warning" onclick="testConfirm()">❓ Test Confirm</button>
        
        <h3>📝 Prompt Tests</h3>
        <button class="btn btn-info" onclick="testPrompt()">📝 Test Prompt</button>
        
        <h3>🎯 Advanced Tests</h3>
        <button class="btn btn-error" onclick="testMultiple()">📦 Multiple Alerts</button>
        <button class="btn" onclick="testConsole()">🖥️ Console Test</button>
        <button class="btn" onclick="testDOM()">🏗️ DOM Test</button>
        
        <div id="result" class="result">
            <strong>📊 Results will appear here...</strong>
        </div>
        
        <div class="status status-success">
            <strong>✅ Expected Behavior:</strong><br>
            • Alert button should show a popup message<br>
            • Confirm button should show Yes/No dialog<br>
            • Prompt button should ask for input<br>
            • All buttons should respond when clicked
        </div>
        
        <div class="status status-error">
            <strong>❌ If buttons don't work:</strong><br>
            • JavaScript might be disabled<br>
            • Browser might be blocking popups<br>
            • Check browser console (F12) for errors
        </div>
    </div>

    <script>
        // Test functions
        function testAlert() {
            updateResult('Testing alert...');
            alert('🚨 Alert test successful! This popup proves JavaScript is working.');
            updateResult('✅ Alert test completed');
        }
        
        function testConfirm() {
            updateResult('Testing confirm...');
            var result = confirm('❓ Do you want to continue with the test?');
            if (result) {
                updateResult('✅ Confirm test: You clicked OK');
                alert('Great! You clicked OK.');
            } else {
                updateResult('✅ Confirm test: You clicked Cancel');
                alert('You clicked Cancel, but the test still works!');
            }
        }
        
        function testPrompt() {
            updateResult('Testing prompt...');
            var name = prompt('📝 Please enter your name:');
            if (name !== null && name !== '') {
                updateResult('✅ Prompt test: Hello, ' + name + '!');
                alert('Hello, ' + name + '! Prompt test successful.');
            } else {
                updateResult('✅ Prompt test: No name entered');
                alert('No name entered, but prompt test works!');
            }
        }
        
        function testMultiple() {
            updateResult('Testing multiple alerts...');
            alert('🔢 This is alert #1');
            alert('🔢 This is alert #2');
            alert('🔢 This is alert #3');
            updateResult('✅ Multiple alerts test completed');
        }
        
        function testConsole() {
            updateResult('Testing console...');
            console.log('🖥️ Console test message');
            console.warn('⚠️ Console warning test');
            console.error('❌ Console error test');
            alert('📊 Console test completed. Check browser console (F12) to see messages.');
            updateResult('✅ Console test completed');
        }
        
        function testDOM() {
            updateResult('Testing DOM manipulation...');
            
            // Create a test element
            var testDiv = document.createElement('div');
            testDiv.innerHTML = '🏗️ DOM test element created!';
            testDiv.style.cssText = 'background: #d4edda; padding: 10px; margin: 10px 0; border-radius: 5px; color: #155724;';
            
            // Add to result area
            var resultArea = document.getElementById('result');
            resultArea.appendChild(testDiv);
            
            // Remove after 3 seconds
            setTimeout(function() {
                if (testDiv.parentElement) {
                    testDiv.remove();
                }
                updateResult('✅ DOM test completed - element created and removed');
            }, 3000);
            
            alert('🏗️ DOM test: A green box should appear and disappear in 3 seconds.');
        }
        
        function updateResult(message) {
            var resultDiv = document.getElementById('result');
            var timestamp = new Date().toLocaleTimeString();
            resultDiv.innerHTML = '<strong>[' + timestamp + ']</strong> ' + message;
        }
        
        // Auto-run basic test
        document.addEventListener('DOMContentLoaded', function() {
            updateResult('🚀 Page loaded successfully. JavaScript is working!');
            console.log('🧪 Basic test page loaded');
            
            // Test if basic functions exist
            var tests = {
                'alert': typeof alert !== 'undefined',
                'confirm': typeof confirm !== 'undefined',
                'prompt': typeof prompt !== 'undefined',
                'console': typeof console !== 'undefined',
                'document': typeof document !== 'undefined'
            };
            
            console.log('📊 Function availability:', tests);
            
            var allWorking = Object.values(tests).every(Boolean);
            if (allWorking) {
                console.log('✅ All basic JavaScript functions are available');
            } else {
                console.error('❌ Some JavaScript functions are missing');
            }
        });
        
        // Test button click detection
        document.addEventListener('click', function(e) {
            if (e.target.tagName === 'BUTTON') {
                console.log('🖱️ Button clicked:', e.target.textContent);
            }
        });
        
        // Show a welcome message after 1 second
        setTimeout(function() {
            console.log('⏰ Auto-test: JavaScript timing functions work');
        }, 1000);
    </script>
</body>
</html>
