<?php
/**
 * Universal Flash Messages System Demo
 * Complete demonstration of the most powerful notification system
 * 
 * This single system replaces ALL alert/notification needs:
 * ✅ Session-based flash messages (survives redirects)
 * ✅ Instant JavaScript alerts (immediate display)
 * ✅ Emergency fallback messages (when everything fails)
 * ✅ Auto-fallback system (smart error handling)
 * ✅ Mobile responsive design
 * ✅ Sound notifications
 * ✅ Multiple message types
 * ✅ Bulk message support
 * ✅ Custom positioning and options
 */

// Include the universal system
require_once __DIR__ . '/../views/components/component-helper.php';
require_once __DIR__ . '/../app/helpers/FlashHelper.php';

// Initialize the system
init_flash_messages();

// Simulate some flash messages for demo
if (isset($_GET['demo'])) {
    switch ($_GET['demo']) {
        case 'login':
            flash_login_success('John Doe');
            break;
        case 'error':
            flash_error('Something went wrong!', 'System Error');
            break;
        case 'multiple':
            flash_multiple([
                ['type' => 'success', 'message' => 'Profile updated successfully!'],
                ['type' => 'info', 'message' => 'Check your email for confirmation'],
                ['type' => 'warning', 'message' => 'Please verify your phone number']
            ]);
            break;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Universal Flash Messages System - JobSpace</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .demo-section {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            overflow-x: auto;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">

    <!-- Flash Messages Auto-Display Here -->
    
    <div class="container mx-auto px-4 py-8 max-w-6xl">
        
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">
                🚀 Universal Flash Messages System
            </h1>
            <p class="text-xl text-gray-600">
                The ONLY notification system you need for JobSpace
            </p>
            <p class="text-gray-500 mt-2">
                Replaces all alert components • Auto-fallback • Error-proof • Mobile-responsive
            </p>
        </div>

        <!-- Quick Demo Links -->
        <div class="demo-section">
            <h2 class="text-2xl font-semibold mb-4">🎯 Quick Demo</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <a href="?demo=login" class="bg-green-600 text-white px-6 py-3 rounded-lg text-center hover:bg-green-700 transition-colors">
                    Login Success Demo
                </a>
                <a href="?demo=error" class="bg-red-600 text-white px-6 py-3 rounded-lg text-center hover:bg-red-700 transition-colors">
                    Error Message Demo
                </a>
                <a href="?demo=multiple" class="bg-blue-600 text-white px-6 py-3 rounded-lg text-center hover:bg-blue-700 transition-colors">
                    Multiple Messages Demo
                </a>
            </div>
        </div>

        <!-- Session-Based Flash Messages -->
        <div class="demo-section">
            <h2 class="text-2xl font-semibold mb-4">📝 Session-Based Flash Messages</h2>
            <p class="text-gray-600 mb-4">These messages survive page redirects and are perfect for form submissions.</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                <button onclick="testSessionFlash('success')" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                    Success Flash
                </button>
                <button onclick="testSessionFlash('error')" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                    Error Flash
                </button>
                <button onclick="testSessionFlash('warning')" class="bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700">
                    Warning Flash
                </button>
                <button onclick="testSessionFlash('info')" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                    Info Flash
                </button>
            </div>
            
            <div class="code-block">
<strong>PHP Usage:</strong>
flash_success('Login successful!');
flash_error('Invalid credentials!');
flash_warning('Please verify your email!');
flash_info('Welcome to JobSpace!');

<strong>In Controllers:</strong>
if ($loginSuccess) {
    flash_login_success($user['name']);
    redirect('/dashboard');
} else {
    flash_login_failed();
    redirect('/login');
}
            </div>
        </div>

        <!-- Instant JavaScript Alerts -->
        <div class="demo-section">
            <h2 class="text-2xl font-semibold mb-4">⚡ Instant JavaScript Alerts</h2>
            <p class="text-gray-600 mb-4">Immediate display without page refresh. Perfect for AJAX responses.</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                <button onclick="showSuccess('Data saved successfully!', 'Success!')" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                    Instant Success
                </button>
                <button onclick="showError('Failed to save data!', 'Error!')" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                    Instant Error
                </button>
                <button onclick="showWarning('Please check your input!', 'Warning!')" class="bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700">
                    Instant Warning
                </button>
                <button onclick="showInfo('Here is some information!', 'Info')" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                    Instant Info
                </button>
            </div>
            
            <div class="code-block">
<strong>JavaScript Usage:</strong>
showSuccess('Data saved!', 'Success!');
showError('Failed to save!', 'Error!');
addFlashMessage('success', 'Custom message', 'Title', {autoHide: false});

<strong>PHP Instant Alerts:</strong>
flash_success_instant('Saved!');
flash_error_instant('Failed!');
flash_instant('success', 'Custom message', 'Title');
            </div>
        </div>

        <!-- Advanced Features -->
        <div class="demo-section">
            <h2 class="text-2xl font-semibold mb-4">🔥 Advanced Features</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                <button onclick="testBulkMessages()" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700">
                    Bulk Messages
                </button>
                <button onclick="testCustomOptions()" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700">
                    Custom Options
                </button>
                <button onclick="testEmergencyFallback()" class="bg-red-800 text-white px-4 py-2 rounded-lg hover:bg-red-900">
                    Emergency Fallback
                </button>
                <button onclick="testNoAutoHide()" class="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700">
                    No Auto-Hide
                </button>
                <button onclick="testCustomPosition()" class="bg-teal-600 text-white px-4 py-2 rounded-lg hover:bg-teal-700">
                    Custom Position
                </button>
                <button onclick="testSmartMessages()" class="bg-pink-600 text-white px-4 py-2 rounded-lg hover:bg-pink-700">
                    Smart Messages
                </button>
            </div>
            
            <div class="code-block">
<strong>Bulk Messages:</strong>
flash_multiple([
    ['type' => 'success', 'message' => 'Profile updated!'],
    ['type' => 'info', 'message' => 'Check your email']
]);

<strong>Custom Options:</strong>
flash_success('Important!', 'Notice', [
    'autoHide' => false,
    'position' => 'top-center',
    'duration' => 10000,
    'showTimestamp' => true
]);

<strong>Emergency Fallback:</strong>
flash_emergency('Critical system error!');

<strong>Smart Messages (Auto-choose method):</strong>
flash_success_smart('Message', 'Title', $instant = false);
            </div>
        </div>

        <!-- Real-World Examples -->
        <div class="demo-section">
            <h2 class="text-2xl font-semibold mb-4">🌍 Real-World Examples</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                <button onclick="simulateLogin()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                    Login Flow
                </button>
                <button onclick="simulateQuiz()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                    Quiz Completion
                </button>
                <button onclick="simulateEcommerce()" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700">
                    E-commerce Flow
                </button>
                <button onclick="simulateValidation()" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                    Validation Errors
                </button>
                <button onclick="simulateFileUpload()" class="bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700">
                    File Upload
                </button>
                <button onclick="simulateSystemError()" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700">
                    System Error
                </button>
            </div>
            
            <div class="code-block">
<strong>Authentication:</strong>
flash_login_success('John Doe');
flash_login_failed();
flash_logout_success();
flash_registration_success();

<strong>CRUD Operations:</strong>
flash_created('Quiz');
flash_updated('Profile');
flash_deleted('Post');
flash_saved('Settings');

<strong>E-commerce:</strong>
flash_added_to_cart('iPhone 13');
flash_order_placed('ORD-12345');
flash_payment_success(299.99);

<strong>Quiz System:</strong>
flash_quiz_completed(85);
flash_quiz_failed(45);
flash_quiz_time_up();
            </div>
        </div>

        <!-- System Benefits -->
        <div class="demo-section">
            <h2 class="text-2xl font-semibold mb-4">✨ System Benefits</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-semibold text-green-600 mb-3">✅ What This System Provides</h3>
                    <ul class="space-y-2 text-gray-700">
                        <li>• <strong>Universal:</strong> Works everywhere in your app</li>
                        <li>• <strong>Error-proof:</strong> Auto-fallback when things fail</li>
                        <li>• <strong>Session-based:</strong> Survives redirects</li>
                        <li>• <strong>Instant alerts:</strong> Immediate JavaScript display</li>
                        <li>• <strong>Mobile-responsive:</strong> Works on all devices</li>
                        <li>• <strong>Auto-positioning:</strong> Smart message stacking</li>
                        <li>• <strong>Sound support:</strong> Optional audio notifications</li>
                        <li>• <strong>Bulk messages:</strong> Multiple alerts at once</li>
                        <li>• <strong>Custom options:</strong> Full control over behavior</li>
                        <li>• <strong>Emergency fallback:</strong> Never fails completely</li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold text-red-600 mb-3">❌ What You Don't Need Anymore</h3>
                    <ul class="space-y-2 text-gray-700">
                        <li>• <del>Separate alert components</del></li>
                        <li>• <del>Multiple notification systems</del></li>
                        <li>• <del>Complex error handling</del></li>
                        <li>• <del>Manual positioning logic</del></li>
                        <li>• <del>Browser compatibility worries</del></li>
                        <li>• <del>Mobile responsiveness coding</del></li>
                        <li>• <del>Animation and transition code</del></li>
                        <li>• <del>Session management for alerts</del></li>
                        <li>• <del>JavaScript alert() functions</del></li>
                        <li>• <del>Custom CSS for notifications</del></li>
                    </ul>
                </div>
            </div>
        </div>

    </div>

    <script>
        // Session-based flash message tests (requires page reload)
        function testSessionFlash(type) {
            const messages = {
                'success': 'This is a session-based success message!',
                'error': 'This is a session-based error message!',
                'warning': 'This is a session-based warning message!',
                'info': 'This is a session-based info message!'
            };
            
            // Simulate setting session flash and redirect
            alert('This would set a session flash message and redirect. In real usage, this happens server-side.');
        }
        
        // Bulk messages test
        function testBulkMessages() {
            const messages = [
                {type: 'success', message: 'Profile updated successfully!', title: 'Success!'},
                {type: 'info', message: 'Check your email for confirmation', title: 'Info'},
                {type: 'warning', message: 'Please verify your phone number', title: 'Action Required'}
            ];
            
            showBulkAlerts(messages);
        }
        
        // Custom options test
        function testCustomOptions() {
            addFlashMessage('info', 'This message has custom options: no auto-hide, shows timestamp, custom duration', 'Custom Options', {
                autoHide: false,
                showTimestamp: true,
                duration: 15000,
                position: 'top-center'
            });
        }
        
        // Emergency fallback test
        function testEmergencyFallback() {
            showEmergencyMessage('This is an emergency fallback message when all else fails!');
        }
        
        // No auto-hide test
        function testNoAutoHide() {
            addFlashMessage('warning', 'This message will not auto-hide. Click the X to close it.', 'Persistent Message', {
                autoHide: false
            });
        }
        
        // Custom position test
        function testCustomPosition() {
            addFlashMessage('success', 'This message appears in the center!', 'Center Position', {
                position: 'top-center',
                duration: 3000
            });
        }
        
        // Smart messages test
        function testSmartMessages() {
            showSuccess('Smart message: Auto-chooses best display method!', 'Smart System');
        }
        
        // Real-world simulation functions
        function simulateLogin() {
            showBulkAlerts([
                {type: 'success', message: 'Welcome back, John Doe!', title: 'Login Successful'},
                {type: 'info', message: 'You have 3 new notifications', title: 'Updates'}
            ]);
        }
        
        function simulateQuiz() {
            showBulkAlerts([
                {type: 'success', message: 'Quiz completed! Your score: 85%', title: 'Well Done!'},
                {type: 'success', message: 'You earned 50 points!', title: 'Points Earned'},
                {type: 'info', message: 'Check the leaderboard to see your ranking', title: 'Leaderboard'}
            ]);
        }
        
        function simulateEcommerce() {
            showBulkAlerts([
                {type: 'success', message: 'iPhone 13 added to cart!', title: 'Added to Cart'},
                {type: 'info', message: 'Free shipping on orders over $100', title: 'Shipping Info'},
                {type: 'warning', message: 'Only 2 items left in stock!', title: 'Low Stock'}
            ]);
        }
        
        function simulateValidation() {
            showError('Please fix the following errors:<br>• Email is required<br>• Password must be at least 6 characters<br>• Phone number is invalid', 'Validation Errors');
        }
        
        function simulateFileUpload() {
            showBulkAlerts([
                {type: 'success', message: 'Profile picture uploaded successfully!', title: 'Upload Complete'},
                {type: 'warning', message: 'Image was resized to fit requirements', title: 'Image Resized'}
            ]);
        }
        
        function simulateSystemError() {
            showBulkAlerts([
                {type: 'error', message: 'Database connection failed', title: 'System Error'},
                {type: 'info', message: 'Please try again in a few minutes', title: 'Retry'},
                {type: 'warning', message: 'Contact support if problem persists', title: 'Support'}
            ]);
        }
    </script>

</body>
</html>
