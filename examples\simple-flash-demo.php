<?php
/**
 * 🚀 SIMPLE FLASH MESSAGES DEMO
 * Shows how easy it is to use the single-file flash messages system
 */

// ============================================================================
// STEP 1: INCLUDE THE FLASH MESSAGES SYSTEM (JUST ONE LINE!)
// ============================================================================
include '../views/components/flash-messages.php';

// ============================================================================
// STEP 2: SIMULATE SOME ACTIONS
// ============================================================================

// Simulate user session (optional - system works without it too)
if (!isset($_SESSION['user']) && isset($_GET['simulate_login'])) {
    $_SESSION['user'] = [
        'name' => 'John Doe',
        'email' => '<EMAIL>',
        'role' => 'user'
    ];
}

if (isset($_GET['simulate_logout'])) {
    unset($_SESSION['user']);
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'login':
            if ($_POST['email'] === '<EMAIL>' && $_POST['password'] === '123456') {
                $_SESSION['user'] = ['name' => 'Admin User', 'email' => '<EMAIL>'];
                flash_login_success('Admin User');
            } else {
                flash_login_failed();
            }
            break;
            
        case 'register':
            if (!empty($_POST['name']) && !empty($_POST['email'])) {
                flash_registration_success();
            } else {
                flash_validation_errors(['Name is required', 'Email is required']);
            }
            break;
            
        case 'quiz':
            $score = rand(60, 100);
            if ($score >= 70) {
                flash_quiz_completed($score);
            } else {
                flash_quiz_failed($score);
            }
            break;
            
        case 'payment':
            flash_payment_success(299.99);
            break;
            
        case 'create':
            flash_created('New Post');
            break;
            
        case 'update':
            flash_updated('User Profile');
            break;
            
        case 'delete':
            flash_deleted('Old File');
            break;
    }
    
    // Redirect to prevent form resubmission
    header('Location: ' . strtok($_SERVER["REQUEST_URI"], '?'));
    exit;
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Simple Flash Messages Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
            border: none;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
    </style>
</head>
<body>

    <div class="container mx-auto px-4 py-8 max-w-6xl">
        
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-white mb-4">
                🚀 Simple Flash Messages Demo
            </h1>
            <p class="text-xl text-white/90 mb-4">
                Single file solution - just include and use!
            </p>
            <div class="text-white/80">
                Current User: <?= isset($_SESSION['user']) ? $_SESSION['user']['name'] : 'Not logged in' ?>
                <?php if (isset($_SESSION['user'])): ?>
                    | <a href="?simulate_logout" class="underline">Logout</a>
                <?php else: ?>
                    | <a href="?simulate_login" class="underline">Simulate Login</a>
                <?php endif; ?>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card p-8 mb-8">
            <h2 class="text-2xl font-bold text-gray-800 mb-6 text-center">🎯 Quick Actions</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
                <form method="POST" class="inline">
                    <input type="hidden" name="action" value="quiz">
                    <button type="submit" class="btn btn-primary w-full">Quiz Test</button>
                </form>
                <form method="POST" class="inline">
                    <input type="hidden" name="action" value="payment">
                    <button type="submit" class="btn btn-primary w-full">Payment</button>
                </form>
                <form method="POST" class="inline">
                    <input type="hidden" name="action" value="create">
                    <button type="submit" class="btn btn-primary w-full">Create</button>
                </form>
                <form method="POST" class="inline">
                    <input type="hidden" name="action" value="update">
                    <button type="submit" class="btn btn-primary w-full">Update</button>
                </form>
                <form method="POST" class="inline">
                    <input type="hidden" name="action" value="delete">
                    <button type="submit" class="btn btn-primary w-full">Delete</button>
                </form>
                <button onclick="showSuccess('Instant success message!', 'JavaScript Alert')" class="btn bg-green-500 text-white w-full hover:bg-green-600">
                    JS Success
                </button>
                <button onclick="showError('Instant error message!', 'JavaScript Alert')" class="btn bg-red-500 text-white w-full hover:bg-red-600">
                    JS Error
                </button>
            </div>
        </div>

        <!-- Forms -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            
            <!-- Login Form -->
            <div class="card p-8">
                <h2 class="text-2xl font-bold text-gray-800 mb-6">🔐 Login Test</h2>
                <form method="POST" class="space-y-4">
                    <input type="hidden" name="action" value="login">
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                        <input type="email" name="email" class="input" placeholder="<EMAIL>" required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                        <input type="password" name="password" class="input" placeholder="123456" required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-full">
                        Login
                    </button>
                    
                    <div class="text-sm text-gray-600 text-center">
                        Test: <EMAIL> / 123456
                    </div>
                </form>
            </div>

            <!-- Registration Form -->
            <div class="card p-8">
                <h2 class="text-2xl font-bold text-gray-800 mb-6">📝 Registration Test</h2>
                <form method="POST" class="space-y-4">
                    <input type="hidden" name="action" value="register">
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Name</label>
                        <input type="text" name="name" class="input" placeholder="John Doe">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                        <input type="email" name="email" class="input" placeholder="<EMAIL>">
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-full">
                        Register
                    </button>
                    
                    <div class="text-sm text-gray-600 text-center">
                        Leave fields empty to test validation
                    </div>
                </form>
            </div>

        </div>

        <!-- JavaScript Examples -->
        <div class="card p-8 mt-8">
            <h2 class="text-2xl font-bold text-gray-800 mb-6 text-center">⚡ JavaScript Examples</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button onclick="showSuccess('Data saved successfully!', 'Success!')" class="btn bg-green-500 text-white hover:bg-green-600">
                    Success Alert
                </button>
                <button onclick="showError('Something went wrong!', 'Error!')" class="btn bg-red-500 text-white hover:bg-red-600">
                    Error Alert
                </button>
                <button onclick="showWarning('Please check your input!', 'Warning!')" class="btn bg-yellow-500 text-white hover:bg-yellow-600">
                    Warning Alert
                </button>
                <button onclick="showInfo('Here is some information!', 'Info')" class="btn bg-blue-500 text-white hover:bg-blue-600">
                    Info Alert
                </button>
            </div>
            
            <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                <button onclick="showMultipleMessages()" class="btn bg-purple-500 text-white hover:bg-purple-600">
                    Multiple Messages
                </button>
                <button onclick="showCustomMessage()" class="btn bg-indigo-500 text-white hover:bg-indigo-600">
                    Custom Options
                </button>
                <button onclick="dismissAllMessages()" class="btn bg-gray-500 text-white hover:bg-gray-600">
                    Dismiss All
                </button>
            </div>
        </div>

        <!-- Code Example -->
        <div class="card p-8 mt-8">
            <h2 class="text-2xl font-bold text-gray-800 mb-6 text-center">💻 How to Use</h2>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                
                <!-- PHP Usage -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">🐘 PHP Usage</h3>
                    <div class="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm overflow-x-auto">
<pre>// Step 1: Include (just one line!)
include 'flash-messages.php';

// Step 2: Use anywhere
flash_success('Login successful!');
flash_error('Invalid credentials!');
flash_warning('Please verify email!');
flash_info('Welcome message!');

// Specialized functions
flash_login_success('John Doe');
flash_quiz_completed(85);
flash_payment_success(299.99);
flash_created('New Post');
flash_updated('Profile');
flash_deleted('File');</pre>
                    </div>
                </div>

                <!-- JavaScript Usage -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">🟨 JavaScript Usage</h3>
                    <div class="bg-gray-900 text-yellow-400 p-4 rounded-lg font-mono text-sm overflow-x-auto">
<pre>// Instant alerts
showSuccess('Message!', 'Title');
showError('Error!', 'Title');
showWarning('Warning!', 'Title');
showInfo('Info!', 'Title');

// Custom options
showSuccess('Message', 'Title', {
    autoHide: false,
    duration: 10000,
    glow: true
});

// Dismiss all
dismissAllMessages();</pre>
                    </div>
                </div>

            </div>
        </div>

        <!-- Features -->
        <div class="card p-8 mt-8">
            <h2 class="text-2xl font-bold text-gray-800 mb-6 text-center">✨ Features</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="text-3xl mb-2">📱</div>
                    <h3 class="font-semibold mb-2">Mobile Responsive</h3>
                    <p class="text-gray-600 text-sm">Auto-adapts to mobile devices with touch gestures</p>
                </div>
                <div class="text-center">
                    <div class="text-3xl mb-2">🎨</div>
                    <h3 class="font-semibold mb-2">Beautiful Design</h3>
                    <p class="text-gray-600 text-sm">Modern themes with smooth animations</p>
                </div>
                <div class="text-center">
                    <div class="text-3xl mb-2">🚀</div>
                    <h3 class="font-semibold mb-2">Single File</h3>
                    <p class="text-gray-600 text-sm">Just include one file and start using</p>
                </div>
                <div class="text-center">
                    <div class="text-3xl mb-2">🔧</div>
                    <h3 class="font-semibold mb-2">Easy Config</h3>
                    <p class="text-gray-600 text-sm">Configure everything from the top of the file</p>
                </div>
                <div class="text-center">
                    <div class="text-3xl mb-2">🌐</div>
                    <h3 class="font-semibold mb-2">Auto-Detection</h3>
                    <p class="text-gray-600 text-sm">Automatically detects site name and user info</p>
                </div>
                <div class="text-center">
                    <div class="text-3xl mb-2">🛡️</div>
                    <h3 class="font-semibold mb-2">Error-Proof</h3>
                    <p class="text-gray-600 text-sm">Never fails completely, always shows messages</p>
                </div>
            </div>
        </div>

    </div>

    <script>
        // Demo functions
        function showMultipleMessages() {
            showSuccess('First message!', 'Success');
            setTimeout(() => showInfo('Second message!', 'Info'), 300);
            setTimeout(() => showWarning('Third message!', 'Warning'), 600);
        }
        
        function showCustomMessage() {
            showSuccess('This message has custom options!', 'Custom Message', {
                autoHide: false,
                glow: true
            });
        }
        
        // Auto-demo on page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                showInfo('Welcome to the Flash Messages demo! Try the buttons above.', 'Welcome!', {
                    duration: 6000
                });
            }, 1000);
        });
    </script>

</body>
</html>
