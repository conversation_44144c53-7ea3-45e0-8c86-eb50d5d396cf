<?php
/**
 * 🔗 Integration Test - Working Flash Messages
 * Test the working system with form submissions
 */

// Include the working flash messages system
require_once __DIR__ . '/working-flash.php';

// Handle form submissions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'test_success':
            simple_success('Success message created successfully!', 'Success Test');
            break;
        case 'test_error':
            simple_error('Error message created successfully!', 'Error Test');
            break;
        case 'test_warning':
            simple_warning('Warning message created successfully!', 'Warning Test');
            break;
        case 'test_info':
            simple_info('Info message created successfully!', 'Info Test');
            break;
        case 'test_multiple':
            simple_success('First message', 'Success');
            simple_error('Second message', 'Error');
            simple_warning('Third message', 'Warning');
            simple_info('Fourth message', 'Info');
            break;
        case 'test_long':
            simple_info('This is a very long message to test how the system handles longer content. It should wrap properly and still look good in the notification area.', 'Long Message Test');
            break;
    }
    
    // Redirect to prevent form resubmission
    header('Location: ' . $_SERVER['PHP_SELF'] . '#results');
    exit;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔗 Integration Test - Working Flash Messages</title>
    <style>
        body {
            font-family: system-ui, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .test-button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 8px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            min-width: 200px;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
        }
        
        .test-button:active {
            transform: scale(0.95);
        }
        
        .success { background: linear-gradient(45deg, #28a745, #20c997); }
        .error { background: linear-gradient(45deg, #dc3545, #e74c3c); }
        .warning { background: linear-gradient(45deg, #ffc107, #f39c12); }
        .info { background: linear-gradient(45deg, #17a2b8, #3498db); }
        .special { background: linear-gradient(45deg, #6f42c1, #e83e8c); }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .status-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .code-example {
            background: #1a1a1a;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔗 Integration Test - Working Flash Messages</h1>
        <p>Test the working flash messages system with real form submissions and redirects.</p>
        
        <div class="status-box">
            <h3>✅ System Status: ACTIVE</h3>
            <p><strong>Working Flash Messages system is loaded and ready!</strong></p>
            <p>Click any button below to test different message types.</p>
        </div>
        
        <h3>🧪 Single Message Tests</h3>
        <div class="test-grid">
            <form method="POST">
                <input type="hidden" name="action" value="test_success">
                <button type="submit" class="test-button success">✅ Success Message</button>
            </form>
            
            <form method="POST">
                <input type="hidden" name="action" value="test_error">
                <button type="submit" class="test-button error">❌ Error Message</button>
            </form>
            
            <form method="POST">
                <input type="hidden" name="action" value="test_warning">
                <button type="submit" class="test-button warning">⚠️ Warning Message</button>
            </form>
            
            <form method="POST">
                <input type="hidden" name="action" value="test_info">
                <button type="submit" class="test-button info">ℹ️ Info Message</button>
            </form>
        </div>
        
        <h3>🎯 Advanced Tests</h3>
        <div class="test-grid">
            <form method="POST">
                <input type="hidden" name="action" value="test_multiple">
                <button type="submit" class="test-button special">📦 Multiple Messages</button>
            </form>
            
            <form method="POST">
                <input type="hidden" name="action" value="test_long">
                <button type="submit" class="test-button special">📝 Long Message</button>
            </form>
        </div>
        
        <div id="results">
            <h3>📋 Usage Examples</h3>
            <p>Here's how to use the working flash messages system:</p>
            
            <h4>🔧 Basic Usage</h4>
            <div class="code-example">
&lt;?php
// Include the working system
require_once 'working-flash.php';

// Add messages
simple_success('Operation completed!', 'Success!');
simple_error('Something went wrong!', 'Error!');
simple_warning('Please check this!', 'Warning!');
simple_info('Here is some information', 'Info');
?&gt;
            </div>
            
            <h4>🎯 Form Integration</h4>
            <div class="code-example">
&lt;?php
if ($_POST['action'] === 'save_data') {
    // Process form data
    if ($success) {
        simple_success('Data saved successfully!', 'Success!');
    } else {
        simple_error('Failed to save data!', 'Error!');
    }
    
    // Redirect to prevent resubmission
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}
?&gt;
            </div>
            
            <h4>⭐ Key Features</h4>
            <ul>
                <li>✅ <strong>Zero Dependencies</strong> - Works on any PHP website</li>
                <li>✅ <strong>Auto-Hide</strong> - Messages disappear after 5 seconds</li>
                <li>✅ <strong>Click to Dismiss</strong> - Users can close messages manually</li>
                <li>✅ <strong>Mobile Responsive</strong> - Adapts to all screen sizes</li>
                <li>✅ <strong>Beautiful Animations</strong> - Smooth slide-in/out effects</li>
                <li>✅ <strong>Session-Based</strong> - Survives page redirects</li>
                <li>✅ <strong>XSS Protected</strong> - All content is properly escaped</li>
                <li>✅ <strong>Easy Integration</strong> - Just include one file</li>
            </ul>
            
            <h4>🔄 Migration from Complex System</h4>
            <p>To replace the complex flash messages system with this working version:</p>
            <ol>
                <li>Replace <code>require_once 'flash-messages.php';</code> with <code>require_once 'working-flash.php';</code></li>
                <li>Change function calls:
                    <ul>
                        <li><code>flash_success()</code> → <code>simple_success()</code></li>
                        <li><code>flash_error()</code> → <code>simple_error()</code></li>
                        <li><code>flash_warning()</code> → <code>simple_warning()</code></li>
                        <li><code>flash_info()</code> → <code>simple_info()</code></li>
                    </ul>
                </li>
                <li>Remove any manual rendering calls - the system auto-renders</li>
                <li>Test and enjoy the working system!</li>
            </ol>
        </div>
        
        <div class="status-box">
            <h3>🎉 Success!</h3>
            <p>The working flash messages system is now ready for production use. It's simple, reliable, and guaranteed to work on any PHP website.</p>
        </div>
    </div>

    <!-- Flash messages will be automatically rendered here -->

</body>
</html>
