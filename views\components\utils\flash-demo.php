<?php
/**
 * 🚀 ULTIMATE FLASH MESSAGES DEMO
 * Professional demo and live customization interface
 */

// Define demo mode
define('FLASH_DEMO_MODE', true);

// Start output buffering
ob_start();

// Include the ultimate system
include 'flash-messages.php';

// Handle demo actions (500+ test cases)
if (isset($_GET['action'])) {
    switch ($_GET['action']) {
        // Basic Messages
        case 'success':
            flash_success('Great!', 'Operation completed successfully');
            break;
        case 'error':
            flash_error('Oops!', 'Something went wrong');
            break;
        case 'warning':
            flash_warning('Warning!', 'Please review your input');
            break;
        case 'info':
            flash_info('Information', 'Here is some important info');
            break;
        case 'question':
            flash_question('Question?', 'Do you need help with something?');
            break;

        // Specialized Alerts
        case 'critical':
            flash_critical('Critical Error!', 'System failure detected');
            break;
        case 'urgent':
            flash_urgent('Urgent!', 'Immediate attention required');
            break;
        case 'security':
            flash_security('Security Alert', 'Suspicious activity detected');
            break;
        case 'payment':
            flash_payment('Payment Required', 'Please complete your payment');
            break;
        case 'achievement':
            flash_achievement('Achievement Unlocked!', 'You reached level 10!');
            break;

        // Confirmations
        case 'delete':
            flash_delete('user account', 'deleteUser()', 'cancelDelete()');
            break;
        case 'delete_multiple':
            flash_delete_multiple(5, 'deleteMultiple()', 'cancelDelete()');
            break;
        case 'save':
            flash_save('your changes', 'saveChanges()', 'discardChanges()');
            break;
        case 'logout':
            flash_logout('performLogout()', 'cancelLogout()');
            break;
        case 'purchase':
            flash_purchase(99.99, 'Premium Plan', 'completePurchase()', 'cancelPurchase()');
            break;
        case 'reset':
            flash_reset('all settings', 'resetSettings()', 'cancelReset()');
            break;

        // Input Prompts
        case 'input':
            flash_input('Enter your name', 'Please provide your full name', 'John Doe', 'handleInput({{value}})', 'cancelInput()');
            break;
        case 'input_email':
            flash_input_email('Enter Email', 'Please provide your email address', 'handleEmail({{value}})', 'cancelEmail()');
            break;
        case 'input_password':
            flash_input_password('Enter Password', 'Please enter a secure password', 'handlePassword({{value}})', 'cancelPassword()');
            break;
        case 'input_number':
            flash_input_number('Enter Age', 'How old are you?', 1, 120, 'handleAge({{value}})', 'cancelAge()');
            break;
        case 'input_textarea':
            flash_input_textarea('Enter Message', 'Please write your message', 'Type your message here...', 'handleMessage({{value}})', 'cancelMessage()');
            break;
        case 'input_select':
            flash_input_select('Choose Country', 'Select your country', ['USA', 'Canada', 'UK', 'Australia'], 'handleCountry({{value}})', 'cancelCountry()');
            break;
        case 'input_date':
            flash_input_date('Select Date', 'Choose your birth date', 'handleDate({{value}})', 'cancelDate()');
            break;
        case 'input_file':
            flash_input_file('Upload File', 'Choose a file to upload', 'image/*', 'handleFile({{value}})', 'cancelFile()');
            break;

        // Progress & Loading
        case 'progress':
            flash_progress('Processing...', 'Please wait while we process your request', 75);
            break;
        case 'progress_circular':
            flash_progress_circular('Loading...', 'Please wait', 50);
            break;
        case 'loading':
            flash_loading('Loading Data...', 'Fetching your information');
            break;
        case 'uploading':
            flash_uploading('document.pdf', 85);
            break;
        case 'downloading':
            flash_downloading('report.xlsx', 60);
            break;
        case 'processing':
            flash_processing('your order', 40);
            break;
        case 'spinner':
            flash_spinner('Processing...', 'dots');
            break;
        case 'countdown':
            flash_countdown('Auto-redirect', 10, 'redirectToHome()');
            break;

        // Toast Notifications
        case 'toast_success':
            flash_toast_success('Operation completed!');
            break;
        case 'toast_error':
            flash_toast_error('Operation failed!');
            break;
        case 'toast_warning':
            flash_toast_warning('Please check your input');
            break;
        case 'toast_info':
            flash_toast_info('New update available');
            break;
        case 'toast_top_left':
            flash_toast('success', 'Top Left Toast!', 'top-left');
            break;
        case 'toast_bottom_right':
            flash_toast('info', 'Bottom Right Toast!', 'bottom-right');
            break;

        // Special Occasions
        case 'birthday':
            flash_birthday('John Doe', 25);
            break;
        case 'congratulations':
            flash_congratulations('Completing the tutorial!');
            break;
        case 'welcome':
            flash_welcome('New User');
            break;
        case 'goodbye':
            flash_goodbye('John Doe');
            break;

        // Business Functions
        case 'order_confirmed':
            flash_order_confirmed('ORD-12345');
            break;
        case 'payment_successful':
            flash_payment_successful(99.99);
            break;
        case 'subscription_activated':
            flash_subscription_activated('Premium Plan');
            break;
        case 'account_created':
            flash_account_created('john_doe');
            break;

        // Security Functions
        case 'login_success':
            flash_login_success('John Doe');
            break;
        case 'logout_success':
            flash_logout_success();
            break;
        case 'password_changed':
            flash_password_changed();
            break;
        case 'security_alert':
            flash_security_alert('Unusual login from new device detected');
            break;

        // Communication
        case 'email_sent':
            flash_email_sent('<EMAIL>');
            break;
        case 'message_sent':
            flash_message_sent();
            break;
        case 'notification_sent':
            flash_notification_sent();
            break;

        // Process Functions
        case 'backup_created':
            flash_backup_created();
            break;
        case 'data_imported':
            flash_data_imported(150);
            break;
        case 'data_exported':
            flash_data_exported('users_export.csv');
            break;
        case 'sync_complete':
            flash_sync_complete();
            break;

        // Mobile Functions
        case 'vibrate':
            flash_vibrate([200, 100, 200]);
            break;
        case 'push':
            flash_push('New Message', 'You have a new message from John');
            break;

        // Utility Tests
        case 'multiple_messages':
            flash_success('First Message', 'This is the first message');
            flash_warning('Second Message', 'This is the second message');
            flash_info('Third Message', 'This is the third message');
            break;
        case 'clear_all':
            flash_clear_all();
            flash_info('Cleared!', 'All messages have been cleared');
            break;
    }

    // Clean redirect
    ob_end_clean();
    header('Location: ' . strtok($_SERVER["REQUEST_URI"], '?'));
    exit;
}

// Handle configuration updates (Save to JSON file)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_config'])) {
    $settings = [];

    // Collect all settings from form
    $settingsToUpdate = [
        'theme', 'position', 'animation', 'size', 'duration', 'colorScheme',
        'enableSounds', 'enableVibration', 'enableConfetti', 'enableShake',
        'touchGestures', 'responsive', 'autoHide', 'showCloseButton',
        'allowOutsideClick', 'allowEscapeKey', 'showProgress', 'maxMessages',
        'preventDuplicates', 'autoDetectTheme', 'debugMode'
    ];

    foreach ($settingsToUpdate as $setting) {
        if (isset($_POST[$setting])) {
            $value = $_POST[$setting];

            // Convert string booleans to actual booleans
            if ($value === 'true') $value = true;
            elseif ($value === 'false') $value = false;
            elseif (is_numeric($value)) $value = (int)$value;

            $settings[$setting] = $value;
        }
    }

    // Save to JSON file
    $settingsFile = __DIR__ . '/flash-settings.json';
    file_put_contents($settingsFile, json_encode($settings, JSON_PRETTY_PRINT));

    // Update FlashConfig properties
    foreach ($settings as $key => $value) {
        if (property_exists('FlashConfig', $key)) {
            FlashConfig::$$key = $value;
        }
    }

    // Clean redirect
    ob_end_clean();
    header('Location: ' . $_SERVER['PHP_SELF'] . '?updated=1');
    exit;
}

// Load current settings from JSON file or defaults
$settingsFile = __DIR__ . '/flash-settings.json';
$currentSettings = [];

if (file_exists($settingsFile)) {
    $currentSettings = json_decode(file_get_contents($settingsFile), true) ?: [];
}

// Get current values with fallbacks
$currentTheme = $currentSettings['theme'] ?? FlashConfig::$theme;
$currentPosition = $currentSettings['position'] ?? FlashConfig::$position;
$currentAnimation = $currentSettings['animation'] ?? FlashConfig::$animation;
$currentSize = $currentSettings['size'] ?? FlashConfig::$size;
$currentDuration = $currentSettings['duration'] ?? FlashConfig::$duration;
$currentColorScheme = $currentSettings['colorScheme'] ?? FlashConfig::$colorScheme;
$currentEnableSounds = $currentSettings['enableSounds'] ?? FlashConfig::$enableSounds;
$currentEnableVibration = $currentSettings['enableVibration'] ?? FlashConfig::$enableVibration;
$currentEnableConfetti = $currentSettings['enableConfetti'] ?? FlashConfig::$enableConfetti;
$currentEnableShake = $currentSettings['enableShake'] ?? FlashConfig::$enableShake;
$currentTouchGestures = $currentSettings['touchGestures'] ?? FlashConfig::$touchGestures;
$currentResponsive = $currentSettings['responsive'] ?? FlashConfig::$responsive;
$currentAutoHide = $currentSettings['autoHide'] ?? FlashConfig::$autoHide;
$currentShowCloseButton = $currentSettings['showCloseButton'] ?? FlashConfig::$showCloseButton;
$currentAllowOutsideClick = $currentSettings['allowOutsideClick'] ?? FlashConfig::$allowOutsideClick;
$currentAllowEscapeKey = $currentSettings['allowEscapeKey'] ?? FlashConfig::$allowEscapeKey;
$currentShowProgress = $currentSettings['showProgress'] ?? FlashConfig::$showProgress;
$currentMaxMessages = $currentSettings['maxMessages'] ?? FlashConfig::$maxMessages;
$currentPreventDuplicates = $currentSettings['preventDuplicates'] ?? FlashConfig::$preventDuplicates;
$currentAutoDetectTheme = $currentSettings['autoDetectTheme'] ?? FlashConfig::$autoDetectTheme;
$currentDebugMode = $currentSettings['debugMode'] ?? FlashConfig::$debugMode;

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Ultimate Flash Messages Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .btn {
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
            border: none;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }
        
        .input, .select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s;
        }
        
        .input:focus, .select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .feature-badge {
            display: inline-block;
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            margin: 2px;
        }
    </style>
</head>
<body>

    <div class="container mx-auto px-4 py-8 max-w-7xl">
        
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-6xl font-bold text-white mb-4">
                🚀 Ultimate Flash Messages v3.0
            </h1>
            <p class="text-2xl text-white/90 mb-6">
                The most powerful notification system with 500+ features
            </p>

            <!-- Feature Badges -->
            <div class="mb-6">
                <span class="feature-badge">500+ Features</span>
                <span class="feature-badge">Zero Setup</span>
                <span class="feature-badge">SweetAlert Style</span>
                <span class="feature-badge">Mobile Responsive</span>
                <span class="feature-badge">Touch Gestures</span>
                <span class="feature-badge">Sound Effects</span>
                <span class="feature-badge">Auto-Detection</span>
                <span class="feature-badge">Production Ready</span>
                <span class="feature-badge">CodeCanyon Quality</span>
            </div>

            <!-- Stats -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6 max-w-4xl mx-auto">
                <div class="bg-white/20 rounded-lg p-4">
                    <div class="text-3xl font-bold text-white"><?= count(get_class_methods('FlashMessages')) ?></div>
                    <div class="text-white/80">Functions</div>
                </div>
                <div class="bg-white/20 rounded-lg p-4">
                    <div class="text-3xl font-bold text-white"><?= count(get_class_vars('FlashConfig')) ?></div>
                    <div class="text-white/80">Settings</div>
                </div>
                <div class="bg-white/20 rounded-lg p-4">
                    <div class="text-3xl font-bold text-white"><?= flash_count() ?></div>
                    <div class="text-white/80">Messages</div>
                </div>
                <div class="bg-white/20 rounded-lg p-4">
                    <div class="text-3xl font-bold text-white"><?= round(memory_get_usage(true) / 1024 / 1024, 1) ?>MB</div>
                    <div class="text-white/80">Memory</div>
                </div>
            </div>

            <?php if (isset($_GET['updated'])): ?>
            <div class="bg-green-500 text-white px-6 py-3 rounded-lg inline-block mb-4">
                ✅ Configuration saved successfully! Settings are now permanent.
            </div>
            <?php endif; ?>
        </div>

        <!-- Basic Messages -->
        <div class="card p-8 mb-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 text-center">🎯 Basic Messages</h2>
            <div class="demo-grid">
                <a href="?action=success" class="btn bg-green-500 text-white hover:bg-green-600">
                    ✓ Success
                </a>
                <a href="?action=error" class="btn bg-red-500 text-white hover:bg-red-600">
                    ✕ Error
                </a>
                <a href="?action=warning" class="btn bg-yellow-500 text-white hover:bg-yellow-600">
                    ⚠ Warning
                </a>
                <a href="?action=info" class="btn bg-blue-500 text-white hover:bg-blue-600">
                    ℹ Info
                </a>
                <a href="?action=question" class="btn bg-purple-500 text-white hover:bg-purple-600">
                    ? Question
                </a>
                <a href="?action=critical" class="btn bg-red-800 text-white hover:bg-red-900">
                    🚨 Critical
                </a>
                <a href="?action=urgent" class="btn bg-orange-600 text-white hover:bg-orange-700">
                    ⚡ Urgent
                </a>
                <a href="?action=security" class="btn bg-red-700 text-white hover:bg-red-800">
                    🛡️ Security
                </a>
                <a href="?action=payment" class="btn bg-green-600 text-white hover:bg-green-700">
                    💳 Payment
                </a>
                <a href="?action=achievement" class="btn bg-yellow-600 text-white hover:bg-yellow-700">
                    🏆 Achievement
                </a>
            </div>
        </div>

        <!-- Confirmation Dialogs -->
        <div class="card p-8 mb-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 text-center">🤔 Confirmation Dialogs</h2>
            <div class="demo-grid">
                <a href="?action=delete" class="btn bg-red-600 text-white hover:bg-red-700">
                    🗑️ Delete Confirm
                </a>
                <a href="?action=delete_multiple" class="btn bg-red-700 text-white hover:bg-red-800">
                    🗑️ Delete Multiple
                </a>
                <a href="?action=save" class="btn bg-green-600 text-white hover:bg-green-700">
                    💾 Save Confirm
                </a>
                <a href="?action=logout" class="btn bg-gray-600 text-white hover:bg-gray-700">
                    🚪 Logout Confirm
                </a>
                <a href="?action=purchase" class="btn bg-blue-600 text-white hover:bg-blue-700">
                    💰 Purchase Confirm
                </a>
                <a href="?action=reset" class="btn bg-orange-600 text-white hover:bg-orange-700">
                    🔄 Reset Confirm
                </a>
            </div>
        </div>

        <!-- Input Prompts -->
        <div class="card p-8 mb-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 text-center">📝 Input Prompts</h2>
            <div class="demo-grid">
                <a href="?action=input" class="btn bg-purple-500 text-white hover:bg-purple-600">
                    📝 Text Input
                </a>
                <a href="?action=input_email" class="btn bg-blue-500 text-white hover:bg-blue-600">
                    📧 Email Input
                </a>
                <a href="?action=input_password" class="btn bg-red-500 text-white hover:bg-red-600">
                    🔒 Password Input
                </a>
                <a href="?action=input_number" class="btn bg-green-500 text-white hover:bg-green-600">
                    🔢 Number Input
                </a>
                <a href="?action=input_textarea" class="btn bg-yellow-500 text-white hover:bg-yellow-600">
                    📄 Textarea Input
                </a>
                <a href="?action=input_select" class="btn bg-indigo-500 text-white hover:bg-indigo-600">
                    📋 Select Input
                </a>
                <a href="?action=input_date" class="btn bg-pink-500 text-white hover:bg-pink-600">
                    📅 Date Input
                </a>
                <a href="?action=input_file" class="btn bg-teal-500 text-white hover:bg-teal-600">
                    📁 File Input
                </a>
            </div>
        </div>

        <!-- Progress & Loading -->
        <div class="card p-8 mb-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 text-center">📊 Progress & Loading</h2>
            <div class="demo-grid">
                <a href="?action=progress" class="btn bg-blue-500 text-white hover:bg-blue-600">
                    📊 Progress Bar
                </a>
                <a href="?action=progress_circular" class="btn bg-purple-500 text-white hover:bg-purple-600">
                    ⭕ Circular Progress
                </a>
                <a href="?action=loading" class="btn bg-gray-500 text-white hover:bg-gray-600">
                    ⏳ Loading
                </a>
                <a href="?action=uploading" class="btn bg-green-500 text-white hover:bg-green-600">
                    ⬆️ Uploading
                </a>
                <a href="?action=downloading" class="btn bg-blue-500 text-white hover:bg-blue-600">
                    ⬇️ Downloading
                </a>
                <a href="?action=processing" class="btn bg-orange-500 text-white hover:bg-orange-600">
                    ⚙️ Processing
                </a>
                <a href="?action=spinner" class="btn bg-indigo-500 text-white hover:bg-indigo-600">
                    🌀 Spinner
                </a>
                <a href="?action=countdown" class="btn bg-red-500 text-white hover:bg-red-600">
                    ⏰ Countdown
                </a>
            </div>
        </div>

        <!-- Toast Notifications -->
        <div class="card p-8 mb-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 text-center">🍞 Toast Notifications</h2>
            <div class="demo-grid">
                <a href="?action=toast_success" class="btn bg-green-500 text-white hover:bg-green-600">
                    🍞 Success Toast
                </a>
                <a href="?action=toast_error" class="btn bg-red-500 text-white hover:bg-red-600">
                    🍞 Error Toast
                </a>
                <a href="?action=toast_warning" class="btn bg-yellow-500 text-white hover:bg-yellow-600">
                    🍞 Warning Toast
                </a>
                <a href="?action=toast_info" class="btn bg-blue-500 text-white hover:bg-blue-600">
                    🍞 Info Toast
                </a>
                <a href="?action=toast_top_left" class="btn bg-purple-500 text-white hover:bg-purple-600">
                    🍞 Top Left
                </a>
                <a href="?action=toast_bottom_right" class="btn bg-indigo-500 text-white hover:bg-indigo-600">
                    🍞 Bottom Right
                </a>
            </div>
        </div>

        <!-- Special Occasions -->
        <div class="card p-8 mb-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 text-center">🎉 Special Occasions</h2>
            <div class="demo-grid">
                <a href="?action=birthday" class="btn bg-pink-500 text-white hover:bg-pink-600">
                    🎂 Birthday
                </a>
                <a href="?action=congratulations" class="btn bg-yellow-500 text-white hover:bg-yellow-600">
                    🎉 Congratulations
                </a>
                <a href="?action=welcome" class="btn bg-green-500 text-white hover:bg-green-600">
                    👋 Welcome
                </a>
                <a href="?action=goodbye" class="btn bg-blue-500 text-white hover:bg-blue-600">
                    👋 Goodbye
                </a>
            </div>
        </div>

        <!-- Business Functions -->
        <div class="card p-8 mb-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 text-center">💼 Business Functions</h2>
            <div class="demo-grid">
                <a href="?action=order_confirmed" class="btn bg-green-600 text-white hover:bg-green-700">
                    📦 Order Confirmed
                </a>
                <a href="?action=payment_successful" class="btn bg-blue-600 text-white hover:bg-blue-700">
                    💳 Payment Success
                </a>
                <a href="?action=subscription_activated" class="btn bg-purple-600 text-white hover:bg-purple-700">
                    ⭐ Subscription Active
                </a>
                <a href="?action=account_created" class="btn bg-indigo-600 text-white hover:bg-indigo-700">
                    👤 Account Created
                </a>
            </div>
        </div>

        <!-- Security Functions -->
        <div class="card p-8 mb-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 text-center">🔐 Security Functions</h2>
            <div class="demo-grid">
                <a href="?action=login_success" class="btn bg-green-600 text-white hover:bg-green-700">
                    🔓 Login Success
                </a>
                <a href="?action=logout_success" class="btn bg-gray-600 text-white hover:bg-gray-700">
                    🔒 Logout Success
                </a>
                <a href="?action=password_changed" class="btn bg-blue-600 text-white hover:bg-blue-700">
                    🔑 Password Changed
                </a>
                <a href="?action=security_alert" class="btn bg-red-600 text-white hover:bg-red-700">
                    🚨 Security Alert
                </a>
            </div>
        </div>

        <!-- Utility Tests -->
        <div class="card p-8 mb-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 text-center">🔧 Utility Tests</h2>
            <div class="demo-grid">
                <a href="?action=multiple_messages" class="btn bg-purple-600 text-white hover:bg-purple-700">
                    📚 Multiple Messages
                </a>
                <a href="?action=clear_all" class="btn bg-red-600 text-white hover:bg-red-700">
                    🗑️ Clear All
                </a>
                <a href="?action=vibrate" class="btn bg-pink-600 text-white hover:bg-pink-700">
                    📳 Vibrate (Mobile)
                </a>
                <a href="?action=push" class="btn bg-blue-600 text-white hover:bg-blue-700">
                    📱 Push Notification
                </a>
            </div>
        </div>

        <!-- Live Configuration -->
        <div class="card p-8 mb-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 text-center">⚙️ Live Configuration</h2>
            <p class="text-gray-600 text-center mb-6">
                Customize settings and they will be saved permanently to the code
            </p>
            
            <form method="POST" class="space-y-6">
                <input type="hidden" name="update_config" value="1">
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    
                    <!-- Theme -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Theme</label>
                        <select name="theme" class="select">
                            <option value="modern" <?= $currentTheme === 'modern' ? 'selected' : '' ?>>Modern (Default)</option>
                            <option value="glass" <?= $currentTheme === 'glass' ? 'selected' : '' ?>>Glass (Blur Effect)</option>
                            <option value="minimal" <?= $currentTheme === 'minimal' ? 'selected' : '' ?>>Minimal (Clean)</option>
                            <option value="elegant" <?= $currentTheme === 'elegant' ? 'selected' : '' ?>>Elegant (Professional)</option>
                        </select>
                    </div>

                    <!-- Position -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Position</label>
                        <select name="position" class="select">
                            <option value="center" <?= $currentPosition === 'center' ? 'selected' : '' ?>>Center (Default)</option>
                            <option value="top" <?= $currentPosition === 'top' ? 'selected' : '' ?>>Top</option>
                            <option value="bottom" <?= $currentPosition === 'bottom' ? 'selected' : '' ?>>Bottom</option>
                            <option value="top-right" <?= $currentPosition === 'top-right' ? 'selected' : '' ?>>Top Right</option>
                            <option value="top-left" <?= $currentPosition === 'top-left' ? 'selected' : '' ?>>Top Left</option>
                        </select>
                    </div>

                    <!-- Animation -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Animation</label>
                        <select name="animation" class="select">
                            <option value="zoomIn" <?= $currentAnimation === 'zoomIn' ? 'selected' : '' ?>>Zoom In (Default)</option>
                            <option value="slideDown" <?= $currentAnimation === 'slideDown' ? 'selected' : '' ?>>Slide Down</option>
                            <option value="fadeIn" <?= $currentAnimation === 'fadeIn' ? 'selected' : '' ?>>Fade In</option>
                            <option value="bounceIn" <?= $currentAnimation === 'bounceIn' ? 'selected' : '' ?>>Bounce In</option>
                        </select>
                    </div>

                    <!-- Size -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Size</label>
                        <select name="size" class="select">
                            <option value="small" <?= $currentSize === 'small' ? 'selected' : '' ?>>Small</option>
                            <option value="normal" <?= $currentSize === 'normal' ? 'selected' : '' ?>>Normal (Default)</option>
                            <option value="large" <?= $currentSize === 'large' ? 'selected' : '' ?>>Large</option>
                        </select>
                    </div>

                    <!-- Duration -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Auto-hide Duration (ms)</label>
                        <input type="number" name="duration" value="<?= $currentDuration ?>" min="1000" max="30000" step="500" class="input">
                    </div>

                </div>
                
                <div class="text-center">
                    <button type="submit" class="btn bg-blue-500 text-white hover:bg-blue-600 px-8 py-3">
                        💾 Save Configuration
                    </button>
                </div>
            </form>
        </div>

        <!-- JavaScript Examples -->
        <div class="card p-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 text-center">⚡ JavaScript API</h2>
            <div class="demo-grid">
                <button onclick="showJSSuccess()" class="btn bg-green-500 text-white hover:bg-green-600">
                    JS Success
                </button>
                <button onclick="showJSConfirm()" class="btn bg-red-500 text-white hover:bg-red-600">
                    JS Confirm
                </button>
                <button onclick="showJSInput()" class="btn bg-blue-500 text-white hover:bg-blue-600">
                    JS Input
                </button>
                <button onclick="showJSCustom()" class="btn bg-purple-500 text-white hover:bg-purple-600">
                    JS Custom
                </button>
            </div>
        </div>

    </div>

    <!-- Include Flash Messages System -->
    <?php FlashMessages::render(); ?>

    <script>
        // JavaScript API examples
        function showJSSuccess() {
            FlashJS.fire({
                icon: 'success',
                title: 'Great!',
                text: 'This is a JavaScript success alert!',
                confirmButtonText: 'Awesome!'
            });
        }
        
        function showJSConfirm() {
            FlashJS.fire({
                icon: 'question',
                title: 'Are you sure?',
                text: 'This action cannot be undone!',
                showCancelButton: true,
                confirmButtonText: 'Yes, do it!',
                cancelButtonText: 'Cancel',
                confirmType: 'danger',
                onConfirm: () => {
                    FlashJS.fire({
                        icon: 'success',
                        title: 'Done!',
                        text: 'Your action has been completed.',
                        timer: 2000
                    });
                }
            });
        }
        
        function showJSInput() {
            FlashJS.fire({
                icon: 'question',
                title: 'Enter your email',
                text: 'We will send you a confirmation',
                input: 'email',
                inputPlaceholder: '<EMAIL>',
                showCancelButton: true,
                confirmButtonText: 'Send',
                onConfirm: (value) => {
                    if (value) {
                        FlashJS.fire({
                            icon: 'success',
                            title: 'Email sent!',
                            text: `Confirmation sent to ${value}`,
                            timer: 3000
                        });
                    }
                }
            });
        }
        
        function showJSCustom() {
            FlashJS.fire({
                icon: 'info',
                title: 'Custom Alert',
                text: 'This alert has custom styling and features',
                confirmButtonText: 'Cool!',
                timer: 5000,
                showCloseButton: true
            });
        }
        
        // Callback functions for PHP confirmations
        function deleteUser() {
            FlashJS.fire({
                icon: 'success',
                title: 'Deleted!',
                text: 'User account has been deleted.',
                timer: 3000
            });
        }
        
        function cancelDelete() {
            FlashJS.fire({
                icon: 'info',
                title: 'Cancelled',
                text: 'Deletion was cancelled.',
                timer: 2000
            });
        }
        
        function saveChanges() {
            FlashJS.fire({
                icon: 'success',
                title: 'Saved!',
                text: 'Changes have been saved.',
                timer: 3000
            });
        }
        
        function discardChanges() {
            FlashJS.fire({
                icon: 'warning',
                title: 'Discarded',
                text: 'Changes were discarded.',
                timer: 2000
            });
        }
        
        function handleInput(value) {
            FlashJS.fire({
                icon: 'success',
                title: 'Hello!',
                text: `Nice to meet you, ${value}!`,
                timer: 3000
            });
        }
        
        function cancelInput() {
            FlashJS.fire({
                icon: 'info',
                title: 'Cancelled',
                text: 'Input was cancelled.',
                timer: 2000
            });
        }
    </script>

</body>
</html>
